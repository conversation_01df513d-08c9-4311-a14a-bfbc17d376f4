/**
 * Integration test for the improved agreement generator
 * 
 * This script tests the improved agreement generator with a real project
 * to ensure it properly handles customization in the actual application.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

// Import the agreement generator
import { generateAgreement, regenerateAgreement } from '../src/utils/agreement/index.js';

// Test data for different project types
const testProjects = [
  {
    name: "Dream Game",
    title: "Dream Game",
    description: "a collaborative game development project",
    project_type: "game",
    projectType: "game",
    is_public: true,
    platforms: "PC (Windows, macOS)",
    engine: "Unreal Engine 5",
    id: "game-test-1"
  },
  {
    name: "Dream Software",
    title: "Dream Software",
    description: "a collaborative software development project",
    project_type: "software",
    projectType: "software",
    is_public: true,
    platforms: "Web, Desktop (Windows, macOS)",
    engine: "React, Node.js",
    id: "software-test-1"
  },
  {
    name: "Dream Music",
    title: "Dream Music",
    description: "a collaborative music production project",
    project_type: "music",
    projectType: "music",
    is_public: true,
    platforms: "Digital streaming platforms",
    engine: "Pro Tools, Logic Pro",
    id: "music-test-1"
  }
];

// Test options
const testOptions = {
  contributors: [
    {
      permission_level: 'Owner',
      display_name: 'Gynell Journigan',
      company_name: 'Gynell Journigan LLC',
      address: '123 Business St, Suite 100, Wilmington, DE 19801',
      state: 'Delaware',
      city: 'Wilmington',
      county: 'New Castle County',
      email: '<EMAIL>',
      title: 'President'
    }
  ],
  currentUser: {
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Gynell Journigan'
    }
  },
  fullName: 'Gynell Journigan'
};

// Run tests for each project type
console.log('🚀 Testing agreement generator integration...');

async function runTests() {
  for (const project of testProjects) {
    try {
      console.log(`Generating agreement for: ${project.name} (${project.project_type})`);
      
      // Generate the agreement
      const agreement = await generateAgreement(project, testOptions);
      
      // Save the agreement to a file
      const outputPath = path.join(outputDir, `integration-${project.project_type}-agreement.md`);
      fs.writeFileSync(outputPath, agreement, 'utf8');
      
      console.log(`✅ Agreement saved to: ${outputPath}`);
      
      // Check for project-specific content
      const projectTypeCheck = checkProjectTypeContent(agreement, project.project_type);
      console.log(`Project type check: ${projectTypeCheck ? '✅ Passed' : '❌ Failed'}`);
      
      // Check for company information
      const companyCheck = checkCompanyInfo(agreement);
      console.log(`Company info check: ${companyCheck ? '✅ Passed' : '❌ Failed'}`);
      
      // Check for date formatting
      const dateCheck = checkDateFormatting(agreement);
      console.log(`Date formatting check: ${dateCheck ? '✅ Passed' : '❌ Failed'}`);
      
      // Test regeneration
      console.log(`Testing regeneration for: ${project.name}`);
      const regeneratedAgreement = await regenerateAgreement(project, testOptions);
      const regenerationPath = path.join(outputDir, `regenerated-${project.project_type}-agreement.md`);
      fs.writeFileSync(regenerationPath, regeneratedAgreement, 'utf8');
      console.log(`✅ Regenerated agreement saved to: ${regenerationPath}`);
      
      console.log('-----------------------------------');
    } catch (error) {
      console.error(`❌ Error generating agreement for ${project.name}:`, error);
    }
  }
}

/**
 * Check if the agreement contains appropriate content for the project type
 * @param {string} agreement - The generated agreement
 * @param {string} projectType - The project type
 * @returns {boolean} - Whether the check passed
 */
function checkProjectTypeContent(agreement, projectType) {
  // Check for project type specific content
  switch (projectType) {
    case 'game':
      return agreement.includes('game development') || 
             agreement.includes('Game development') || 
             agreement.includes('Core Gameplay');
    case 'software':
      return agreement.includes('software development') || 
             agreement.includes('Software development') || 
             agreement.includes('Core Functionality');
    case 'music':
      return agreement.includes('music production') || 
             agreement.includes('Music production') || 
             agreement.includes('Composition');
    default:
      return false;
  }
}

/**
 * Check if the agreement contains proper company information
 * @param {string} agreement - The generated agreement
 * @returns {boolean} - Whether the check passed
 */
function checkCompanyInfo(agreement) {
  return agreement.includes('Gynell Journigan LLC') && 
         agreement.includes('Delaware corporation') &&
         agreement.includes('New Castle County, Delaware');
}

/**
 * Check if the agreement has proper date formatting
 * @param {string} agreement - The generated agreement
 * @returns {boolean} - Whether the check passed
 */
function checkDateFormatting(agreement) {
  // Get today's date in the format used in the agreement
  const today = new Date();
  const formattedDate = today.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
  
  return agreement.includes(formattedDate);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});
