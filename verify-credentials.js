#!/usr/bin/env node

/**
 * Credential Verification Script
 * 
 * Quickly verifies that the test credentials work on both localhost and production
 */

const { chromium } = require('playwright');

// Test configuration
const LOCALHOST_URL = 'http://localhost:5173';
const PRODUCTION_URL = 'https://royalty.technology';

// Test credentials
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

console.log('🔐 Verifying Test Credentials');
console.log('='.repeat(40));
console.log(`Email: ${TEST_CREDENTIALS.email}`);
console.log(`Password: ${TEST_CREDENTIALS.password}`);
console.log('');

async function testCredentialsOnUrl(url) {
  console.log(`🌐 Testing credentials on: ${url}`);
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Navigate to the site
    console.log('   📍 Navigating to site...');
    await page.goto(url, { waitUntil: 'networkidle', timeout: 30000 });
    
    // Check if login form is present
    const emailInput = page.locator('input[type="email"]');
    const passwordInput = page.locator('input[type="password"]');
    const submitButton = page.locator('button[type="submit"]');
    
    const needsLogin = await emailInput.isVisible();
    
    if (!needsLogin) {
      console.log('   ✅ Already authenticated or no login required');
      await browser.close();
      return { success: true, message: 'Already authenticated' };
    }
    
    console.log('   🔑 Login form detected, attempting authentication...');
    
    // Fill in credentials
    await emailInput.fill(TEST_CREDENTIALS.email);
    await passwordInput.fill(TEST_CREDENTIALS.password);
    
    // Submit form
    await submitButton.click();
    
    // Wait for navigation or error
    await page.waitForLoadState('networkidle', { timeout: 15000 });
    
    // Check if still on login page (authentication failed)
    const stillNeedsLogin = await emailInput.isVisible();
    
    if (stillNeedsLogin) {
      // Check for error messages
      const errorMessage = await page.locator('.error, .alert-danger, [role="alert"]').textContent().catch(() => '');
      console.log('   ❌ Authentication failed');
      if (errorMessage) {
        console.log(`   📝 Error: ${errorMessage}`);
      }
      await browser.close();
      return { success: false, message: errorMessage || 'Authentication failed' };
    }
    
    // Check current URL and page content
    const currentUrl = page.url();
    const pageTitle = await page.title();
    
    console.log('   ✅ Authentication successful!');
    console.log(`   📍 Current URL: ${currentUrl}`);
    console.log(`   📄 Page Title: ${pageTitle}`);
    
    // Check for grid tiles
    const gridTiles = await page.locator('[data-canvas-card]').count();
    if (gridTiles > 0) {
      console.log(`   🎯 Found ${gridTiles} grid tiles`);
    }
    
    await browser.close();
    return { 
      success: true, 
      message: 'Authentication successful',
      url: currentUrl,
      title: pageTitle,
      gridTiles: gridTiles
    };
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    await browser.close();
    return { success: false, message: error.message };
  }
}

async function main() {
  const results = {};
  
  // Test localhost
  console.log('🏠 Testing Localhost');
  console.log('-'.repeat(20));
  try {
    results.localhost = await testCredentialsOnUrl(LOCALHOST_URL);
  } catch (error) {
    results.localhost = { success: false, message: `Failed to test localhost: ${error.message}` };
  }
  
  console.log('');
  
  // Test production
  console.log('🌐 Testing Production');
  console.log('-'.repeat(20));
  try {
    results.production = await testCredentialsOnUrl(PRODUCTION_URL);
  } catch (error) {
    results.production = { success: false, message: `Failed to test production: ${error.message}` };
  }
  
  console.log('');
  console.log('📊 SUMMARY');
  console.log('='.repeat(40));
  
  if (results.localhost.success) {
    console.log('✅ Localhost: Credentials work');
  } else {
    console.log(`❌ Localhost: ${results.localhost.message}`);
  }
  
  if (results.production.success) {
    console.log('✅ Production: Credentials work');
  } else {
    console.log(`❌ Production: ${results.production.message}`);
  }
  
  console.log('');
  
  if (results.localhost.success || results.production.success) {
    console.log('🎉 Test credentials are working!');
    
    // Recommend which environment to use for testing
    if (results.localhost.success) {
      console.log('💡 Recommendation: Use localhost for testing (faster)');
    } else if (results.production.success) {
      console.log('💡 Recommendation: Use production for testing (localhost unavailable)');
    }
    
    console.log('');
    console.log('🚀 Ready to run comprehensive grid tests:');
    console.log('   node run-grid-tests.js');
    
  } else {
    console.log('❌ Test credentials are not working on either environment');
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Check if the credentials are correct');
    console.log('   2. Verify the user account exists in the system');
    console.log('   3. Check if the authentication system is working');
    console.log('   4. Try creating a new test account');
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('');
  console.log('Usage: node verify-credentials.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --localhost    Test only localhost');
  console.log('  --production   Test only production');
  console.log('');
  console.log('Examples:');
  console.log('  node verify-credentials.js');
  console.log('  node verify-credentials.js --localhost');
  console.log('  node verify-credentials.js --production');
  console.log('');
  process.exit(0);
}

// Run main function
main().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
