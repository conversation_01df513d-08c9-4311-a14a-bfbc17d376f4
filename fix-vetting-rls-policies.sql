-- Fix missing RLS policies for vetting system tables
-- Run this in Supabase Dashboard > SQL Editor

-- Enable RLS on vetting_criteria table (if not already enabled)
ALTER TABLE vetting_criteria ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for vetting_criteria table
-- Anyone can view active vetting criteria (they are public configuration)
CREATE POLICY "Anyone can view active vetting criteria" 
ON vetting_criteria 
FOR SELECT 
TO authenticated 
USING (is_active = true);

-- Only admins can modify vetting criteria (for now, allow authenticated users to read all)
CREATE POLICY "Authenticated users can view all vetting criteria" 
ON vetting_criteria 
FOR SELECT 
TO authenticated 
USING (true);

-- Add RLS policies for reviewer_assignments table
-- Reviewers can view their own assignments
CREATE POLICY "Reviewers can view their own assignments" 
ON reviewer_assignments 
FOR SELECT 
TO authenticated 
USING (reviewer_id = auth.uid());

-- Users can view assignments for their own applications
CREATE POLICY "Users can view assignments for their applications" 
ON reviewer_assignments 
FOR SELECT 
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM vetting_applications 
    WHERE id = reviewer_assignments.application_id 
    AND user_id = auth.uid()
  )
);

-- Admins/system can create assignments (for now, allow any authenticated user to read)
CREATE POLICY "System can manage reviewer assignments" 
ON reviewer_assignments 
FOR ALL 
TO authenticated 
USING (true) 
WITH CHECK (true);

-- Add missing RLS policies for vetting_reviews table
-- Reviewers can view and manage their own reviews
CREATE POLICY "Reviewers can manage their own reviews" 
ON vetting_reviews 
FOR ALL 
TO authenticated 
USING (reviewer_id = auth.uid()) 
WITH CHECK (reviewer_id = auth.uid());

-- Users can view reviews of their own applications
CREATE POLICY "Users can view reviews of their applications" 
ON vetting_reviews 
FOR SELECT 
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM vetting_applications 
    WHERE id = vetting_reviews.application_id 
    AND user_id = auth.uid()
  )
);

-- Add missing RLS policies for skill_assessments table
-- Users can view and manage their own skill assessments
CREATE POLICY "Users can manage their own skill assessments" 
ON skill_assessments 
FOR ALL 
TO authenticated 
USING (user_id = auth.uid()) 
WITH CHECK (user_id = auth.uid());

-- Users can view public skill assessments
CREATE POLICY "Users can view public skill assessments" 
ON skill_assessments 
FOR SELECT 
TO authenticated 
USING (verification_level IN ('verified', 'expert_verified'));

-- Add missing RLS policies for vetting_workflow_logs table
-- Users can view logs for their own applications
CREATE POLICY "Users can view their application logs" 
ON vetting_workflow_logs 
FOR SELECT 
TO authenticated 
USING (user_id = auth.uid());

-- System can create workflow logs
CREATE POLICY "System can create workflow logs" 
ON vetting_workflow_logs 
FOR INSERT 
TO authenticated 
WITH CHECK (true);

-- Refresh the schema cache to ensure policies take effect
NOTIFY pgrst, 'reload schema';
