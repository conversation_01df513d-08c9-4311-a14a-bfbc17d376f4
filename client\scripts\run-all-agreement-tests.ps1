# Run all agreement tests
Write-Host "Running all agreement tests..." -ForegroundColor Cyan

# Run standard agreement tests
Write-Host "Step 1: Running standard agreement tests..." -ForegroundColor Yellow
node scripts/run-agreement-tests.mjs
Write-Host ""

# Run minimal agreement tests
Write-Host "Step 2: Running minimal agreement tests..." -ForegroundColor Yellow
node scripts/test-minimal-agreement.mjs
Write-Host ""

# Run reference agreement test
Write-Host "Step 3: Running reference agreement test..." -ForegroundColor Yellow
node scripts/test-reference-agreement.mjs
Write-Host ""

# Verify reference agreement
Write-Host "Step 4: Verifying reference agreement..." -ForegroundColor Yellow
node scripts/verify-reference-agreement.mjs
Write-Host ""

# Verify all agreements
Write-Host "Step 5: Verifying all agreements..." -ForegroundColor Yellow
node scripts/verify-agreements.mjs
Write-Host ""

Write-Host "All tests completed. Check the test-output/agreements directory for results." -ForegroundColor Green
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoE<PERSON>,IncludeKeyDown")
