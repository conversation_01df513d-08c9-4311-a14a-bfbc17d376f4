<svg viewBox="0 0 1200 1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F5F0E8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E8DDD4;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A8C090;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#8FA876;stop-opacity:0.3" />
    </linearGradient>
    
    <linearGradient id="vineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#A8C090;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A8C090;stop-opacity:0.1" />
    </linearGradient>
    
    <!-- Filters -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#A8C090" flood-opacity="0.15"/>
    </filter>
    
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="growPulse" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with subtle texture -->
  <rect width="1200" height="1800" fill="#F5F0E8"/>
  
  <!-- Ambient vine decorations in corners -->
  <path d="M 0 0 Q 20 10 40 0 Q 60 15 80 5 Q 100 20 120 10" stroke="url(#vineGradient)" stroke-width="2" fill="none" opacity="0.3"/>
  <path d="M 1200 0 Q 1180 10 1160 0 Q 1140 15 1120 5 Q 1100 20 1080 10" stroke="url(#vineGradient)" stroke-width="2" fill="none" opacity="0.3"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1200" height="100" fill="#A8C090" opacity="0.1"/>
  <text x="600" y="55" font-family="serif" font-size="36" font-weight="300" text-anchor="middle" fill="#5D6B47">Royaltea Botanical Interface System</text>
  
  <!-- Ambient Interaction System -->
  <g transform="translate(50, 120)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Ambient Interaction System</text>
    
    <!-- Cursor States -->
    <text x="0" y="55" font-family="sans-serif" font-size="16" fill="#666">Cursor Transformations</text>
    
    <!-- Default Cursor with Seed -->
    <g transform="translate(0, 75)">
      <circle cx="15" cy="15" r="12" fill="none" stroke="#A8C090" stroke-width="1" opacity="0.3" stroke-dasharray="2,2"/>
      <circle cx="15" cy="15" r="3" fill="#8B4513"/>
      <circle cx="13" cy="13" r="1" fill="#A0522D"/>
      <text x="15" y="40" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Default Cursor</text>
      <text x="15" y="52" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Seed ready to plant</text>
    </g>
    
    <!-- Hover State -->
    <g transform="translate(120, 75)">
      <circle cx="15" cy="15" r="12" fill="none" stroke="#A8C090" stroke-width="2" opacity="0.6">
        <animate attributeName="r" values="12;16;12" dur="1.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="15" cy="15" r="3" fill="#8B4513">
        <animate attributeName="fill" values="#8B4513;#A8C090;#8B4513" dur="1.5s" repeatCount="indefinite"/>
      </circle>
      <text x="15" y="40" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Hover State</text>
      <text x="15" y="52" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Seed pulses, ready to grow</text>
    </g>
    
    <!-- Click/Drop Animation -->
    <g transform="translate(240, 75)">
      <circle cx="15" cy="8" r="2" fill="#8B4513" opacity="0.8">
        <animateTransform attributeName="transform" type="translate" values="0,0; 0,7; 0,7" dur="0.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="0.5s" repeatCount="indefinite"/>
      </circle>
      <!-- Sprout emerging -->
      <path d="M 13,20 Q 15,15 17,20" stroke="#A8C090" stroke-width="2" fill="none" opacity="0">
        <animate attributeName="opacity" values="0;1;1" dur="0.5s" begin="0.5s" repeatCount="indefinite"/>
      </path>
      <circle cx="15" cy="20" r="1" fill="#A8C090" opacity="0">
        <animate attributeName="opacity" values="0;1;1" dur="0.5s" begin="0.5s" repeatCount="indefinite"/>
      </circle>
      <text x="15" y="40" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Click Animation</text>
      <text x="15" y="52" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Seed drops, sprout grows</text>
    </g>
    
    <!-- Page Reverberation Effect -->
    <g transform="translate(360, 75)">
      <circle cx="15" cy="15" r="5" fill="#A8C090" opacity="0.1">
        <animate attributeName="r" values="5;25;50" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;0.1;0" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="15" cy="15" r="2" fill="#A8C090"/>
      <text x="15" y="40" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Page Reverberation</text>
      <text x="15" y="52" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Ripple effect spreads</text>
    </g>
  </g>
  
  <!-- Botanical UI Elements -->
  <g transform="translate(50, 300)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Botanical UI Elements</text>
    
    <!-- Button with Growing Accents -->
    <text x="0" y="55" font-family="sans-serif" font-size="16" fill="#666">Interactive Elements</text>
    
    <!-- Default Button -->
    <g transform="translate(0, 75)">
      <rect x="0" y="0" width="140" height="40" rx="20" fill="#A8C090" filter="url(#dropShadow)"/>
      <text x="70" y="25" font-family="sans-serif" font-size="14" font-weight="600" text-anchor="middle" fill="#FFF">Start Project</text>
      <!-- Subtle leaf accent -->
      <path d="M 120,8 Q 125,5 130,8 Q 127,12 125,15 Q 123,12 120,8" fill="#8FA876" opacity="0.6"/>
      <text x="70" y="55" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Default State</text>
    </g>
    
    <!-- Hover Button with Growth -->
    <g transform="translate(160, 75)">
      <rect x="0" y="0" width="140" height="40" rx="20" fill="#A8C090" filter="url(#softGlow)"/>
      <text x="70" y="25" font-family="sans-serif" font-size="14" font-weight="600" text-anchor="middle" fill="#FFF">Start Project</text>
      <!-- Growing vine accents -->
      <path d="M 120,8 Q 125,5 130,8 Q 127,12 125,15 Q 123,12 120,8" fill="#8FA876">
        <animateTransform attributeName="transform" type="scale" values="1;1.2;1" dur="2s" repeatCount="indefinite"/>
      </path>
      <path d="M 132,10 Q 137,7 142,10 Q 139,14 137,17 Q 135,14 132,10" fill="#8FA876" opacity="0">
        <animate attributeName="opacity" values="0;0.8;0" dur="2s" repeatCount="indefinite"/>
      </path>
      <text x="70" y="55" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Hover - Growth</text>
    </g>
    
    <!-- Card with Organic Borders -->
    <g transform="translate(0, 130)">
      <rect x="0" y="0" width="200" height="120" rx="12" fill="url(#cardGradient)" filter="url(#dropShadow)"/>
      
      <!-- Organic border elements -->
      <path d="M 12,0 Q 20,5 28,0" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      <path d="M 200,12 Q 195,20 200,28" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      <path d="M 188,120 Q 180,115 172,120" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      <path d="M 0,108 Q 5,100 0,92" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      
      <text x="100" y="25" font-family="serif" font-size="16" font-weight="600" text-anchor="middle" fill="#5D6B47">Project Status</text>
      <circle cx="100" cy="50" r="15" fill="#A8C090" opacity="0.3"/>
      <circle cx="100" cy="50" r="8" fill="#A8C090"/>
      <text x="100" y="75" font-family="sans-serif" font-size="12" text-anchor="middle" fill="#666">Growing: 75%</text>
      <text x="100" y="90" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">2 contributors active</text>
      
      <!-- Small sprouting element -->
      <path d="M 170,95 Q 175,90 180,95" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.5"/>
      <circle cx="175" cy="95" r="1" fill="#A8C090" opacity="0.5"/>
    </g>
  </g>
  
  <!-- Passive Growth System -->
  <g transform="translate(600, 300)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Passive Growth System</text>
    
    <!-- Growth Stages -->
    <text x="0" y="55" font-family="sans-serif" font-size="16" fill="#666">Natural Progression</text>
    
    <!-- Stage 1: Seed -->
    <g transform="translate(0, 75)">
      <circle cx="30" cy="30" r="20" fill="none" stroke="#E8DDD4" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="30" cy="30" r="3" fill="#8B4513"/>
      <text x="30" y="65" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Stage 1: Seed</text>
      <text x="30" y="77" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">New user action</text>
    </g>
    
    <!-- Stage 2: Sprout -->
    <g transform="translate(100, 75)">
      <circle cx="30" cy="30" r="20" fill="none" stroke="#E8DDD4" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="30" cy="35" r="2" fill="#8B4513"/>
      <path d="M 28,33 Q 30,25 32,33" stroke="#A8C090" stroke-width="2" fill="none"/>
      <text x="30" y="65" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Stage 2: Sprout</text>
      <text x="30" y="77" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">First interaction</text>
    </g>
    
    <!-- Stage 3: Growing -->
    <g transform="translate(200, 75)">
      <circle cx="30" cy="30" r="20" fill="none" stroke="#E8DDD4" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="30" cy="38" r="2" fill="#8B4513"/>
      <path d="M 28,36 Q 30,20 32,36" stroke="#A8C090" stroke-width="2" fill="none"/>
      <path d="M 25,28 Q 20,25 25,22" stroke="#A8C090" stroke-width="1" fill="none"/>
      <path d="M 35,28 Q 40,25 35,22" stroke="#A8C090" stroke-width="1" fill="none"/>
      <text x="30" y="65" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Stage 3: Growing</text>
      <text x="30" y="77" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Regular usage</text>
    </g>
    
    <!-- Stage 4: Flourishing -->
    <g transform="translate(300, 75)">
      <circle cx="30" cy="30" r="20" fill="none" stroke="#E8DDD4" stroke-width="2" stroke-dasharray="5,5"/>
      <circle cx="30" cy="40" r="2" fill="#8B4513"/>
      <path d="M 28,38 Q 30,15 32,38" stroke="#A8C090" stroke-width="3" fill="none"/>
      <path d="M 22,25 Q 15,20 20,15" stroke="#A8C090" stroke-width="2" fill="none"/>
      <path d="M 38,25 Q 45,20 40,15" stroke="#A8C090" stroke-width="2" fill="none"/>
      <path d="M 25,18 Q 20,15 25,12" stroke="#A8C090" stroke-width="1" fill="none"/>
      <path d="M 35,18 Q 40,15 35,12" stroke="#A8C090" stroke-width="1" fill="none"/>
      <!-- Small leaves -->
      <path d="M 18,20 Q 15,17 18,14 Q 21,17 18,20" fill="url(#leafGradient)"/>
      <path d="M 42,20 Q 45,17 42,14 Q 39,17 42,20" fill="url(#leafGradient)"/>
      <text x="30" y="65" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Stage 4: Flourishing</text>
      <text x="30" y="77" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Power user</text>
    </g>
  </g>
  
  <!-- Minmax Interaction Layer -->
  <g transform="translate(50, 500)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Optional Cultivation Layer</text>
    
    <text x="0" y="55" font-family="sans-serif" font-size="16" fill="#666">Advanced Interaction (Optional)</text>
    
    <!-- Garden Customization Panel -->
    <rect x="0" y="75" width="400" height="200" rx="12" fill="url(#cardGradient)" filter="url(#dropShadow)" opacity="0.9"/>
    
    <!-- Header -->
    <text x="200" y="100" font-family="serif" font-size="18" font-weight="600" text-anchor="middle" fill="#5D6B47">Garden Cultivation</text>
    
    <!-- Cultivation Options -->
    <g transform="translate(20, 120)">
      <!-- Seed Selection -->
      <text x="0" y="15" font-family="sans-serif" font-size="12" fill="#666">Choose Seeds:</text>
      <circle cx="100" cy="10" r="6" fill="#8B4513" opacity="0.6"/>
      <circle cx="120" cy="10" r="6" fill="#A0522D" opacity="0.6"/>
      <circle cx="140" cy="10" r="6" fill="#D2691E" opacity="0.6"/>
      <text x="170" y="15" font-family="sans-serif" font-size="10" fill="#999">Click to plant different varieties</text>
      
      <!-- Growth Speed -->
      <text x="0" y="40" font-family="sans-serif" font-size="12" fill="#666">Growth Rate:</text>
      <rect x="100" y="32" width="80" height="6" rx="3" fill="#E8DDD4"/>
      <rect x="100" y="32" width="50" height="6" rx="3" fill="#A8C090"/>
      <text x="190" y="40" font-family="sans-serif" font-size="10" fill="#999">Based on platform usage</text>
      
      <!-- Seasonal Themes -->
      <text x="0" y="65" font-family="sans-serif" font-size="12" fill="#666">Seasonal Theme:</text>
      <rect x="100" y="52" width="20" height="15" rx="3" fill="#C8E6C9" opacity="0.7"/>
      <rect x="125" y="52" width="20" height="15" rx="3" fill="#FFE0B2" opacity="0.7"/>
      <rect x="150" y="52" width="20" height="15" rx="3" fill="#FFCCBC" opacity="0.7"/>
      <rect x="175" y="52" width="20" height="15" rx="3" fill="#E1F5FE" opacity="0.7"/>
      
      <!-- Auto-Cultivation Toggle -->
      <text x="0" y="90" font-family="sans-serif" font-size="12" fill="#666">Auto-Cultivation:</text>
      <rect x="110" y="82" width="30" height="15" rx="7" fill="#A8C090"/>
      <circle cx="130" cy="89" r="6" fill="#FFF"/>
      <text x="150" y="92" font-family="sans-serif" font-size="10" fill="#999">Grows with site usage</text>
    </g>
    
    <!-- Cultivation Stats -->
    <g transform="translate(250, 120)">
      <text x="0" y="15" font-family="sans-serif" font-size="12" font-weight="600" fill="#5D6B47">Your Garden Stats</text>
      
      <text x="0" y="35" font-family="sans-serif" font-size="10" fill="#666">Seeds Planted: 47</text>
      <text x="0" y="50" font-family="sans-serif" font-size="10" fill="#666">Projects Grown: 12</text>
      <text x="0" y="65" font-family="sans-serif" font-size="10" fill="#666">Growth Rate: 2.3x</text>
      <text x="0" y="80" font-family="sans-serif" font-size="10" fill="#666">Garden Level: 5</text>
    </g>
  </g>
  
  <!-- Subtle Animation Patterns -->
  <g transform="translate(600, 500)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Ambient Animations</text>
    
    <!-- Breathing Effect -->
    <g transform="translate(0, 50)">
      <circle cx="50" cy="50" r="25" fill="none" stroke="#A8C090" stroke-width="1" opacity="0.2">
        <animate attributeName="r" values="25;30;25" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.2;0.1;0.2" dur="4s" repeatCount="indefinite"/>
      </circle>
      <text x="50" y="85" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Gentle Breathing</text>
      <text x="50" y="97" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Always present, very subtle</text>
    </g>
    
    <!-- Seasonal Wind Effect -->
    <g transform="translate(120, 50)">
      <path d="M 20,40 Q 35,35 50,40 Q 65,45 80,40" stroke="#A8C090" stroke-width="2" fill="none" opacity="0.3">
        <animateTransform attributeName="transform" type="translate" values="0,0; 5,-2; 0,0; -3,1; 0,0" dur="8s" repeatCount="indefinite"/>
      </path>
      <path d="M 25,50 Q 40,45 55,50 Q 70,55 85,50" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.2">
        <animateTransform attributeName="transform" type="translate" values="0,0; -3,1; 0,0; 4,-1; 0,0" dur="6s" repeatCount="indefinite"/>
      </path>
      <text x="50" y="85" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Seasonal Breeze</text>
      <text x="50" y="97" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">Gentle movement</text>
    </g>
    
    <!-- Growth Pulse -->
    <g transform="translate(240, 50)">
      <circle cx="50" cy="50" r="3" fill="#A8C090">
        <animate attributeName="r" values="3;8;3" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="1;0.3;1" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="50" cy="50" r="1" fill="#8FA876"/>
      <text x="50" y="85" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#666">Growth Pulse</text>
      <text x="50" y="97" font-family="sans-serif" font-size="8" text-anchor="middle" fill="#999">On user actions</text>
    </g>
  </g>
  
  <!-- Page Layout Integration -->
  <g transform="translate(50, 750)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Page Layout Integration</text>
    
    <!-- Header with Botanical Accents -->
    <rect x="0" y="50" width="500" height="60" rx="8" fill="#F5F0E8" stroke="#E8DDD4" stroke-width="1"/>
    
    <!-- Navigation -->
    <text x="20" y="75" font-family="serif" font-size="16" font-weight="600" fill="#5D6B47">Royaltea</text>
    <text x="120" y="75" font-family="sans-serif" font-size="14" fill="#666">Start</text>
    <text x="170" y="75" font-family="sans-serif" font-size="14" fill="#666">Track</text>
    <text x="220" y="75" font-family="sans-serif" font-size="14" fill="#666">Earn</text>
    
    <!-- Subtle vine connecting nav items -->
    <path d="M 110,80 Q 145,75 180,80 Q 200,85 230,80" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.2"/>
    
    <!-- Growing accent in corner -->
    <path d="M 480,55 Q 485,50 490,55 Q 487,60 485,65 Q 483,60 480,55" fill="#A8C090" opacity="0.3"/>
    
    <!-- Content Area with Organic Elements -->
    <rect x="0" y="130" width="500" height="200" rx="8" fill="#FFF" stroke="#E8DDD4" stroke-width="1" opacity="0.9"/>
    
    <!-- Content with botanical decorations -->
    <text x="20" y="155" font-family="serif" font-size="18" font-weight="600" fill="#5D6B47">Project Dashboard</text>
    
    <!-- Subtle leaf separators -->
    <path d="M 20,165 Q 25,162 30,165 Q 27,168 25,171 Q 23,168 20,165" fill="#A8C090" opacity="0.2"/>
    <path d="M 470,165 Q 475,162 480,165 Q 477,168 475,171 Q 473,168 470,165" fill="#A8C090" opacity="0.2"/>
    
    <text x="20" y="185" font-family="sans-serif" font-size="14" fill="#666">Active Projects: 3</text>
    <text x="20" y="205" font-family="sans-serif" font-size="14" fill="#666">Pending Revenue: $1,247</text>
    <text x="20" y="225" font-family="sans-serif" font-size="14" fill="#666">Contributors: 8</text>
    
    <!-- Growing progress indicators -->
    <circle cx="450" cy="185" r="4" fill="#A8C090" opacity="0.6"/>
    <circle cx="450" cy="205" r="4" fill="#A8C090" opacity="0.8"/>
    <circle cx="450" cy="225" r="4" fill="#A8C090" opacity="0.4"/>
  </g>
  
  <!-- Responsive Adaptations -->
  <g transform="translate(50, 1000)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Responsive Botanical Elements</text>
    
    <!-- Desktop -->
    <g transform="translate(0, 50)">
      <rect x="0" y="0" width="150" height="100" rx="8" fill="#E8DDD4" filter="url(#dropShadow)"/>
      <text x="75" y="20" font-family="sans-serif" font-size="14" text-anchor="middle" fill="#666">Desktop</text>
      
      <!-- Full botanical decorations -->
      <path d="M 10,30 Q 20,25 30,30 Q 40,35 50,30" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      <path d="M 100,70 Q 110,65 120,70 Q 130,75 140,70" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      <circle cx="130" cy="35" r="2" fill="#A8C090" opacity="0.3"/>
      <circle cx="20" cy="75" r="2" fill="#A8C090" opacity="0.3"/>
      
      <text x="75" y="50" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Full decorative elements</text>
      <text x="75" y="65" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Rich animations</text>
      <text x="75" y="80" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Seed cursor system</text>
    </g>
    
    <!-- Tablet -->
    <g transform="translate(170, 50)">
      <rect x="0" y="0" width="120" height="100" rx="8" fill="#E8DDD4" filter="url(#dropShadow)"/>
      <text x="60" y="20" font-family="sans-serif" font-size="14" text-anchor="middle" fill="#666">Tablet</text>
      
      <!-- Simplified botanical decorations -->
      <path d="M 10,30 Q 20,25 30,30" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      <path d="M 90,70 Q 100,65 110,70" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.4"/>
      <circle cx="100" cy="35" r="2" fill="#A8C090" opacity="0.3"/>
      
      <text x="60" y="50" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Simplified elements</text>
      <text x="60" y="65" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Touch-optimized</text>
      <text x="60" y="80" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Tap to grow</text>
    </g>
    
    <!-- Mobile -->
    <g transform="translate(310, 50)">
      <rect x="0" y="0" width="80" height="100" rx="8" fill="#E8DDD4" filter="url(#dropShadow)"/>
      <text x="40" y="20" font-family="sans-serif" font-size="14" text-anchor="middle" fill="#666">Mobile</text>
      
      <!-- Minimal botanical hints -->
      <circle cx="15" cy="35" r="1" fill="#A8C090" opacity="0.4"/>
      <circle cx="65" cy="75" r="1" fill="#A8C090" opacity="0.4"/>
      <path d="M 30,45 Q 35,42 40,45" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.3"/>
      
      <text x="40" y="50" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Minimal hints</text>
      <text x="40" y="65" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Performance first</text>
      <text x="40" y="80" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#999">Subtle growth</text>
    </g>
  </g>
  
  <!-- Color Palette (Refined) -->
  <g transform="translate(600, 1000)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Botanical Color System</text>
    
    <!-- Primary Botanical Colors -->
    <text x="0" y="55" font-family="sans-serif" font-size="16" fill="#666">Natural Palette</text>
    
    <!-- Sage Green Family -->
    <circle cx="30" cy="85" r="15" fill="#A8C090" opacity="1"/>
    <circle cx="70" cy="85" r="15" fill="#A8C090" opacity="0.7"/>
    <circle cx="110" cy="85" r="15" fill="#A8C090" opacity="0.4"/>
    <circle cx="150" cy="85" r="15" fill="#A8C090" opacity="0.2"/>
    <text x="90" y="115" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#5D6B47">Sage Green Opacity Scale</text>
    
    <!-- Seed Brown Family -->
    <circle cx="30" cy="140" r="15" fill="#8B4513" opacity="1"/>
    <circle cx="70" cy="140" r="15" fill="#A0522D" opacity="1"/>
    <circle cx="110" cy="140" r="15" fill="#D2691E" opacity="1"/>
    <circle cx="150" cy="140" r="15" fill="#DEB887" opacity="1"/>
    <text x="90" y="170" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#5D6B47">Seed & Earth Tones</text>
    
    <!-- Background Neutrals -->
    <rect x="20" y="185" width="40" height="20" fill="#F5F0E8"/>
    <rect x="70" y="185" width="40" height="20" fill="#E8DDD4"/>
    <rect x="120" y="185" width="40" height="20" fill="#D4C4B0"/>
    <text x="90" y="220" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#5D6B47">Background Neutrals</text>
  </g>
  
  <!-- Implementation Notes -->
  <g transform="translate(50, 1250)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Implementation Philosophy</text>
    
    <rect x="0" y="40" width="1100" height="200" rx="12" fill="#F5F0E8" stroke="#A8C090" stroke-width="1" opacity="0.8"/>
    
    <text x="30" y="70" font-family="serif" font-size="18" font-weight="600" fill="#5D6B47">Invisible by Default, Discoverable by Choice</text>
    
    <text x="30" y="100" font-family="sans-serif" font-size="14" fill="#666">
      • Botanical elements are subtle enhancements, not dominant visuals
    </text>
    <text x="30" y="120" font-family="sans-serif" font-size="14" fill="#666">
      • Growth happens naturally through normal site usage
    </text>
    <text x="30" y="140" font-family="sans-serif" font-size="14" fill="#666">
      • Advanced users can access cultivation controls for deeper engagement
    </text>
    <text x="30" y="160" font-family="sans-serif" font-size="14" fill="#666">
      • All animations are performance-optimized and respectful of user preferences
    </text>
    <text x="30" y="180" font-family="sans-serif" font-size="14" fill="#666">
      • Professional functionality always takes priority over aesthetic elements
    </text>
    <text x="30" y="200" font-family="sans-serif" font-size="14" fill="#666">
      • Responsive design scales botanical complexity based on device capabilities
    </text>
    
    <!-- Small decorative vine in corner -->
    <path d="M 1050,50 Q 1060,45 1070,50 Q 1080,55 1090,50" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.3"/>
  </g>
  
  <!-- Technical Specifications -->
  <g transform="translate(50, 1500)">
    <text x="0" y="25" font-family="serif" font-size="24" font-weight="bold" fill="#5D6B47">Technical Implementation</text>
    
    <g transform="translate(0, 50)">
      <text x="0" y="0" font-family="sans-serif" font-size="16" font-weight="600" fill="#5D6B47">CSS Custom Properties</text>
      <rect x="0" y="10" width="300" height="60" fill="#2d3748" rx="4"/>
      <text x="10" y="30" font-family="monospace" font-size="10" fill="#a0aec0">--seed-brown: #8B4513;</text>
      <text x="10" y="45" font-family="monospace" font-size="10" fill="#a0aec0">--sage-green: #A8C090;</text>
      <text x="10" y="60" font-family="monospace" font-size="10" fill="#a0aec0">--growth-duration: 2s ease-in-out;</text>
    </g>
    
    <g transform="translate(350, 50)">
      <text x="0" y="0" font-family="sans-serif" font-size="16" font-weight="600" fill="#5D6B47">Animation Classes</text>
      <rect x="0" y="10" width="300" height="60" fill="#2d3748" rx="4"/>
      <text x="10" y="30" font-family="monospace" font-size="10" fill="#a0aec0">.seed-cursor { cursor: url(seed.svg); }</text>
      <text x="10" y="45" font-family="monospace" font-size="10" fill="#a0aec0">.grow-on-click { transition: all 2s; }</text>
      <text x="10" y="60" font-family="monospace" font-size="10" fill="#a0aec0">.page-reverberate { animation: ripple; }</text>
    </g>
    
    <g transform="translate(0, 140)">
      <text x="0" y="0" font-family="sans-serif" font-size="16" font-weight="600" fill="#5D6B47">Performance Considerations</text>
      <text x="0" y="25" font-family="sans-serif" font-size="12" fill="#666">• Use CSS transforms for animations (GPU acceleration)</text>
      <text x="0" y="40" font-family="sans-serif" font-size="12" fill="#666">• Respect prefers-reduced-motion for accessibility</text>
      <text x="0" y="55" font-family="sans-serif" font-size="12" fill="#666">• Lazy load botanical decorations on mobile</text>
      <text x="0" y="70" font-family="sans-serif" font-size="12" fill="#666">• Use requestAnimationFrame for smooth cursor effects</text>
    </g>
  </g>
  
  <!-- Footer -->
  <rect x="0" y="1700" width="1200" height="100" fill="url(#vineGradient)" opacity="0.1"/>
  <text x="600" y="1740" font-family="serif" font-size="18" text-anchor="middle" fill="#5D6B47">Subtle • Natural • Professional</text>
  <text x="600" y="1765" font-family="sans-serif" font-size="14" text-anchor="middle" fill="#5D6B47" opacity="0.7">Where technology grows organically 🌱</text>
  
  <!-- Ambient decorative elements -->
  <path d="M 0,1750 Q 50,1740 100,1750 Q 150,1760 200,1750" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.2"/>
  <path d="M 1000,1750 Q 1050,1740 1100,1750 Q 1150,1760 1200,1750" stroke="#A8C090" stroke-width="1" fill="none" opacity="0.2"/>
</svg>