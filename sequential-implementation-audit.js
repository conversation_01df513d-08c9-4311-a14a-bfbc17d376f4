#!/usr/bin/env node

/**
 * Sequential Implementation Audit
 * 
 * Maintains authentication throughout the entire audit by navigating sequentially
 * without creating new contexts or sessions
 */

const { chromium } = require('playwright');

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// All routes to audit in sequential order
const ROUTES_TO_AUDIT = [
  { route: '/', name: 'Dashboard' },
  { route: '/start', name: 'Start' },
  { route: '/track', name: 'Track' },
  { route: '/earn', name: 'Earn' },
  { route: '/projects', name: 'Projects/Ventures' },
  { route: '/project/wizard', name: 'Project Wizard' },
  { route: '/missions', name: 'Mission Board' },
  { route: '/validation/metrics', name: 'Validation' },
  { route: '/revenue', name: 'Revenue' },
  { route: '/analytics/contributions', name: 'Analytics' },
  { route: '/analytics/insights', name: 'AI Insights' },
  { route: '/profile', name: 'Profile' },
  { route: '/teams', name: 'Teams/Alliances' },
  { route: '/social', name: 'Social' },
  { route: '/settings', name: 'Settings' },
  { route: '/notifications', name: 'Notifications' },
  { route: '/bugs', name: 'Bug Reports' },
  { route: '/learn', name: 'Learning' },
  { route: '/help', name: 'Help Center' }
];

// Patterns that indicate placeholder/construction content
const PLACEHOLDER_PATTERNS = [
  /under construction/i,
  /section under construction/i,
  /being built/i,
  /development info/i,
  /canvas:/i,
  /section:/i,
  /component:/i,
  /placeholder/i,
  /coming soon/i,
  /not implemented/i,
  /todo/i,
  /work in progress/i,
  /🚧/,
  /⚠️.*construction/i,
  /this section is being/i,
  /feature coming soon/i,
  /feature under development/i,
  /page under construction/i
];

async function sequentialImplementationAudit() {
  console.log('🔍 SEQUENTIAL IMPLEMENTATION AUDIT');
  console.log('='.repeat(60));
  console.log('🔐 Maintaining authentication throughout entire audit...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Step 1: Authenticate ONCE at the beginning
    console.log('\n🔐 STEP 1: Initial Authentication');
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const needsAuth = await page.locator('input[type="email"]').isVisible();
    if (needsAuth) {
      console.log('   🔑 Logging in...');
      await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
      await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000); // Extra time for auth to settle
      console.log('   ✅ Authentication successful');
    } else {
      console.log('   ✅ Already authenticated');
    }
    
    // Verify authentication worked
    const authContent = await page.textContent('body');
    const isStillLogin = authContent?.includes('Welcome Back') && authContent?.includes('Sign in to continue');
    
    if (isStillLogin) {
      console.log('   ❌ Authentication failed - still showing login page');
      return;
    }
    
    console.log('   ✅ Authentication verified - proceeding with audit');
    
    // Step 2: Sequential audit of all pages
    console.log('\n📊 STEP 2: Sequential Page Audit');
    console.log('-'.repeat(60));
    
    const auditResults = [];
    
    for (let i = 0; i < ROUTES_TO_AUDIT.length; i++) {
      const { route, name } = ROUTES_TO_AUDIT[i];
      
      console.log(`\n📍 [${i + 1}/${ROUTES_TO_AUDIT.length}] Auditing: ${name} (${route})`);
      
      // Navigate to the page (maintaining same session)
      await page.goto(`${PRODUCTION_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1500); // Give content time to load
      
      // Get page content
      const bodyText = await page.textContent('body');
      const title = await page.title();
      const contentLength = bodyText?.length || 0;
      const currentUrl = page.url();
      
      // Quick auth check
      const isLoginPage = bodyText?.includes('Welcome Back') && bodyText?.includes('Sign in to continue');
      
      if (isLoginPage) {
        console.log(`   🔐 ❌ Authentication lost! Re-authenticating...`);
        
        // Re-authenticate if needed
        await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
        await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Try navigating to the page again
        await page.goto(`${PRODUCTION_URL}${route}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1500);
        
        // Re-get content
        const newBodyText = await page.textContent('body');
        const newContentLength = newBodyText?.length || 0;
        const stillLogin = newBodyText?.includes('Welcome Back') && newBodyText?.includes('Sign in to continue');
        
        if (stillLogin) {
          console.log(`   ❌ Still showing login after re-auth - skipping`);
          auditResults.push({
            route,
            name,
            status: 'AUTH_FAILED',
            contentLength: newContentLength,
            contentPreview: newBodyText?.substring(0, 200)
          });
          continue;
        } else {
          console.log(`   ✅ Re-authentication successful`);
          // Update variables with new content
          bodyText = newBodyText;
          contentLength = newContentLength;
        }
      }
      
      // Analyze content for placeholder patterns
      const placeholderMatches = [];
      for (const pattern of PLACEHOLDER_PATTERNS) {
        if (bodyText && pattern.test(bodyText)) {
          const match = bodyText.match(pattern);
          if (match) {
            placeholderMatches.push(match[0]);
          }
        }
      }
      
      const hasPlaceholder = placeholderMatches.length > 0;
      
      // Determine implementation status
      let status;
      let statusIcon;
      
      if (hasPlaceholder) {
        status = 'PLACEHOLDER_CONTENT';
        statusIcon = '🚧';
      } else if (contentLength > 1200) {
        status = 'FULLY_IMPLEMENTED';
        statusIcon = '✅';
      } else if (contentLength > 600) {
        status = 'PARTIAL_IMPLEMENTATION';
        statusIcon = '⚠️';
      } else {
        status = 'MINIMAL_CONTENT';
        statusIcon = '❓';
      }
      
      console.log(`   ${statusIcon} Status: ${status}`);
      console.log(`   📏 Content Length: ${contentLength} characters`);
      console.log(`   🌐 URL: ${currentUrl}`);
      
      if (hasPlaceholder) {
        console.log(`   🚧 Placeholder Content Found:`);
        placeholderMatches.forEach(match => console.log(`     • "${match}"`));
      }
      
      // Show content preview
      const preview = bodyText?.substring(0, 300).replace(/\s+/g, ' ').trim();
      console.log(`   📄 Content Preview: ${preview}...`);
      
      auditResults.push({
        route,
        name,
        status,
        contentLength,
        hasPlaceholder,
        placeholderMatches,
        contentPreview: preview,
        title,
        url: currentUrl
      });
      
      // Small delay between pages to be respectful
      await page.waitForTimeout(500);
    }
    
    // Step 3: Generate comprehensive summary
    console.log('\n📊 COMPREHENSIVE IMPLEMENTATION SUMMARY');
    console.log('='.repeat(60));
    
    const totalPages = auditResults.length;
    const placeholderPages = auditResults.filter(r => r.status === 'PLACEHOLDER_CONTENT');
    const fullyImplemented = auditResults.filter(r => r.status === 'FULLY_IMPLEMENTED');
    const partialImplementation = auditResults.filter(r => r.status === 'PARTIAL_IMPLEMENTATION');
    const minimalContent = auditResults.filter(r => r.status === 'MINIMAL_CONTENT');
    const authFailed = auditResults.filter(r => r.status === 'AUTH_FAILED');
    
    console.log(`📊 Total Pages Audited: ${totalPages}`);
    console.log(`✅ Fully Implemented: ${fullyImplemented.length} (${((fullyImplemented.length / totalPages) * 100).toFixed(1)}%)`);
    console.log(`🚧 Placeholder Content: ${placeholderPages.length} (${((placeholderPages.length / totalPages) * 100).toFixed(1)}%)`);
    console.log(`⚠️  Partial Implementation: ${partialImplementation.length} (${((partialImplementation.length / totalPages) * 100).toFixed(1)}%)`);
    console.log(`❓ Minimal Content: ${minimalContent.length} (${((minimalContent.length / totalPages) * 100).toFixed(1)}%)`);
    console.log(`❌ Auth Failed: ${authFailed.length}`);
    
    // Detailed breakdown
    console.log('\n🚧 PAGES WITH PLACEHOLDER CONTENT (NEED IMPLEMENTATION):');
    if (placeholderPages.length === 0) {
      console.log('   🎉 No placeholder content found!');
    } else {
      placeholderPages.forEach(page => {
        console.log(`   • ${page.name} (${page.route})`);
        if (page.placeholderMatches && page.placeholderMatches.length > 0) {
          page.placeholderMatches.forEach(match => {
            console.log(`     - "${match}"`);
          });
        }
      });
    }
    
    console.log('\n✅ FULLY IMPLEMENTED PAGES:');
    fullyImplemented.forEach(page => {
      console.log(`   • ${page.name} (${page.route}) - ${page.contentLength} chars`);
    });
    
    console.log('\n⚠️  PARTIAL IMPLEMENTATION (MAY NEED ENHANCEMENT):');
    partialImplementation.forEach(page => {
      console.log(`   • ${page.name} (${page.route}) - ${page.contentLength} chars`);
    });
    
    console.log('\n❓ MINIMAL CONTENT (NEEDS REVIEW):');
    minimalContent.forEach(page => {
      console.log(`   • ${page.name} (${page.route}) - ${page.contentLength} chars`);
    });
    
    if (authFailed.length > 0) {
      console.log('\n❌ AUTHENTICATION FAILED:');
      authFailed.forEach(page => {
        console.log(`   • ${page.name} (${page.route})`);
      });
    }
    
    // Priority recommendations
    const needsWork = placeholderPages.length + partialImplementation.length + minimalContent.length;
    
    console.log('\n🎯 PRIORITY RECOMMENDATIONS:');
    console.log(`📈 Implementation Rate: ${((fullyImplemented.length / totalPages) * 100).toFixed(1)}%`);
    console.log(`🔧 Pages Needing Work: ${needsWork}/${totalPages}`);
    
    if (placeholderPages.length > 0) {
      console.log('\n🔥 HIGH PRIORITY - Remove placeholder content:');
      placeholderPages.forEach(page => {
        console.log(`   1. ${page.name} (${page.route})`);
      });
    }
    
    if (partialImplementation.length > 0) {
      console.log('\n📈 MEDIUM PRIORITY - Enhance partial implementations:');
      partialImplementation.forEach(page => {
        console.log(`   • ${page.name} (${page.route})`);
      });
    }
    
    if (minimalContent.length > 0) {
      console.log('\n🔍 LOW PRIORITY - Review minimal content:');
      minimalContent.forEach(page => {
        console.log(`   • ${page.name} (${page.route})`);
      });
    }
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalPages,
        fullyImplemented: fullyImplemented.length,
        placeholderContent: placeholderPages.length,
        partialImplementation: partialImplementation.length,
        minimalContent: minimalContent.length,
        authFailed: authFailed.length,
        implementationRate: (fullyImplemented.length / totalPages) * 100,
        needsWork: needsWork
      },
      pages: auditResults,
      priorities: {
        highPriority: placeholderPages.map(p => ({ 
          name: p.name, 
          route: p.route, 
          placeholders: p.placeholderMatches 
        })),
        mediumPriority: partialImplementation.map(p => ({ 
          name: p.name, 
          route: p.route, 
          contentLength: p.contentLength 
        })),
        lowPriority: minimalContent.map(p => ({ 
          name: p.name, 
          route: p.route, 
          contentLength: p.contentLength 
        }))
      }
    };
    
    const fs = require('fs');
    const path = require('path');
    
    const reportsDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const reportPath = path.join(reportsDir, `sequential-implementation-audit-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 Detailed audit report saved: ${reportPath}`);
    console.log('\n🎉 Sequential implementation audit completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during sequential audit:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the sequential audit
sequentialImplementationAudit().catch(error => {
  console.error('❌ Failed to run sequential audit:', error);
  process.exit(1);
});
