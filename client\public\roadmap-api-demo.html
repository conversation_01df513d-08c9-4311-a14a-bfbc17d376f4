<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Royaltea Roadmap API Demo</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #333;
    }
    .progress-container {
      width: 100%;
      background-color: #f3f4f6;
      border-radius: 9999px;
      height: 24px;
      margin: 10px 0;
      overflow: hidden;
    }
    .progress-bar {
      height: 100%;
      background-color: #3b82f6;
      border-radius: 9999px;
      transition: width 0.5s ease-in-out;
    }
    .phase {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
    }
    .phase-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .phase-progress {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .phase-progress-container {
      width: 100px;
      height: 8px;
      background-color: #f3f4f6;
      border-radius: 9999px;
      overflow: hidden;
    }
    .phase-progress-bar {
      height: 100%;
      background-color: #3b82f6;
      border-radius: 9999px;
    }
    .completed {
      background-color: #10b981;
    }
    code {
      background-color: #f3f4f6;
      padding: 2px 4px;
      border-radius: 4px;
      font-family: monospace;
    }
    pre {
      background-color: #f3f4f6;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <h1>Royaltea Roadmap API Demo</h1>
  
  <div id="loading">Loading roadmap data...</div>
  
  <div id="error" style="display: none;" class="error">
    <p>Error loading roadmap data. Please try again later.</p>
  </div>
  
  <div id="roadmap-container" style="display: none;">
    <h2>Overall Progress</h2>
    <div class="progress-container">
      <div id="overall-progress" class="progress-bar" style="width: 0%"></div>
    </div>
    <p id="progress-text">0% complete (0 of 0 tasks)</p>
    
    <h2>Phases</h2>
    <div id="phases-container"></div>
  </div>
  
  <div id="api-info" style="margin-top: 40px;">
    <h2>API Usage</h2>
    <p>You can use the Royaltea Roadmap API to display the current development progress on your website.</p>
    
    <h3>Endpoint</h3>
    <code>GET https://royalty.technology/api/roadmap</code>
    
    <h3>Response Format</h3>
    <pre>{
  "success": true,
  "data": [...], // Full roadmap data
  "stats": {
    "totalTasks": 100,
    "completedTasks": 25,
    "progressPercentage": 25,
    "phases": [
      {
        "id": 1,
        "title": "Foundation & User Management",
        "timeframe": "2-3 weeks",
        "progress": 50
      },
      // More phases...
    ]
  }
}</pre>
    
    <h3>Example Usage</h3>
    <pre>// Fetch roadmap data
fetch('https://royalty.technology/api/roadmap')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Use data.stats to display progress
      const progress = data.stats.progressPercentage;
      document.getElementById('progress-bar').style.width = `${progress}%`;
      
      // Display phase information
      const phases = data.stats.phases;
      // ...
    }
  });</pre>
  </div>
  
  <script>
    // Function to fetch roadmap data
    async function fetchRoadmapData() {
      try {
        // In a real implementation, you would use the API endpoint
        // const response = await fetch('https://royalty.technology/api/roadmap');
        
        // For this demo, we'll use the Supabase client directly
        // Replace with your actual Supabase URL and anon key
        const supabaseUrl = 'https://lfnzfbvvvxvxvvvvvvvv.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
        
        // Load Supabase client from CDN
        const { createClient } = await import('https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/+esm');
        const supabase = createClient(supabaseUrl, supabaseAnonKey);
        
        // Get roadmap data
        const { data, error } = await supabase
          .from('roadmap')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(1);
        
        if (error) throw error;
        
        if (data && data.length > 0 && data[0].data) {
          const roadmapData = data[0].data;
          const stats = calculateStats(roadmapData);
          
          // Display roadmap data
          displayRoadmapData(roadmapData, stats);
        } else {
          throw new Error('No roadmap data found');
        }
      } catch (error) {
        console.error('Error fetching roadmap data:', error);
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
      }
    }
    
    // Function to calculate stats
    function calculateStats(phases) {
      let totalTasks = 0;
      let completedTasks = 0;
      let phaseStats = [];

      phases.forEach(phase => {
        let phaseTotalTasks = 0;
        let phaseCompletedTasks = 0;
        
        phase.sections.forEach(section => {
          phaseTotalTasks += section.tasks.length;
          phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
        });
        
        totalTasks += phaseTotalTasks;
        completedTasks += phaseCompletedTasks;
        
        phaseStats.push({
          id: phase.id,
          title: phase.title,
          timeframe: phase.timeframe,
          progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
        });
      });

      return {
        totalTasks,
        completedTasks,
        progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        phases: phaseStats
      };
    }
    
    // Function to display roadmap data
    function displayRoadmapData(roadmapData, stats) {
      // Update overall progress
      document.getElementById('overall-progress').style.width = `${stats.progressPercentage}%`;
      document.getElementById('progress-text').textContent = 
        `${stats.progressPercentage}% complete (${stats.completedTasks} of ${stats.totalTasks} tasks)`;
      
      // Add completed class if 100% complete
      if (stats.progressPercentage === 100) {
        document.getElementById('overall-progress').classList.add('completed');
      }
      
      // Display phases
      const phasesContainer = document.getElementById('phases-container');
      phasesContainer.innerHTML = '';
      
      stats.phases.forEach(phase => {
        const phaseElement = document.createElement('div');
        phaseElement.className = 'phase';
        
        const phaseHeader = document.createElement('div');
        phaseHeader.className = 'phase-header';
        
        const phaseTitle = document.createElement('h3');
        phaseTitle.textContent = `Phase ${phase.id}: ${phase.title} (${phase.timeframe})`;
        
        const phaseProgress = document.createElement('div');
        phaseProgress.className = 'phase-progress';
        
        const progressContainer = document.createElement('div');
        progressContainer.className = 'phase-progress-container';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'phase-progress-bar';
        if (phase.progress === 100) {
          progressBar.classList.add('completed');
        }
        progressBar.style.width = `${phase.progress}%`;
        
        const progressText = document.createElement('span');
        progressText.textContent = `${phase.progress}%`;
        
        progressContainer.appendChild(progressBar);
        phaseProgress.appendChild(progressContainer);
        phaseProgress.appendChild(progressText);
        
        phaseHeader.appendChild(phaseTitle);
        phaseHeader.appendChild(phaseProgress);
        
        phaseElement.appendChild(phaseHeader);
        phasesContainer.appendChild(phaseElement);
      });
      
      // Show roadmap container
      document.getElementById('loading').style.display = 'none';
      document.getElementById('roadmap-container').style.display = 'block';
    }
    
    // Fetch roadmap data on page load
    document.addEventListener('DOMContentLoaded', fetchRoadmapData);
  </script>
</body>
</html>
