import React, { useState, useEffect, useContext } from 'react';
import { Link, Navigate } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import ContributionList from '../../components/contribution/ContributionList';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import { Card, CardBody, CardHeader, Button, Chip, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { Clock, TrendingUp, Award, Plus, BarChart3, Calendar, Target } from 'lucide-react';

const ContributionsPage = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalContributions: 0,
    totalHours: 0,
    pendingValidation: 0,
    approvedContributions: 0
  });
  const [projects, setProjects] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Fetch contribution stats
        const { data: contributions, error: contributionsError } = await supabase
          .from('contributions')
          .select('hours_spent, validation_status, project_id')
          .eq('user_id', currentUser.id);

        if (contributionsError) throw contributionsError;

        // Calculate stats
        const totalContributions = contributions?.length || 0;
        const totalHours = contributions?.reduce((sum, c) => sum + (c.hours_spent || 0), 0) || 0;
        const pendingValidation = contributions?.filter(c => c.validation_status === 'pending').length || 0;
        const approvedContributions = contributions?.filter(c => c.validation_status === 'approved').length || 0;

        setStats({
          totalContributions,
          totalHours,
          pendingValidation,
          approvedContributions
        });

        // Fetch projects user has contributed to
        const projectIds = [...new Set(contributions?.map(c => c.project_id).filter(Boolean))];
        if (projectIds.length > 0) {
          const { data: projectsData, error: projectsError } = await supabase
            .from('projects')
            .select('id, name, thumbnail_url')
            .in('id', projectIds);

          if (projectsError) throw projectsError;
          setProjects(projectsData || []);
        }

      } catch (error) {
        console.error('Error fetching contributions data:', error);
        toast.error('Failed to load contributions data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentUser]);

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  if (loading) {
    return <LoadingAnimation />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-md border-0 shadow-xl ring-1 ring-black/5 dark:ring-white/10">
            <CardBody className="p-8">
              <div className="flex flex-col lg:flex-row items-start justify-between gap-8">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="p-3 rounded-2xl bg-gradient-to-br from-orange-500 to-red-500 shadow-lg">
                      <Clock className="text-white" size={28} />
                    </div>
                    <div>
                      <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
                        My Contributions
                      </h1>
                      <p className="text-lg text-slate-600 dark:text-slate-300 mt-1">
                        Track all your contributions across projects
                      </p>
                    </div>
                  </div>

                  {/* Quick Stats Summary */}
                  <div className="flex flex-wrap gap-4 text-sm text-slate-600 dark:text-slate-400">
                    <div className="flex items-center gap-2">
                      <Target size={16} className="text-blue-500" />
                      <span>{stats.totalContributions} total contributions</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock size={16} className="text-green-500" />
                      <span>{stats.totalHours} hours logged</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Award size={16} className="text-purple-500" />
                      <span>{stats.approvedContributions} approved</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    as={Link}
                    to="/projects"
                    color="primary"
                    variant="shadow"
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold"
                    startContent={<Plus size={20} />}
                  >
                    Join Project
                  </Button>
                  <Button
                    as={Link}
                    to="/analytics/contributions"
                    variant="bordered"
                    size="lg"
                    className="border-2 border-slate-300 dark:border-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 font-semibold"
                    startContent={<BarChart3 size={20} />}
                  >
                    Analytics
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Enhanced Stats Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 rounded-xl bg-white/20 backdrop-blur-sm">
                  <Target className="text-white" size={24} />
                </div>
                <div className="text-right">
                  <p className="text-blue-100 text-sm font-medium">Total</p>
                  <p className="text-3xl font-bold">{stats.totalContributions}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-blue-100">Contributions</span>
                  <span className="text-white font-medium">{stats.totalContributions}</span>
                </div>
                <Progress
                  value={Math.min((stats.totalContributions / 50) * 100, 100)}
                  className="h-2"
                  classNames={{
                    track: "bg-white/20",
                    indicator: "bg-white"
                  }}
                />
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-emerald-500 via-green-600 to-teal-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 rounded-xl bg-white/20 backdrop-blur-sm">
                  <Clock className="text-white" size={24} />
                </div>
                <div className="text-right">
                  <p className="text-emerald-100 text-sm font-medium">Hours</p>
                  <p className="text-3xl font-bold">{stats.totalHours}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-emerald-100">Time Logged</span>
                  <span className="text-white font-medium">{stats.totalHours}h</span>
                </div>
                <Progress
                  value={Math.min((stats.totalHours / 200) * 100, 100)}
                  className="h-2"
                  classNames={{
                    track: "bg-white/20",
                    indicator: "bg-white"
                  }}
                />
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-amber-500 via-orange-600 to-red-600 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 rounded-xl bg-white/20 backdrop-blur-sm">
                  <Calendar className="text-white" size={24} />
                </div>
                <div className="text-right">
                  <p className="text-amber-100 text-sm font-medium">Pending</p>
                  <p className="text-3xl font-bold">{stats.pendingValidation}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-amber-100">Awaiting Review</span>
                  <span className="text-white font-medium">{stats.pendingValidation}</span>
                </div>
                <Progress
                  value={stats.totalContributions > 0 ? (stats.pendingValidation / stats.totalContributions) * 100 : 0}
                  className="h-2"
                  classNames={{
                    track: "bg-white/20",
                    indicator: "bg-white"
                  }}
                />
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500 via-violet-600 to-indigo-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 rounded-xl bg-white/20 backdrop-blur-sm">
                  <Award className="text-white" size={24} />
                </div>
                <div className="text-right">
                  <p className="text-purple-100 text-sm font-medium">Approved</p>
                  <p className="text-3xl font-bold">{stats.approvedContributions}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-purple-100">Validated Work</span>
                  <span className="text-white font-medium">{stats.approvedContributions}</span>
                </div>
                <Progress
                  value={stats.totalContributions > 0 ? (stats.approvedContributions / stats.totalContributions) * 100 : 0}
                  className="h-2"
                  classNames={{
                    track: "bg-white/20",
                    indicator: "bg-white"
                  }}
                />
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Enhanced Projects Section */}
        {projects.length > 0 && (
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-md border-0 shadow-xl ring-1 ring-black/5 dark:ring-white/10">
              <CardHeader className="pb-4 px-8 pt-8">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-purple-500">
                      <Target className="text-white" size={20} />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Active Projects</h2>
                      <p className="text-slate-600 dark:text-slate-400">Projects you've contributed to</p>
                    </div>
                  </div>
                  <Chip color="primary" variant="flat" size="lg">
                    {projects.length} projects
                  </Chip>
                </div>
              </CardHeader>
              <CardBody className="px-8 pb-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {projects.map(project => (
                    <Card
                      key={project.id}
                      className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-700 dark:to-slate-800 hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 ring-1 ring-black/5 dark:ring-white/10"
                    >
                      <CardBody className="p-6">
                        <div className="flex items-start gap-4">
                          {project.thumbnail_url ? (
                            <img
                              src={project.thumbnail_url}
                              alt={project.name}
                              className="w-16 h-16 rounded-2xl object-cover shadow-lg ring-2 ring-white dark:ring-slate-600"
                            />
                          ) : (
                            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                              <span className="text-2xl">📁</span>
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <h3 className="font-bold text-lg text-slate-900 dark:text-white truncate mb-2">
                              {project.name}
                            </h3>
                            <div className="flex flex-col gap-2">
                              <Button
                                as={Link}
                                to={`/project/${project.id}/contributions`}
                                size="sm"
                                color="primary"
                                variant="shadow"
                                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium"
                                fullWidth
                              >
                                View Contributions
                              </Button>
                              <Button
                                as={Link}
                                to={`/project/${project.id}`}
                                size="sm"
                                variant="bordered"
                                className="border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 font-medium"
                                fullWidth
                              >
                                Project Details
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}

        {/* Enhanced Contributions List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-md border-0 shadow-xl ring-1 ring-black/5 dark:ring-white/10">
            <CardHeader className="pb-4 px-8 pt-8">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-xl bg-gradient-to-br from-green-500 to-emerald-500">
                    <Clock className="text-white" size={20} />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Contribution History</h2>
                    <p className="text-slate-600 dark:text-slate-400">Complete record of your work</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Chip
                    color="success"
                    variant="flat"
                    size="lg"
                    startContent={<Award size={16} />}
                  >
                    {stats.approvedContributions} approved
                  </Chip>
                  <Chip
                    color="primary"
                    variant="flat"
                    size="lg"
                    startContent={<Target size={16} />}
                  >
                    {stats.totalContributions} total
                  </Chip>
                </div>
              </div>
            </CardHeader>
            <CardBody className="px-8 pb-8">
              {stats.totalContributions === 0 ? (
                <div className="text-center py-16">
                  <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 flex items-center justify-center">
                    <Clock size={32} className="text-slate-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                    No contributions yet
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
                    Start contributing to projects to see your work history here. Join a project and begin tracking your contributions.
                  </p>
                  <Button
                    as={Link}
                    to="/projects"
                    color="primary"
                    variant="shadow"
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold"
                    startContent={<Plus size={20} />}
                  >
                    Find Projects to Join
                  </Button>
                </div>
              ) : (
                <div className="bg-slate-50 dark:bg-slate-900/50 rounded-2xl p-6">
                  <ContributionList
                    userId={currentUser.id}
                    limit={0} // Show all contributions
                    showActions={false}
                  />
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default ContributionsPage;
