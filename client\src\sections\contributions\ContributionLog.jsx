import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Select, SelectItem, Input } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { FileText, Filter, Calendar, Clock, CheckCircle, AlertCircle, Search } from 'lucide-react';

/**
 * Contribution Log Section
 * 
 * Displays comprehensive contribution history including:
 * - Filterable contribution list
 * - Validation status tracking
 * - Time and project breakdowns
 * - Search and sorting capabilities
 */
const ContributionLog = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [contributions, setContributions] = useState([]);
  const [filteredContributions, setFilteredContributions] = useState([]);
  const [projects, setProjects] = useState([]);
  const [filters, setFilters] = useState({
    project: 'all',
    status: 'all',
    category: 'all',
    dateRange: '30'
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [summary, setSummary] = useState({
    totalHours: 0,
    totalContributions: 0,
    approvedHours: 0,
    pendingHours: 0
  });

  // Load contributions and projects
  useEffect(() => {
    if (currentUser) {
      loadContributionsAndProjects();
    }
  }, [currentUser]);

  // Apply filters when they change
  useEffect(() => {
    applyFilters();
  }, [contributions, filters, searchTerm]);

  const loadContributionsAndProjects = async () => {
    try {
      setLoading(true);

      // Load user's contributions
      const { data: contributionData, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          hours_logged,
          description,
          task_category,
          difficulty_level,
          contribution_date,
          validation_status,
          validation_date,
          project_id,
          projects (
            id,
            name
          )
        `)
        .eq('user_id', currentUser.id)
        .order('contribution_date', { ascending: false });

      if (contributionsError) throw contributionsError;

      setContributions(contributionData || []);

      // Load user's projects for filtering
      const { data: projectData, error: projectsError } = await supabase
        .from('project_members')
        .select(`
          project_id,
          projects (
            id,
            name
          )
        `)
        .eq('user_id', currentUser.id);

      if (projectsError) throw projectsError;

      const uniqueProjects = projectData?.reduce((acc, member) => {
        if (member.projects && !acc.find(p => p.id === member.projects.id)) {
          acc.push(member.projects);
        }
        return acc;
      }, []) || [];

      setProjects(uniqueProjects);

      // Calculate summary
      calculateSummary(contributionData || []);

    } catch (error) {
      console.error('Error loading contributions:', error);
      toast.error('Failed to load contribution log');
    } finally {
      setLoading(false);
    }
  };

  const calculateSummary = (contributionData) => {
    const totalHours = contributionData.reduce((sum, c) => sum + (c.hours_logged || 0), 0);
    const totalContributions = contributionData.length;
    const approvedHours = contributionData
      .filter(c => c.validation_status === 'approved')
      .reduce((sum, c) => sum + (c.hours_logged || 0), 0);
    const pendingHours = contributionData
      .filter(c => c.validation_status === 'pending')
      .reduce((sum, c) => sum + (c.hours_logged || 0), 0);

    setSummary({
      totalHours: Math.round(totalHours * 100) / 100,
      totalContributions,
      approvedHours: Math.round(approvedHours * 100) / 100,
      pendingHours: Math.round(pendingHours * 100) / 100
    });
  };

  const applyFilters = () => {
    let filtered = [...contributions];

    // Date range filter
    if (filters.dateRange !== 'all') {
      const daysAgo = parseInt(filters.dateRange);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysAgo);
      
      filtered = filtered.filter(c => 
        new Date(c.contribution_date) >= cutoffDate
      );
    }

    // Project filter
    if (filters.project !== 'all') {
      filtered = filtered.filter(c => c.project_id === filters.project);
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(c => c.validation_status === filters.status);
    }

    // Category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(c => c.task_category === filters.category);
    }

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(c => 
        c.description?.toLowerCase().includes(searchLower) ||
        c.projects?.name?.toLowerCase().includes(searchLower)
      );
    }

    setFilteredContributions(filtered);
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'success';
      case 'rejected':
        return 'danger';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'rejected':
        return <AlertCircle size={16} className="text-red-400" />;
      case 'pending':
        return <Clock size={16} className="text-yellow-400" />;
      default:
        return <Clock size={16} className="text-gray-400" />;
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'development': return 'primary';
      case 'design': return 'secondary';
      case 'testing': return 'warning';
      case 'documentation': return 'success';
      case 'meeting': return 'default';
      default: return 'default';
    }
  };

  const getDifficultyColor = (level) => {
    switch (level) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'danger';
      default: return 'default';
    }
  };

  const formatHours = (hours) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${Math.round(hours * 10) / 10}h`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading contribution log...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center gap-3"
      >
        <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
          <FileText size={20} className="text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Contribution Log</h2>
          <p className="text-white/60">View and manage your contribution history</p>
        </div>
      </motion.div>

      {/* Summary Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Clock size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Total Hours</p>
                <p className="text-2xl font-bold text-white">{formatHours(summary.totalHours)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <FileText size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Total Contributions</p>
                <p className="text-2xl font-bold text-white">{summary.totalContributions}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <CheckCircle size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Approved Hours</p>
                <p className="text-2xl font-bold text-white">{formatHours(summary.approvedHours)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <AlertCircle size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Pending Hours</p>
                <p className="text-2xl font-bold text-white">{formatHours(summary.pendingHours)}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <div className="flex items-center gap-2">
              <Filter size={20} className="text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Filters</h3>
            </div>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <Input
                placeholder="Search contributions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<Search size={18} />}
              />

              <Select
                label="Project"
                value={filters.project}
                onChange={(e) => handleFilterChange('project', e.target.value)}
              >
                <SelectItem key="all" value="all">All Projects</SelectItem>
                {projects.map(project => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </Select>

              <Select
                label="Status"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <SelectItem key="all" value="all">All Status</SelectItem>
                <SelectItem key="approved" value="approved">Approved</SelectItem>
                <SelectItem key="pending" value="pending">Pending</SelectItem>
                <SelectItem key="rejected" value="rejected">Rejected</SelectItem>
              </Select>

              <Select
                label="Category"
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
              >
                <SelectItem key="all" value="all">All Categories</SelectItem>
                <SelectItem key="development" value="development">Development</SelectItem>
                <SelectItem key="design" value="design">Design</SelectItem>
                <SelectItem key="testing" value="testing">Testing</SelectItem>
                <SelectItem key="documentation" value="documentation">Documentation</SelectItem>
                <SelectItem key="meeting" value="meeting">Meeting</SelectItem>
              </Select>

              <Select
                label="Date Range"
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              >
                <SelectItem key="7" value="7">Last 7 days</SelectItem>
                <SelectItem key="30" value="30">Last 30 days</SelectItem>
                <SelectItem key="90" value="90">Last 90 days</SelectItem>
                <SelectItem key="all" value="all">All time</SelectItem>
              </Select>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Contributions List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold text-white">
                Contributions ({filteredContributions.length})
              </h3>
            </div>
          </CardHeader>
          <CardBody>
            {filteredContributions.length === 0 ? (
              <div className="text-center py-8">
                <FileText size={48} className="text-white/30 mx-auto mb-4" />
                <p className="text-white/60">No contributions found matching your filters</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredContributions.map((contribution, index) => (
                  <motion.div
                    key={contribution.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center justify-between p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(contribution.validation_status)}
                        <h4 className="text-white font-medium">
                          {contribution.projects?.name || 'Unknown Project'}
                        </h4>
                        <Chip 
                          size="sm" 
                          color={getCategoryColor(contribution.task_category)}
                          variant="flat"
                        >
                          {contribution.task_category}
                        </Chip>
                        <Chip 
                          size="sm" 
                          color={getDifficultyColor(contribution.difficulty_level)}
                          variant="flat"
                        >
                          {contribution.difficulty_level}
                        </Chip>
                        <Chip 
                          size="sm" 
                          color={getStatusColor(contribution.validation_status)}
                          variant="flat"
                        >
                          {contribution.validation_status || 'pending'}
                        </Chip>
                      </div>
                      <p className="text-white/70 text-sm mb-1">{contribution.description}</p>
                      <p className="text-white/50 text-xs">
                        {new Date(contribution.contribution_date).toLocaleDateString()} • 
                        {contribution.validation_date && (
                          ` Validated: ${new Date(contribution.validation_date).toLocaleDateString()}`
                        )}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-white font-bold text-lg">{formatHours(contribution.hours_logged)}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default ContributionLog;
