/**
 * Simple test script for the agreement generator
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a test project
const testProject = {
  name: "Test Game Project",
  description: "This is a test game project with medium duration",
  project_type: "game",
  estimated_duration: 6,
  company_name: "Test Company",
  contributors: [
    {
      permission_level: "Owner",
      display_name: "Test Owner",
      users: {
        display_name: "Test Owner",
        email: "<EMAIL>",
        address: "123 Test St, Test City, Test State 12345",
        state: "Test State",
        county: "Test County",
        title: "CEO"
      }
    }
  ],
  milestones: [
    {
      title: "First Milestone",
      description: "Complete initial setup",
      deadline: "2024-12-31"
    },
    {
      title: "Second Milestone",
      description: "Implement core features",
      deadline: "2025-03-31"
    }
  ]
};

// Create test options
const testOptions = {
  contributors: testProject.contributors,
  currentUser: {
    id: "test-user-id",
    email: "<EMAIL>",
    user_metadata: {
      full_name: "Test Contributor"
    }
  },
  royaltyModel: {
    model_type: "custom",
    contributor_percentage: 33,
    min_payout: 1000,
    max_payout: 100000,
    configuration: {
      tasks_weight: 30,
      hours_weight: 30,
      difficulty_weight: 40
    }
  },
  milestones: testProject.milestones,
  fullName: "Test Contributor"
};

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Generate the agreement
try {
  console.log('Generating agreement...');
  const agreement = generator.generateAgreement(templateText, testProject, testOptions);
  
  // Save the generated agreement to a file
  const outputPath = path.join(outputDir, 'test-output-simple.md');
  fs.writeFileSync(outputPath, agreement);
  
  console.log(`Generated agreement saved to ${outputPath}`);
} catch (error) {
  console.error('Error generating agreement:', error);
}
