/**
 * Unified Input Manager
 * 
 * Centralizes input handling across the navigation system to prevent conflicts
 * and provide consistent behavior across desktop and mobile devices.
 */

export const INPUT_MODES = {
  NAVIGATION: 'navigation', // For grid/overworld navigation
  CONTENT: 'content',       // For normal content interaction
  EDITING: 'editing'        // For text input and form fields
};

export const GESTURE_TYPES = {
  TAP: 'tap',
  DRAG: 'drag',
  SWIPE: 'swipe',
  PINCH: 'pinch',
  SCROLL: 'scroll'
};

class InputManager {
  constructor() {
    this.currentMode = INPUT_MODES.NAVIGATION;
    this.listeners = new Map();
    this.activeGestures = new Set();
    this.touchState = {
      startTime: 0,
      startPosition: { x: 0, y: 0 },
      currentPosition: { x: 0, y: 0 },
      isDragging: false,
      isScrolling: false
    };
    this.keyboardState = {
      activeKeys: new Set(),
      isInputFocused: false
    };
    this.preferences = {
      touchGesturesEnabled: true,
      keyboardNavigationEnabled: true,
      reducedMotion: false,
      scrollSensitivity: 1.0
    };
  }

  // Mode management
  setMode(mode) {
    if (Object.values(INPUT_MODES).includes(mode)) {
      const previousMode = this.currentMode;
      this.currentMode = mode;
      this.emit('modeChange', { previous: previousMode, current: mode });
    }
  }

  getMode() {
    return this.currentMode;
  }

  // Event listener management
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => callback(data));
    }
  }

  // Keyboard handling - DISABLED to prevent responsiveness issues
  setupKeyboardNavigation(element = document) {
    // Return a no-op cleanup function to maintain API compatibility
    return () => {};

    // const handleKeyDown = (e) => {
    //   this.keyboardState.activeKeys.add(e.key);
    //   this.keyboardState.isInputFocused = this.isInputElement(e.target);

    //   // Don't handle navigation keys when editing
    //   if (this.keyboardState.isInputFocused && this.currentMode !== INPUT_MODES.EDITING) {
    //     this.setMode(INPUT_MODES.EDITING);
    //     return;
    //   }

    //   // Handle navigation shortcuts based on current mode
    //   if (this.currentMode === INPUT_MODES.NAVIGATION) {
    //     this.handleNavigationKeys(e);
    //   } else if (this.currentMode === INPUT_MODES.CONTENT) {
    //     this.handleContentKeys(e);
    //   }
    // };

    // const handleKeyUp = (e) => {
    //   this.keyboardState.activeKeys.delete(e.key);
    //
    //   // Return to content mode when no longer in input
    //   if (this.currentMode === INPUT_MODES.EDITING && !this.isInputElement(e.target)) {
    //     this.setMode(INPUT_MODES.CONTENT);
    //   }
    // };

    // element.addEventListener('keydown', handleKeyDown);
    // element.addEventListener('keyup', handleKeyUp);

    // return () => {
    //   element.removeEventListener('keydown', handleKeyDown);
    //   element.removeEventListener('keyup', handleKeyUp);
    // };
  }

  handleNavigationKeys(e) {
    // DISABLED to prevent responsiveness issues
    return;

    // const keyActions = {
    //   'ArrowUp': () => this.emit('navigate', { direction: 'up' }),
    //   'ArrowDown': () => this.emit('navigate', { direction: 'down' }),
    //   'ArrowLeft': () => this.emit('navigate', { direction: 'left' }),
    //   'ArrowRight': () => this.emit('navigate', { direction: 'right' }),
    //   'Enter': () => this.emit('activate', { target: 'current' }),
    //   'Escape': () => this.emit('escape', {}),
    //   ' ': () => this.emit('toggle', {}),
    //   'Tab': () => this.emit('toggle', {}),
    //   'r': () => this.emit('reset', {}),
    //   'h': () => this.emit('home', {})
    // };

    // if (keyActions[e.key]) {
    //   e.preventDefault();
    //   keyActions[e.key]();
    // }
  }

  handleContentKeys(e) {
    const keyActions = {
      'Escape': () => {
        this.setMode(INPUT_MODES.NAVIGATION);
        this.emit('exitContent', {});
      },
      'Tab': (e) => {
        if (e.shiftKey) {
          this.emit('navigate', { direction: 'previous' });
        } else {
          this.emit('navigate', { direction: 'next' });
        }
      }
    };

    if (keyActions[e.key]) {
      e.preventDefault();
      keyActions[e.key](e);
    }
  }

  isInputElement(element) {
    const inputTypes = ['INPUT', 'TEXTAREA', 'SELECT'];
    return inputTypes.includes(element.tagName) || 
           element.contentEditable === 'true' ||
           element.closest('[contenteditable="true"]');
  }

  // Touch handling
  setupTouchHandling(element) {
    const handleTouchStart = (e) => {
      if (!this.preferences.touchGesturesEnabled) return;

      const touch = e.touches[0];
      this.touchState = {
        startTime: Date.now(),
        startPosition: { x: touch.clientX, y: touch.clientY },
        currentPosition: { x: touch.clientX, y: touch.clientY },
        isDragging: false,
        isScrolling: false
      };

      this.emit('touchStart', {
        position: this.touchState.startPosition,
        mode: this.currentMode
      });
    };

    const handleTouchMove = (e) => {
      if (!this.preferences.touchGesturesEnabled || !this.touchState.startTime) return;

      const touch = e.touches[0];
      this.touchState.currentPosition = { x: touch.clientX, y: touch.clientY };

      const deltaX = this.touchState.currentPosition.x - this.touchState.startPosition.x;
      const deltaY = this.touchState.currentPosition.y - this.touchState.startPosition.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // Determine gesture type
      if (distance > 10 && !this.touchState.isDragging && !this.touchState.isScrolling) {
        if (Math.abs(deltaY) > Math.abs(deltaX) && this.currentMode === INPUT_MODES.CONTENT) {
          // Vertical movement in content mode = scrolling
          this.touchState.isScrolling = true;
          return; // Allow native scrolling
        } else if (this.currentMode === INPUT_MODES.NAVIGATION) {
          // Movement in navigation mode = dragging
          this.touchState.isDragging = true;
          e.preventDefault(); // Prevent scrolling during navigation
        }
      }

      if (this.touchState.isDragging) {
        this.emit('touchDrag', {
          delta: { x: deltaX, y: deltaY },
          distance,
          mode: this.currentMode
        });
      }
    };

    const handleTouchEnd = (e) => {
      if (!this.preferences.touchGesturesEnabled || !this.touchState.startTime) return;

      const duration = Date.now() - this.touchState.startTime;
      const deltaX = this.touchState.currentPosition.x - this.touchState.startPosition.x;
      const deltaY = this.touchState.currentPosition.y - this.touchState.startPosition.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // Determine gesture type
      if (distance < 10 && duration < 300) {
        // Tap
        this.emit('tap', {
          position: this.touchState.startPosition,
          mode: this.currentMode
        });
      } else if (distance > 50 && duration < 500 && !this.touchState.isScrolling) {
        // Swipe
        const isHorizontal = Math.abs(deltaX) > Math.abs(deltaY);
        const direction = isHorizontal 
          ? (deltaX > 0 ? 'right' : 'left')
          : (deltaY > 0 ? 'down' : 'up');

        this.emit('swipe', {
          direction,
          distance,
          duration,
          mode: this.currentMode
        });
      } else if (this.touchState.isDragging) {
        // Drag end
        this.emit('dragEnd', {
          delta: { x: deltaX, y: deltaY },
          distance,
          duration,
          mode: this.currentMode
        });
      }

      // Reset touch state
      this.touchState = {
        startTime: 0,
        startPosition: { x: 0, y: 0 },
        currentPosition: { x: 0, y: 0 },
        isDragging: false,
        isScrolling: false
      };
    };

    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }

  // Scroll handling
  setupScrollHandling(element) {
    const handleScroll = (e) => {
      if (this.currentMode === INPUT_MODES.CONTENT) {
        // Allow normal scrolling in content mode
        this.emit('scroll', {
          deltaY: e.deltaY,
          mode: this.currentMode
        });
      } else if (this.currentMode === INPUT_MODES.NAVIGATION) {
        // Handle navigation scrolling (zoom)
        e.preventDefault();
        this.emit('navigationScroll', {
          deltaY: e.deltaY * this.preferences.scrollSensitivity,
          mode: this.currentMode
        });
      }
    };

    element.addEventListener('wheel', handleScroll, { passive: false });

    return () => {
      element.removeEventListener('wheel', handleScroll);
    };
  }

  // Utility methods
  updatePreferences(newPreferences) {
    this.preferences = { ...this.preferences, ...newPreferences };
  }

  destroy() {
    this.listeners.clear();
    this.activeGestures.clear();
  }
}

// Create singleton instance
const inputManager = new InputManager();

export default inputManager;

// Convenience functions
export const setupKeyboardNavigation = (element) => inputManager.setupKeyboardNavigation(element);
export const setupTouchHandling = (element) => inputManager.setupTouchHandling(element);
export const setupScrollHandling = (element) => inputManager.setupScrollHandling(element);
export const setInputMode = (mode) => inputManager.setMode(mode);
export const getInputMode = () => inputManager.getMode();
