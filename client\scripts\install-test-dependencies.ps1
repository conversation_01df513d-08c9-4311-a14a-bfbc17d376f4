# Install dependencies for agreement test scripts
Write-Host "Installing dependencies for agreement test scripts..." -ForegroundColor Cyan

# Check if npm is available
if (Get-Command npm -ErrorAction SilentlyContinue) {
    # Install diff package for comparing files
    npm install diff chalk
    
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
} else {
    Write-Host "Error: npm not found. Please install Node.js and npm." -ForegroundColor Red
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
