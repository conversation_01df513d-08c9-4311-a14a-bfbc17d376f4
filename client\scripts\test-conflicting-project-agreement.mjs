/**
 * Test Conflicting Project Agreement Generation
 *
 * This script tests the agreement generation for a project with potentially conflicting characteristics.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements/conflicting-project');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Define test data for a project with conflicting characteristics
const conflictingProjectData = {
  name: "Hybrid Game App",
  title: "Hybrid Game App",
  description: "a 2D mobile game with realistic graphics and high-fidelity visuals that also functions as a productivity app with data visualization and user management features",
  project_type: "game", // Specified as game but has software app characteristics
  is_public: true,
  // No engine, platforms, or languages specified
};

console.log('🚀 Starting Conflicting Project Agreement Test');
console.log('📁 Output directory:', outputDir);

// Simulate the agreement generation
const currentUser = {
  id: "contributor-id",
  email: "<EMAIL>",
  user_metadata: { full_name: "Jane Smith" },
  address: "123 Main St, Anytown, CA 12345"
};

const options = {
  contributors: [
    {
      id: "owner-id",
      display_name: "John Doe",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "CEO",
      address: "456 Business Ave, Anytown, CA 12345",
      state: "California",
      county: "Anytown County",
      role: "Project Owner",
      status: "active"
    },
    {
      id: "contributor-id",
      display_name: "Jane Smith",
      email: "<EMAIL>",
      permission_level: "Contributor",
      title: "Developer",
      address: "123 Main St, Anytown, CA 12345",
      role: "Developer",
      status: "active"
    }
  ],
  currentUser: currentUser,
  royaltyModel: {
    model_type: "custom",
    model_schema: "cog",
    configuration: {
      tasks_weight: 33,
      hours_weight: 33,
      difficulty_weight: 34
    },
    is_pre_expense: true,
    contributor_percentage: 50,
    min_payout: 10000,
    max_payout: 100000
  },
  milestones: [
    {
      id: "milestone-1",
      title: "First Milestone",
      description: "Complete initial setup",
      target_date: new Date("2023-06-30").toISOString(),
      deliverables: [
        "Initial setup complete"
      ]
    }
  ],
  fullName: "Jane Smith",
  // Use a fixed date for testing to ensure consistent output
  agreementDate: new Date("2025-05-17")
};

try {
  // Generate the agreement
  console.log('  ✓ Generating agreement...');
  const conflictingAgreement = generator.generateAgreement(templateText, conflictingProjectData, options);

  // Save the generated agreement
  const outputPath = path.join(outputDir, 'conflicting-project-agreement.md');
  fs.writeFileSync(outputPath, conflictingAgreement, 'utf8');
  console.log(`  ✓ Agreement saved to: ${outputPath}`);

  // Validate the agreement
  console.log('\n🔍 Validating conflicting project agreement...');
  
  // Check for engine selection
  if (conflictingAgreement.includes('Engine: Unity')) {
    console.log('  ✓ Correctly selected Unity for 2D mobile game');
  } else if (conflictingAgreement.includes('Engine: Unreal Engine')) {
    console.log('  ❌ Incorrectly selected Unreal Engine despite 2D mobile characteristics');
  } else {
    console.log('  ❓ No specific engine detected');
  }
  
  // Check for mobile platforms
  if (conflictingAgreement.includes('Mobile') || conflictingAgreement.includes('iOS') || conflictingAgreement.includes('Android')) {
    console.log('  ✓ Correctly included mobile platforms');
  } else {
    console.log('  ❌ Missing mobile platforms despite mobile game description');
  }
  
  // Check for programming languages
  if (conflictingAgreement.includes('Programming: C#')) {
    console.log('  ✓ Correctly included C# for Unity');
  } else if (conflictingAgreement.includes('Programming: C++')) {
    console.log('  ❓ Included C++ (typically for Unreal Engine)');
  } else {
    console.log('  ❓ No specific programming language detected');
  }
  
  // Check for required sections
  const requiredSections = [
    '## EXHIBIT I',
    '## EXHIBIT II',
    '## SCHEDULE A',
    '## SCHEDULE B'
  ];
  
  let missingSections = [];
  requiredSections.forEach(section => {
    if (!conflictingAgreement.includes(section)) {
      missingSections.push(section);
    }
  });
  
  if (missingSections.length === 0) {
    console.log('  ✓ All required sections are present');
  } else {
    console.log('  ❌ Missing sections:');
    missingSections.forEach(section => {
      console.log(`    - ${section}`);
    });
  }
  
  // Final validation result
  console.log('\n✅ Conflicting project agreement test completed!');
} catch (error) {
  console.error('\n❌ Error generating agreement:', error);
}
