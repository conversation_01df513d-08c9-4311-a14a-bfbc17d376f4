/**
 * Verify Reference Agreement Script
 *
 * This script compares the generated reference agreement with the original template
 * to identify any differences and verify that the generator is working correctly.
 *
 * Usage: node verify-reference-agreement.mjs
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { diffLines } from 'diff';
import chalk from 'chalk';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define paths
const referenceDir = path.join(__dirname, '../test-output/agreements/reference');
const referenceAgreementPath = path.join(referenceDir, 'reference-agreement.md');
const originalTemplatePath = path.join(referenceDir, 'original-template.md');
const diffOutputPath = path.join(referenceDir, 'agreement-diff.md');

// Check if files exist
if (!fs.existsSync(referenceAgreementPath)) {
  console.error(`❌ Reference agreement not found at: ${referenceAgreementPath}`);
  console.error('Run test-reference-agreement.mjs first to generate the reference agreement.');
  process.exit(1);
}

if (!fs.existsSync(originalTemplatePath)) {
  console.error(`❌ Original template not found at: ${originalTemplatePath}`);
  console.error('Run test-reference-agreement.mjs first to generate the original template copy.');
  process.exit(1);
}

// Read the files
const referenceAgreement = fs.readFileSync(referenceAgreementPath, 'utf8');
const originalTemplate = fs.readFileSync(originalTemplatePath, 'utf8');

// Function to normalize line endings
const normalizeLineEndings = (text) => text.replace(/\r\n/g, '\n');

// Normalize line endings
const normalizedReference = normalizeLineEndings(referenceAgreement);
const normalizedTemplate = normalizeLineEndings(originalTemplate);

// Compare the files
console.log('🔍 Comparing reference agreement with original template...');

// Generate diff
const diff = diffLines(normalizedTemplate, normalizedReference);

// Count differences
let addedLines = 0;
let removedLines = 0;
let unchangedLines = 0;

// Generate diff output
let diffOutput = '';
let consoleOutput = '';

diff.forEach((part) => {
  // Count lines
  const lines = part.value.split('\n').length - 1;
  
  if (part.added) {
    addedLines += lines;
    diffOutput += `+ ${part.value}`;
    consoleOutput += chalk.green(`+ ${part.value}`);
  } else if (part.removed) {
    removedLines += lines;
    diffOutput += `- ${part.value}`;
    consoleOutput += chalk.red(`- ${part.value}`);
  } else {
    unchangedLines += lines;
    diffOutput += `  ${part.value}`;
    consoleOutput += `  ${part.value}`;
  }
});

// Save diff output to file
fs.writeFileSync(diffOutputPath, diffOutput, 'utf8');

// Print summary
console.log('\n📊 Comparison Summary:');
console.log(`Total lines in template: ${normalizedTemplate.split('\n').length}`);
console.log(`Total lines in reference: ${normalizedReference.split('\n').length}`);
console.log(`Added lines: ${addedLines}`);
console.log(`Removed lines: ${removedLines}`);
console.log(`Unchanged lines: ${unchangedLines}`);

// Determine if the files are identical
const identical = addedLines === 0 && removedLines === 0;

if (identical) {
  console.log('\n✅ The reference agreement matches the original template exactly!');
} else {
  console.log('\n❌ The reference agreement differs from the original template.');
  console.log(`Diff saved to: ${diffOutputPath}`);
  
  // Print the first few differences
  console.log('\nFirst few differences:');
  
  // Find the first few differences
  let diffCount = 0;
  const maxDiffsToShow = 5;
  
  diff.forEach((part) => {
    if ((part.added || part.removed) && diffCount < maxDiffsToShow) {
      diffCount++;
      
      // Get the context (a few lines before and after)
      const lines = part.value.split('\n');
      const contextLines = Math.min(3, lines.length);
      
      console.log(part.added ? chalk.green('+ Added:') : chalk.red('- Removed:'));
      
      // Print the first few lines of the difference
      for (let i = 0; i < contextLines && i < lines.length; i++) {
        if (lines[i].trim()) {
          console.log(part.added ? chalk.green(`  ${lines[i]}`) : chalk.red(`  ${lines[i]}`));
        }
      }
      
      // If there are more lines, show an ellipsis
      if (lines.length > contextLines) {
        console.log('  ...');
      }
      
      console.log('');
    }
  });
}

// Analyze specific sections that might be different
console.log('\n🔍 Analyzing specific sections:');

// Check company name
const companyNameTemplate = originalTemplate.match(/City of Gamers Inc\./g);
const companyNameReference = referenceAgreement.match(/City of Gamers Inc\./g);

console.log('Company Name:');
console.log(`  Template: ${companyNameTemplate ? companyNameTemplate.length : 0} occurrences`);
console.log(`  Reference: ${companyNameReference ? companyNameReference.length : 0} occurrences`);

// Check project name
const projectNameTemplate = originalTemplate.match(/Village of The Ages/g);
const projectNameReference = referenceAgreement.match(/Village of The Ages/g);

console.log('Project Name:');
console.log(`  Template: ${projectNameTemplate ? projectNameTemplate.length : 0} occurrences`);
console.log(`  Reference: ${projectNameReference ? projectNameReference.length : 0} occurrences`);

// Check Exhibit I
const exhibitITemplate = originalTemplate.includes('## EXHIBIT I');
const exhibitIReference = referenceAgreement.includes('## EXHIBIT I');

console.log('Exhibit I:');
console.log(`  Template: ${exhibitITemplate ? 'Present' : 'Missing'}`);
console.log(`  Reference: ${exhibitIReference ? 'Present' : 'Missing'}`);

// Check Exhibit II
const exhibitIITemplate = originalTemplate.includes('## EXHIBIT II');
const exhibitIIReference = referenceAgreement.includes('## EXHIBIT II');

console.log('Exhibit II:');
console.log(`  Template: ${exhibitIITemplate ? 'Present' : 'Missing'}`);
console.log(`  Reference: ${exhibitIIReference ? 'Present' : 'Missing'}`);

console.log('\n✨ Verification completed!');
