/**
 * Test Software Project Agreement Generation
 *
 * This script tests the agreement generation for a software application project.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements/software-project');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Define test data for a software application project
const softwareProjectData = {
  name: "HealthTrack Pro",
  title: "HealthTrack Pro",
  description: "a comprehensive health tracking web application with mobile apps that allows users to monitor fitness goals, nutrition intake, and medical appointments with data visualization and AI-powered recommendations",
  project_type: "software",
  is_public: true,
  launch_date: new Date("2023-08-15").toISOString(),
  company_name: "MedTech Solutions Inc.",
  // Not explicitly setting framework to test the auto-detection
  // framework: "React",
  platforms: "Web, iOS, Android",
  estimated_duration: 6, // 6 months
  // Not explicitly setting languages to test the auto-detection
  // languages: ["JavaScript", "TypeScript"]
};

console.log('🚀 Starting Software Project Agreement Test');
console.log('📁 Output directory:', outputDir);

// Simulate the agreement generation
const currentUser = {
  id: "contributor-id",
  email: "<EMAIL>",
  user_metadata: { full_name: "Alex Johnson" },
  address: "789 Tech Blvd, Silicon Valley, CA 94043"
};

const options = {
  contributors: [
    {
      id: "owner-id",
      display_name: "Sarah Chen",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "CEO",
      address: "123 Innovation Way, San Francisco, CA 94105",
      state: "California",
      county: "San Francisco County",
      role: "Project Owner",
      status: "active"
    },
    {
      id: "contributor-id",
      display_name: "Alex Johnson",
      email: "<EMAIL>",
      permission_level: "Contributor",
      title: "Full Stack Developer",
      address: "789 Tech Blvd, Silicon Valley, CA 94043",
      role: "Developer",
      status: "active"
    }
  ],
  currentUser: currentUser,
  royaltyModel: {
    model_type: "custom",
    model_schema: "cog",
    configuration: {
      tasks_weight: 35,
      hours_weight: 35,
      difficulty_weight: 30
    },
    is_pre_expense: true,
    contributor_percentage: 40,
    min_payout: 75000,
    max_payout: 750000
  },
  milestones: [
    {
      id: "milestone-1",
      title: "MVP Development",
      description: "Core functionality and basic UI",
      target_date: new Date("2023-04-15").toISOString(),
      deliverables: [
        "User authentication system",
        "Basic health tracking features",
        "Initial database schema",
        "Responsive web interface"
      ]
    },
    {
      id: "milestone-2",
      title: "Feature Complete",
      description: "All planned features implemented",
      target_date: new Date("2023-06-30").toISOString(),
      deliverables: [
        "Data visualization dashboard",
        "Mobile app versions (iOS/Android)",
        "API integrations with health devices",
        "Notification system"
      ]
    },
    {
      id: "milestone-3",
      title: "Beta Release",
      description: "Feature complete with testing",
      target_date: new Date("2023-07-31").toISOString(),
      deliverables: [
        "User acceptance testing complete",
        "Performance optimization",
        "Security audit passed",
        "Beta program launched"
      ]
    },
    {
      id: "milestone-4",
      title: "Production Launch",
      description: "Public release",
      target_date: new Date("2023-08-15").toISOString(),
      deliverables: [
        "Production environment configured",
        "Marketing materials finalized",
        "Customer support system in place",
        "App store submissions complete"
      ]
    }
  ],
  fullName: "Alex Johnson",
  // Use a fixed date for testing to ensure consistent output
  agreementDate: new Date("2025-05-17")
};

try {
  // Generate the agreement
  console.log('  ✓ Generating agreement...');
  const softwareAgreement = generator.generateAgreement(templateText, softwareProjectData, options);

  // Save the generated agreement
  const outputPath = path.join(outputDir, 'software-project-agreement.md');
  fs.writeFileSync(outputPath, softwareAgreement, 'utf8');
  console.log(`  ✓ Agreement saved to: ${outputPath}`);

  // Validate the agreement
  console.log('\n🔍 Validating software project agreement...');
  
  // Check for software-specific content
  const softwareSpecificTerms = [
    'software application',
    'users',
    'web application',
    'mobile apps',
    'React',
    'JavaScript',
    'database'
  ];
  
  const gameSpecificTerms = [
    'game',
    'gameplay',
    'player',
    'levels',
    'Unreal Engine',
    'Unity'
  ];
  
  let softwareTermsFound = 0;
  let gameTermsFound = 0;
  
  softwareSpecificTerms.forEach(term => {
    if (softwareAgreement.includes(term)) {
      softwareTermsFound++;
      console.log(`  ✓ Found software-specific term: "${term}"`);
    }
  });
  
  gameSpecificTerms.forEach(term => {
    if (softwareAgreement.includes(term)) {
      gameTermsFound++;
      console.log(`  ❌ Found game-specific term: "${term}"`);
    }
  });
  
  // Check for required sections
  const requiredSections = [
    '## EXHIBIT I',
    '## EXHIBIT II',
    '## SCHEDULE A',
    '## SCHEDULE B'
  ];
  
  let missingSections = [];
  requiredSections.forEach(section => {
    if (!softwareAgreement.includes(section)) {
      missingSections.push(section);
    }
  });
  
  if (missingSections.length === 0) {
    console.log('  ✓ All required sections are present');
  } else {
    console.log('  ❌ Missing sections:');
    missingSections.forEach(section => {
      console.log(`    - ${section}`);
    });
  }
  
  // Final validation result
  if (softwareTermsFound >= 5 && gameTermsFound === 0 && missingSections.length === 0) {
    console.log('\n✅ Software project agreement validation passed!');
  } else {
    console.log('\n❌ Software project agreement validation failed!');
    if (softwareTermsFound < 5) {
      console.log(`  - Not enough software-specific terms found (${softwareTermsFound}/5 required)`);
    }
    if (gameTermsFound > 0) {
      console.log(`  - Found ${gameTermsFound} game-specific terms that should not be present`);
    }
    if (missingSections.length > 0) {
      console.log(`  - Missing ${missingSections.length} required sections`);
    }
  }
} catch (error) {
  console.error('\n❌ Error generating agreement:', error);
}
