#!/usr/bin/env node

/**
 * Comprehensive Grid Navigation Test Runner
 * 
 * Runs comprehensive tests for all grid tiles with authentication,
 * 404 detection, and automatic fallback to production if localhost fails.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Royaltea Comprehensive Grid Navigation Test Suite');
console.log('='.repeat(60));

// Configuration
const LOCALHOST_URL = 'http://localhost:5173';
const PRODUCTION_URL = 'https://royalty.technology';

// Test user credentials
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

console.log('🔐 Test Credentials:');
console.log(`   Email: ${TEST_CREDENTIALS.email}`);
console.log(`   Password: ${TEST_CREDENTIALS.password}`);
console.log('');

// Check if <PERSON><PERSON> is installed
function checkPlaywrightInstallation() {
  try {
    execSync('npx playwright --version', { stdio: 'pipe' });
    console.log('✅ Playwright is installed');
    return true;
  } catch (error) {
    console.log('❌ Playwright not found. Installing...');
    try {
      execSync('npm install @playwright/test', { stdio: 'inherit' });
      execSync('npx playwright install', { stdio: 'inherit' });
      console.log('✅ Playwright installed successfully');
      return true;
    } catch (installError) {
      console.error('❌ Failed to install Playwright:', installError.message);
      return false;
    }
  }
}

// Test URL accessibility
async function testUrlAccessibility(url) {
  console.log(`🔍 Testing accessibility of ${url}...`);
  
  try {
    const { chromium } = require('playwright');
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const response = await page.goto(url, { 
      waitUntil: 'networkidle', 
      timeout: 30000 
    });
    
    const isAccessible = response && response.status() < 400;
    await browser.close();
    
    if (isAccessible) {
      console.log(`✅ ${url} is accessible`);
    } else {
      console.log(`❌ ${url} returned status ${response?.status()}`);
    }
    
    return isAccessible;
  } catch (error) {
    console.log(`❌ ${url} is not accessible: ${error.message}`);
    return false;
  }
}

// Determine which URL to use for testing
async function determineTestUrl() {
  console.log('🌐 Determining test URL...');
  
  // Check if localhost is accessible
  const localhostAccessible = await testUrlAccessibility(LOCALHOST_URL);
  
  if (localhostAccessible) {
    console.log('✅ Using localhost for testing');
    return LOCALHOST_URL;
  } else {
    console.log('⚠️  Localhost not accessible, switching to production');
    const productionAccessible = await testUrlAccessibility(PRODUCTION_URL);
    
    if (productionAccessible) {
      console.log('✅ Using production for testing');
      return PRODUCTION_URL;
    } else {
      console.log('❌ Neither localhost nor production is accessible');
      throw new Error('No accessible URL found for testing');
    }
  }
}

// Run the comprehensive tests
async function runComprehensiveTests(testUrl) {
  console.log('🚀 Running comprehensive grid navigation tests...');
  console.log(`🎯 Target URL: ${testUrl}`);
  console.log('');
  
  // Set environment variable for the test
  process.env.PLAYWRIGHT_BASE_URL = testUrl;
  
  // Ensure test-results directory exists
  const testResultsDir = path.join(process.cwd(), 'test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }
  
  try {
    // Run the comprehensive test suite
    const command = [
      'npx playwright test',
      'tests/comprehensive-grid-navigation.spec.js',
      '--config=playwright-auth.config.js',
      '--reporter=html',
      '--timeout=120000' // 2 minutes per test
    ].join(' ');
    
    console.log('📋 Running command:', command);
    console.log('');
    
    execSync(command, { 
      stdio: 'inherit',
      env: { 
        ...process.env,
        PLAYWRIGHT_BASE_URL: testUrl
      }
    });
    
    console.log('');
    console.log('✅ Comprehensive tests completed successfully!');
    
  } catch (error) {
    console.log('');
    console.log('❌ Tests failed or encountered errors');
    console.log('Error details:', error.message);
    
    // Still try to generate a report if possible
    console.log('📄 Attempting to generate partial report...');
  }
}

// Generate summary report
function generateSummaryReport() {
  console.log('📊 Generating summary report...');
  
  const testResultsDir = path.join(process.cwd(), 'test-results');
  
  try {
    // Look for the latest navigation report
    const files = fs.readdirSync(testResultsDir);
    const reportFiles = files.filter(f => f.startsWith('grid-navigation-report-'));
    
    if (reportFiles.length > 0) {
      // Get the most recent report
      const latestReport = reportFiles.sort().pop();
      const reportPath = path.join(testResultsDir, latestReport);
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      
      console.log('');
      console.log('📋 COMPREHENSIVE TEST SUMMARY');
      console.log('='.repeat(40));
      console.log(`🌐 Test URL: ${report.testUrl}`);
      console.log(`🔐 Authentication: ${report.authenticationWorking ? '✅ Working' : '❌ Failed'}`);
      console.log(`📊 Total Tiles: ${report.summary.total}`);
      console.log(`✅ Successful: ${report.summary.successful}`);
      console.log(`❌ Failed: ${report.summary.failed}`);
      console.log(`🚫 404 Errors: ${report.summary.errors404}`);
      console.log(`📈 Success Rate: ${((report.summary.successful / report.summary.total) * 100).toFixed(1)}%`);
      console.log('');
      
      if (report.summary.errors404 > 0) {
        console.log('❌ TILES WITH 404 ERRORS:');
        report.tiles.filter(t => t.is404).forEach(tile => {
          console.log(`   - ${tile.name}: ${tile.url}`);
        });
        console.log('');
      }
      
      if (report.summary.successful > 0) {
        console.log('✅ WORKING TILES:');
        report.tiles.filter(t => t.success).forEach(tile => {
          console.log(`   - ${tile.name}: ${tile.url}`);
        });
        console.log('');
      }
      
      console.log(`📄 Full report: ${reportPath}`);
      
    } else {
      console.log('⚠️  No navigation reports found');
    }
    
  } catch (error) {
    console.log('❌ Failed to generate summary report:', error.message);
  }
}

// Main execution
async function main() {
  try {
    // Check Playwright installation
    if (!checkPlaywrightInstallation()) {
      process.exit(1);
    }
    
    console.log('');
    
    // Determine test URL
    const testUrl = await determineTestUrl();
    
    console.log('');
    
    // Run comprehensive tests
    await runComprehensiveTests(testUrl);
    
    console.log('');
    
    // Generate summary report
    generateSummaryReport();
    
    console.log('');
    console.log('🎉 Grid navigation testing completed!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('   1. Review the HTML report: npx playwright show-report');
    console.log('   2. Check test-results/ directory for detailed reports');
    console.log('   3. Fix any 404 errors found in the routing');
    console.log('   4. Re-run tests after fixes');
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('');
  console.log('Usage: node run-grid-tests.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --localhost    Force localhost testing');
  console.log('  --production   Force production testing');
  console.log('  --debug        Run in debug mode');
  console.log('');
  console.log('Examples:');
  console.log('  node run-grid-tests.js');
  console.log('  node run-grid-tests.js --production');
  console.log('  node run-grid-tests.js --debug');
  console.log('');
  process.exit(0);
}

// Handle forced URL selection
if (args.includes('--localhost')) {
  process.env.PLAYWRIGHT_BASE_URL = LOCALHOST_URL;
  console.log('🔧 Forced localhost testing');
}

if (args.includes('--production')) {
  process.env.PLAYWRIGHT_BASE_URL = PRODUCTION_URL;
  console.log('🔧 Forced production testing');
}

if (args.includes('--debug')) {
  process.env.PWDEBUG = '1';
  console.log('🔧 Debug mode enabled');
}

// Run the main function
main().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
