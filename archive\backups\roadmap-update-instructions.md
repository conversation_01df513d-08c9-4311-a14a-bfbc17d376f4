# Roadmap Update Instructions

## Overview

This document provides instructions for updating the roadmap in the Supabase database to reflect the recent PDF preview improvements.

## What's Being Updated

1. **Marking Completed Tasks**:
   - Task 5.3.4: "Improve PDF preview formatting" → Completed
   - Task 5.3.5: "Fix PDF download issues" → Completed

2. **Adding New Task**:
   - New task in section 5.3: "Enhance agreement customization for project-specific details"

3. **Updating Latest Feature**:
   - Title: "PDF Preview Improvements"
   - Description: "Enhanced PDF preview with left-justified text and fixed automatic download issues. PDFs now display properly and only download when explicitly requested by the user."
   - Date: Current date
   - Author: "Development Team"
   - Version: "1.0.1"

## How to Update the Roadmap

### Option 1: Using the Supabase SQL Editor

1. Log in to the Supabase dashboard
2. Navigate to the SQL Editor
3. Copy the SQL commands from the `roadmap-update-sql.sql` file
4. Paste the commands into the SQL Editor
5. Execute the commands

### Option 2: Using the Supabase JavaScript Client

If you have access to the Supabase key, you can run the `update-roadmap-database.js` script:

```bash
# Set the Supabase key as an environment variable
export SUPABASE_KEY=your_supabase_key

# Run the script
node update-roadmap-database.js
```

## Verifying the Update

After updating the roadmap, you should verify that the changes were applied correctly:

1. Check that tasks 5.3.4 and 5.3.5 are marked as completed
2. Verify that the new task for agreement customization was added
3. Confirm that the latest feature was updated in both the metadata and the top-level field

## Troubleshooting

If you encounter any issues with the SQL commands, you may need to adjust the array indices. The current commands assume:

- The Platform Enhancements phase is at index 4 in the data array
- The Document Management section is at index 2 in the sections array
- The PDF preview formatting task is at index 3 in the tasks array
- The PDF download issues task is at index 4 in the tasks array
- The metadata item is at index 6 in the data array

You can verify these indices by running a SELECT query to examine the current structure:

```sql
SELECT data
FROM roadmap
ORDER BY created_at DESC
LIMIT 1;
```
