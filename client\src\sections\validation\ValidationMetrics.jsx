import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Avatar } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { CheckCircle, Clock, AlertCircle, TrendingUp, Users, Target } from 'lucide-react';

/**
 * Validation Metrics Section
 * 
 * Displays validation and approval metrics including:
 * - Validation success rates
 * - Review turnaround times
 * - Validator performance
 * - Quality metrics
 */
const ValidationMetrics = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [metricsData, setMetricsData] = useState({
    totalValidations: 0,
    approvedValidations: 0,
    pendingValidations: 0,
    rejectedValidations: 0,
    averageReviewTime: 0,
    validationRate: 0,
    recentValidations: [],
    topValidators: [],
    qualityMetrics: {
      accuracy: 0,
      consistency: 0,
      timeliness: 0
    }
  });
  const [error, setError] = useState(null);

  // Load validation metrics data
  useEffect(() => {
    if (currentUser) {
      loadValidationMetrics();
    }
  }, [currentUser]);

  const loadValidationMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load user's contributions that have been validated
      const { data: validatedContributions, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          validation_status,
          validation_date,
          validated_by,
          created_at,
          hours_logged,
          project_id,
          projects (
            id,
            name
          )
        `)
        .eq('user_id', currentUser.id)
        .not('validation_status', 'is', null)
        .order('validation_date', { ascending: false });

      if (contributionsError) throw contributionsError;

      // Load validations performed by the user (if they are a validator)
      const { data: performedValidations, error: validationsError } = await supabase
        .from('contributions')
        .select(`
          id,
          validation_status,
          validation_date,
          user_id,
          created_at,
          hours_logged,
          users (
            id,
            display_name,
            avatar_url
          )
        `)
        .eq('validated_by', currentUser.id)
        .not('validation_status', 'is', null)
        .order('validation_date', { ascending: false });

      if (validationsError) throw validationsError;

      // Load recent validation activity
      const { data: recentActivity, error: activityError } = await supabase
        .from('contributions')
        .select(`
          id,
          validation_status,
          validation_date,
          validated_by,
          user_id,
          hours_logged,
          project_id,
          projects (
            id,
            name
          ),
          users (
            id,
            display_name,
            avatar_url
          )
        `)
        .or(`user_id.eq.${currentUser.id},validated_by.eq.${currentUser.id}`)
        .not('validation_status', 'is', null)
        .order('validation_date', { ascending: false })
        .limit(10);

      if (activityError) throw activityError;

      // Process metrics data
      const processedData = processValidationMetrics(
        validatedContributions || [],
        performedValidations || [],
        recentActivity || []
      );
      setMetricsData(processedData);

    } catch (error) {
      console.error('Error loading validation metrics:', error);
      setError(error.message);
      toast.error('Failed to load validation metrics');
    } finally {
      setLoading(false);
    }
  };

  const processValidationMetrics = (userContributions, userValidations, recentActivity) => {
    // Process user's contributions that were validated
    const totalValidations = userContributions.length;
    const approvedValidations = userContributions.filter(c => c.validation_status === 'approved').length;
    const pendingValidations = userContributions.filter(c => c.validation_status === 'pending').length;
    const rejectedValidations = userContributions.filter(c => c.validation_status === 'rejected').length;

    const validationRate = totalValidations > 0 ? Math.round((approvedValidations / totalValidations) * 100) : 0;

    // Calculate average review time
    const reviewTimes = userContributions
      .filter(c => c.validation_date && c.created_at)
      .map(c => {
        const created = new Date(c.created_at);
        const validated = new Date(c.validation_date);
        return (validated - created) / (1000 * 60 * 60); // hours
      });

    const averageReviewTime = reviewTimes.length > 0 
      ? Math.round(reviewTimes.reduce((sum, time) => sum + time, 0) / reviewTimes.length * 10) / 10
      : 0;

    // Process validator performance (if user performs validations)
    const validatorStats = {};
    userValidations.forEach(validation => {
      const userId = validation.user_id;
      if (!validatorStats[userId]) {
        validatorStats[userId] = {
          user: validation.users,
          total: 0,
          approved: 0,
          rejected: 0,
          totalHours: 0
        };
      }
      validatorStats[userId].total++;
      validatorStats[userId].totalHours += validation.hours_logged || 0;
      if (validation.validation_status === 'approved') {
        validatorStats[userId].approved++;
      } else if (validation.validation_status === 'rejected') {
        validatorStats[userId].rejected++;
      }
    });

    const topValidators = Object.values(validatorStats)
      .map(stats => ({
        ...stats,
        approvalRate: stats.total > 0 ? Math.round((stats.approved / stats.total) * 100) : 0
      }))
      .sort((a, b) => b.total - a.total)
      .slice(0, 5);

    // Process recent activity
    const recentValidations = recentActivity.map(activity => ({
      id: activity.id,
      status: activity.validation_status,
      date: activity.validation_date,
      hours: activity.hours_logged,
      project: activity.projects?.name || 'Unknown Project',
      user: activity.users?.display_name || 'Unknown User',
      avatar: activity.users?.avatar_url,
      isOwnContribution: activity.user_id === currentUser.id,
      isOwnValidation: activity.validated_by === currentUser.id
    }));

    // Calculate quality metrics
    const qualityMetrics = {
      accuracy: validationRate, // Based on approval rate
      consistency: Math.min(100, Math.max(0, 100 - (Math.abs(averageReviewTime - 24) / 24 * 100))), // Consistency based on review time
      timeliness: averageReviewTime > 0 ? Math.min(100, Math.max(0, 100 - (averageReviewTime / 48 * 100))) : 0 // Timeliness based on review speed
    };

    return {
      totalValidations,
      approvedValidations,
      pendingValidations,
      rejectedValidations,
      averageReviewTime,
      validationRate,
      recentValidations,
      topValidators,
      qualityMetrics
    };
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'success';
      case 'rejected':
        return 'danger';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'rejected':
        return <AlertCircle size={16} className="text-red-400" />;
      case 'pending':
        return <Clock size={16} className="text-yellow-400" />;
      default:
        return <Clock size={16} className="text-gray-400" />;
    }
  };

  const formatHours = (hours) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${Math.round(hours * 10) / 10}h`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading validation metrics...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-white mb-4">Error Loading Metrics</h2>
            <p className="text-white/70 mb-4">{error}</p>
            <Button onClick={loadValidationMetrics} color="primary" variant="bordered">
              Try Again
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center gap-3"
      >
        <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center">
          <CheckCircle size={20} className="text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Validation Metrics</h2>
          <p className="text-white/60">Track validation performance and quality metrics</p>
        </div>
      </motion.div>

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Target size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Total Validations</p>
                <p className="text-2xl font-bold text-white">{metricsData.totalValidations}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <CheckCircle size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Approval Rate</p>
                <p className="text-2xl font-bold text-white">{metricsData.validationRate}%</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Clock size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Avg Review Time</p>
                <p className="text-2xl font-bold text-white">{formatHours(metricsData.averageReviewTime)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <AlertCircle size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Pending Reviews</p>
                <p className="text-2xl font-bold text-white">{metricsData.pendingValidations}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Validation Status Breakdown */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <h3 className="text-lg font-semibold text-white">Validation Status Breakdown</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-green-500/20 flex items-center justify-center mx-auto mb-3">
                  <CheckCircle size={24} className="text-green-400" />
                </div>
                <p className="text-2xl font-bold text-white">{metricsData.approvedValidations}</p>
                <p className="text-white/60 text-sm">Approved</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-yellow-500/20 flex items-center justify-center mx-auto mb-3">
                  <Clock size={24} className="text-yellow-400" />
                </div>
                <p className="text-2xl font-bold text-white">{metricsData.pendingValidations}</p>
                <p className="text-white/60 text-sm">Pending</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center mx-auto mb-3">
                  <AlertCircle size={24} className="text-red-400" />
                </div>
                <p className="text-2xl font-bold text-white">{metricsData.rejectedValidations}</p>
                <p className="text-white/60 text-sm">Rejected</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Quality Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <div className="flex items-center gap-2">
              <TrendingUp size={20} className="text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Quality Metrics</h3>
            </div>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/60">Accuracy</span>
                  <span className="text-white font-medium">{Math.round(metricsData.qualityMetrics.accuracy)}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full"
                    style={{ width: `${metricsData.qualityMetrics.accuracy}%` }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/60">Consistency</span>
                  <span className="text-white font-medium">{Math.round(metricsData.qualityMetrics.consistency)}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full"
                    style={{ width: `${metricsData.qualityMetrics.consistency}%` }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/60">Timeliness</span>
                  <span className="text-white font-medium">{Math.round(metricsData.qualityMetrics.timeliness)}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                    style={{ width: `${metricsData.qualityMetrics.timeliness}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Recent Validation Activity */}
      {metricsData.recentValidations.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Recent Validation Activity</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {metricsData.recentValidations.map((validation, index) => (
                  <div key={validation.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(validation.status)}
                      <Avatar
                        src={validation.avatar}
                        name={validation.user}
                        size="sm"
                      />
                      <div>
                        <p className="text-white font-medium">{validation.project}</p>
                        <p className="text-white/60 text-sm">
                          {validation.isOwnContribution ? 'Your contribution' : `Validated by you`} • {formatHours(validation.hours)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Chip 
                        size="sm" 
                        color={getStatusColor(validation.status)}
                        variant="flat"
                      >
                        {validation.status}
                      </Chip>
                      <p className="text-white/60 text-xs mt-1">
                        {new Date(validation.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Top Validators (if user performs validations) */}
      {metricsData.topValidators.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Users size={20} className="text-purple-400" />
                <h3 className="text-lg font-semibold text-white">Validation Performance</h3>
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {metricsData.topValidators.map((validator, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="text-white/60 text-sm">#{index + 1}</span>
                      <Avatar
                        src={validator.user?.avatar_url}
                        name={validator.user?.display_name}
                        size="sm"
                      />
                      <div>
                        <p className="text-white font-medium">{validator.user?.display_name || 'Unknown User'}</p>
                        <p className="text-white/60 text-sm">
                          {validator.total} validations • {formatHours(validator.totalHours)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium">{validator.approvalRate}% approval</p>
                      <p className="text-white/60 text-sm">
                        {validator.approved} approved, {validator.rejected} rejected
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default ValidationMetrics;
