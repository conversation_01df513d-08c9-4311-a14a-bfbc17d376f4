# Retro Profile System Migration Instructions

This document provides detailed instructions for applying the Retro Profile system database migration using the Supabase CLI.

## Prerequisites

1. Install the Supabase CLI:
   ```bash
   # Using npm
   npm install -g supabase

   # Using Homebrew (macOS)
   brew install supabase/tap/supabase

   # Using Windows (with <PERSON>oop)
   scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
   scoop install supabase
   ```

2. Log in to Supabase:
   ```bash
   supabase login
   ```

3. Link your project to Supabase:
   ```bash
   supabase link --project-ref hqqlrrqvjcetoxbdjgzx
   ```

## Applying the Migration

### Option 1: Using Supabase CLI (Recommended)

1. Navigate to your project directory:
   ```bash
   cd /path/to/royaltea
   ```

2. Push the migration to your Supabase project:
   ```bash
   supabase db push
   ```

   This will apply all pending migrations, including the Retro Profile system migration.

### Option 2: Using Supabase Dashboard

If you prefer to apply the migration manually through the Supabase Dashboard:

1. Log in to the [Supabase Dashboard](https://app.supabase.io)
2. Select your project
3. Go to the SQL Editor
4. Copy the contents of the migration file: `supabase/migrations/20240701000003_create_retro_profile_system.sql`
5. Paste the SQL into the SQL Editor
6. Click "Run" to execute the migration

## Verification

After applying the migration, you can verify that it was successful by checking if the new columns exist in the `users` table:

1. Go to the Supabase Dashboard
2. Select your project
3. Go to the Table Editor
4. Select the `users` table
5. Check if the following columns exist:
   - `headline`
   - `location`
   - `website`
   - `cover_image_url`
   - `status_message`
   - `availability_status`
   - `profile_views`
   - `theme_settings`
   - `custom_css`
   - `profile_song_url`
   - `privacy_settings`

6. Also verify that the following tables were created:
   - `profile_comments`
   - `profile_views`
   - `top_collaborators`
   - `profile_themes`

## Troubleshooting

If you encounter any issues during the migration:

1. Check the Supabase logs for error messages
2. Ensure you have the necessary permissions to execute SQL statements
3. If specific statements fail, you can try executing them individually through the SQL Editor

## Manual SQL Execution

If you need to manually execute the SQL statements, here are the key statements from the migration:

```sql
-- Add new columns to the users table
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS headline TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS location TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS website TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS cover_image_url TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS status_message TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS availability_status TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS profile_views INTEGER DEFAULT 0;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS theme_settings JSONB DEFAULT '{
    "theme": "default",
    "colors": {
        "background": "#f8fafc",
        "primary": "#3b82f6",
        "secondary": "#f0f2f5",
        "text": "#1c1e21",
        "accent": "#6c5ce7",
        "links": "#3b82f6",
        "borders": "#e2e8f0"
    },
    "fonts": {
        "heading": "Inter",
        "body": "Inter"
    },
    "layout": "standard"
}'::jsonb;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS custom_css TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS profile_song_url TEXT;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{
    "profile_visibility": "public",
    "section_visibility": {
        "personal_info": true,
        "contact_details": false,
        "skills": true,
        "projects": true,
        "contribution_details": true,
        "contribution_percentages": false,
        "royalty_info": false,
        "profile_song": true,
        "top_collaborators": true,
        "comments": true,
        "profile_visitors": false
    },
    "verification_display": "level_only"
}'::jsonb;

-- Create profile_comments table
CREATE TABLE IF NOT EXISTS public.profile_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create profile_views table
CREATE TABLE IF NOT EXISTS public.profile_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    viewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ip_address TEXT,
    user_agent TEXT
);

-- Create top_collaborators table
CREATE TABLE IF NOT EXISTS public.top_collaborators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    collaborator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, collaborator_id)
);

-- Create increment_profile_views function
CREATE OR REPLACE FUNCTION increment_profile_views(profile_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update the profile_views counter
  UPDATE public.users
  SET profile_views = COALESCE(profile_views, 0) + 1
  WHERE id = profile_id;
END;
$$;
```

For more information on using the Supabase CLI, refer to the [official documentation](https://supabase.com/docs/reference/cli/usage).
