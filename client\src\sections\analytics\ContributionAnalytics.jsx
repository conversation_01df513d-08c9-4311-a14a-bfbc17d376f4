import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { BarChart3, TrendingUp, Clock, Target, Calendar, Users } from 'lucide-react';

/**
 * Contribution Analytics Section
 * 
 * Displays comprehensive analytics about user contributions including:
 * - Time tracking analytics
 * - Contribution patterns
 * - Performance metrics
 * - Team collaboration insights
 */
const ContributionAnalytics = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState({
    totalHours: 0,
    totalContributions: 0,
    averageHoursPerDay: 0,
    mostActiveDay: 'Monday',
    contributionTrend: 'up',
    weeklyData: [],
    monthlyData: [],
    projectBreakdown: [],
    skillContributions: []
  });
  const [timeRange, setTimeRange] = useState('30');
  const [error, setError] = useState(null);

  // Load contribution analytics data
  useEffect(() => {
    if (currentUser) {
      loadAnalyticsData();
    }
  }, [currentUser, timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(timeRange));

      // Load contribution data from Supabase
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          hours_logged,
          contribution_date,
          project_id,
          task_category,
          difficulty_level,
          projects (
            id,
            name
          )
        `)
        .eq('user_id', currentUser.id)
        .gte('contribution_date', startDate.toISOString())
        .lte('contribution_date', endDate.toISOString())
        .order('contribution_date', { ascending: false });

      if (contributionsError) throw contributionsError;

      // Process analytics data
      const processedData = processContributionData(contributions || []);
      setAnalyticsData(processedData);

    } catch (error) {
      console.error('Error loading contribution analytics:', error);
      setError(error.message);
      toast.error('Failed to load contribution analytics');
    } finally {
      setLoading(false);
    }
  };

  const processContributionData = (contributions) => {
    if (!contributions.length) {
      return {
        totalHours: 0,
        totalContributions: 0,
        averageHoursPerDay: 0,
        mostActiveDay: 'No data',
        contributionTrend: 'neutral',
        weeklyData: [],
        monthlyData: [],
        projectBreakdown: [],
        skillContributions: []
      };
    }

    const totalHours = contributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0);
    const totalContributions = contributions.length;
    const averageHoursPerDay = totalHours / parseInt(timeRange);

    // Calculate weekly data
    const weeklyData = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayContributions = contributions.filter(c => 
        new Date(c.contribution_date).toDateString() === date.toDateString()
      );
      return {
        day: date.toLocaleDateString('en-US', { weekday: 'short' }),
        hours: dayContributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0),
        contributions: dayContributions.length
      };
    }).reverse();

    // Find most active day
    const dayHours = {};
    contributions.forEach(c => {
      const day = new Date(c.contribution_date).toLocaleDateString('en-US', { weekday: 'long' });
      dayHours[day] = (dayHours[day] || 0) + (c.hours_logged || 0);
    });
    const mostActiveDay = Object.keys(dayHours).reduce((a, b) => dayHours[a] > dayHours[b] ? a : b, 'Monday');

    // Project breakdown
    const projectHours = {};
    contributions.forEach(c => {
      const projectName = c.projects?.name || 'Unknown Project';
      projectHours[projectName] = (projectHours[projectName] || 0) + (c.hours_logged || 0);
    });
    const projectBreakdown = Object.entries(projectHours).map(([name, hours]) => ({
      name,
      hours,
      percentage: Math.round((hours / totalHours) * 100)
    })).sort((a, b) => b.hours - a.hours);

    return {
      totalHours: Math.round(totalHours * 100) / 100,
      totalContributions,
      averageHoursPerDay: Math.round(averageHoursPerDay * 100) / 100,
      mostActiveDay,
      contributionTrend: totalHours > 0 ? 'up' : 'neutral',
      weeklyData,
      monthlyData: [], // Could be expanded
      projectBreakdown,
      skillContributions: [] // Could be expanded
    };
  };

  const formatHours = (hours) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${Math.round(hours * 10) / 10}h`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading contribution analytics...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-white mb-4">Error Loading Analytics</h2>
            <p className="text-white/70 mb-4">{error}</p>
            <Button onClick={loadAnalyticsData} color="primary" variant="bordered">
              Try Again
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
            <BarChart3 size={20} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Contribution Analytics</h2>
            <p className="text-white/60">Track your contribution patterns and performance</p>
          </div>
        </div>
        
        <Select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="w-32"
          size="sm"
        >
          <SelectItem key="7" value="7">Last 7 days</SelectItem>
          <SelectItem key="30" value="30">Last 30 days</SelectItem>
          <SelectItem key="90" value="90">Last 90 days</SelectItem>
        </Select>
      </motion.div>

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Clock size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Total Hours</p>
                <p className="text-2xl font-bold text-white">{formatHours(analyticsData.totalHours)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Target size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Contributions</p>
                <p className="text-2xl font-bold text-white">{analyticsData.totalContributions}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <TrendingUp size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Daily Average</p>
                <p className="text-2xl font-bold text-white">{formatHours(analyticsData.averageHoursPerDay)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Calendar size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Most Active Day</p>
                <p className="text-lg font-bold text-white">{analyticsData.mostActiveDay}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Weekly Activity Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <h3 className="text-lg font-semibold text-white">Weekly Activity</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-7 gap-2">
              {analyticsData.weeklyData.map((day, index) => (
                <div key={index} className="text-center">
                  <p className="text-white/60 text-xs mb-2">{day.day}</p>
                  <div 
                    className="bg-blue-500/20 rounded-lg p-2 min-h-[60px] flex flex-col justify-end"
                    style={{ 
                      backgroundColor: `rgba(59, 130, 246, ${Math.min(day.hours / 8, 1) * 0.5 + 0.1})` 
                    }}
                  >
                    <p className="text-white text-sm font-medium">{formatHours(day.hours)}</p>
                    <p className="text-white/60 text-xs">{day.contributions} tasks</p>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Project Breakdown */}
      {analyticsData.projectBreakdown.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Project Breakdown</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {analyticsData.projectBreakdown.slice(0, 5).map((project, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="text-white font-medium">{project.name}</p>
                      <div className="w-full bg-white/10 rounded-full h-2 mt-1">
                        <div 
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                          style={{ width: `${project.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-4 text-right">
                      <p className="text-white font-medium">{formatHours(project.hours)}</p>
                      <p className="text-white/60 text-sm">{project.percentage}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default ContributionAnalytics;
