import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Select, SelectItem, Chip } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { TrendingUp, DollarSign, Calendar, Target, BarChart3, Zap } from 'lucide-react';

/**
 * Revenue Projections Section
 * 
 * Displays revenue forecasting and projections including:
 * - Future earnings predictions
 * - Growth rate analysis
 * - Seasonal revenue patterns
 * - Goal tracking and milestones
 */
const RevenueProjections = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [projectionPeriod, setProjectionPeriod] = useState('3');
  const [projectionData, setProjectionData] = useState({
    currentMonthRevenue: 0,
    projectedRevenue: [],
    growthRate: 0,
    confidence: 0,
    seasonalTrends: [],
    milestones: [],
    recommendations: []
  });
  const [error, setError] = useState(null);

  // Load revenue projection data
  useEffect(() => {
    if (currentUser) {
      loadProjectionData();
    }
  }, [currentUser, projectionPeriod]);

  const loadProjectionData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load historical revenue data
      const { data: revenueHistory, error: revenueError } = await supabase
        .from('revenue')
        .select(`
          id,
          amount,
          currency,
          date_received,
          project_id,
          status,
          projects (
            id,
            name
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('status', 'confirmed')
        .order('date_received', { ascending: true });

      if (revenueError) throw revenueError;

      // Load contribution data for projection calculations
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          hours_logged,
          contribution_date,
          project_id,
          hourly_rate
        `)
        .eq('user_id', currentUser.id)
        .order('contribution_date', { ascending: true });

      if (contributionsError) throw contributionsError;

      // Load active projects for future revenue potential
      const { data: activeProjects, error: projectsError } = await supabase
        .from('project_members')
        .select(`
          project_id,
          role,
          projects (
            id,
            name,
            status,
            estimated_revenue,
            revenue_share_percentage
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('projects.status', 'active');

      if (projectsError) throw projectsError;

      // Process projection data
      const processedData = processRevenueProjections(
        revenueHistory || [],
        contributions || [],
        activeProjects || [],
        parseInt(projectionPeriod)
      );
      setProjectionData(processedData);

    } catch (error) {
      console.error('Error loading revenue projections:', error);
      setError(error.message);
      toast.error('Failed to load revenue projections');
    } finally {
      setLoading(false);
    }
  };

  const processRevenueProjections = (revenueHistory, contributions, activeProjects, months) => {
    // Calculate current month revenue
    const currentDate = new Date();
    const currentMonthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const currentMonthRevenue = revenueHistory
      .filter(r => new Date(r.date_received) >= currentMonthStart)
      .reduce((sum, r) => sum + (r.amount || 0), 0);

    // Calculate historical monthly averages
    const monthlyRevenue = {};
    revenueHistory.forEach(revenue => {
      const date = new Date(revenue.date_received);
      const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
      monthlyRevenue[monthKey] = (monthlyRevenue[monthKey] || 0) + (revenue.amount || 0);
    });

    const monthlyAverages = Object.values(monthlyRevenue);
    const averageMonthlyRevenue = monthlyAverages.length > 0 
      ? monthlyAverages.reduce((sum, amount) => sum + amount, 0) / monthlyAverages.length
      : 0;

    // Calculate growth rate
    const recentMonths = monthlyAverages.slice(-3);
    const olderMonths = monthlyAverages.slice(-6, -3);
    
    let growthRate = 0;
    if (recentMonths.length > 0 && olderMonths.length > 0) {
      const recentAvg = recentMonths.reduce((sum, amount) => sum + amount, 0) / recentMonths.length;
      const olderAvg = olderMonths.reduce((sum, amount) => sum + amount, 0) / olderMonths.length;
      
      if (olderAvg > 0) {
        growthRate = ((recentAvg - olderAvg) / olderAvg) * 100;
      }
    }

    // Generate projections
    const projectedRevenue = [];
    for (let i = 1; i <= months; i++) {
      const projectionDate = new Date();
      projectionDate.setMonth(projectionDate.getMonth() + i);
      
      // Base projection on historical average with growth rate applied
      let baseProjection = averageMonthlyRevenue;
      if (growthRate !== 0) {
        baseProjection = baseProjection * Math.pow(1 + (growthRate / 100), i);
      }

      // Add potential revenue from active projects
      const projectPotential = activeProjects.reduce((sum, member) => {
        const project = member.projects;
        if (project?.estimated_revenue && project?.revenue_share_percentage) {
          return sum + (project.estimated_revenue * (project.revenue_share_percentage / 100) / 12);
        }
        return sum;
      }, 0);

      const totalProjection = baseProjection + (projectPotential * 0.3); // 30% confidence on project potential

      projectedRevenue.push({
        month: projectionDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        date: projectionDate.toISOString(),
        baseProjection: Math.round(baseProjection * 100) / 100,
        projectPotential: Math.round(projectPotential * 100) / 100,
        totalProjection: Math.round(totalProjection * 100) / 100,
        confidence: Math.max(90 - (i * 10), 50) // Decreasing confidence over time
      });
    }

    // Calculate seasonal trends
    const seasonalData = {};
    revenueHistory.forEach(revenue => {
      const month = new Date(revenue.date_received).getMonth();
      const monthName = new Date(2023, month).toLocaleDateString('en-US', { month: 'long' });
      seasonalData[monthName] = (seasonalData[monthName] || 0) + (revenue.amount || 0);
    });

    const seasonalTrends = Object.entries(seasonalData)
      .map(([month, total]) => ({
        month,
        averageRevenue: Math.round(total / Math.max(revenueHistory.length / 12, 1) * 100) / 100
      }))
      .sort((a, b) => b.averageRevenue - a.averageRevenue);

    // Generate milestones
    const milestones = [];
    const totalProjected = projectedRevenue.reduce((sum, p) => sum + p.totalProjection, 0);
    
    if (totalProjected > 0) {
      milestones.push({
        title: `${months}-Month Revenue Goal`,
        target: Math.round(totalProjected * 100) / 100,
        current: currentMonthRevenue,
        progress: Math.min((currentMonthRevenue / (totalProjected / months)) * 100, 100),
        deadline: projectedRevenue[projectedRevenue.length - 1]?.date
      });
    }

    // Generate recommendations
    const recommendations = [];
    
    if (growthRate > 0) {
      recommendations.push(`📈 Revenue is growing at ${Math.round(growthRate)}% - maintain current strategies`);
    } else if (growthRate < -10) {
      recommendations.push(`📉 Revenue declining by ${Math.abs(Math.round(growthRate))}% - consider diversifying income sources`);
    }

    if (activeProjects.length > 0) {
      recommendations.push(`🎯 ${activeProjects.length} active projects with revenue potential`);
    }

    if (seasonalTrends.length > 0) {
      const bestMonth = seasonalTrends[0];
      recommendations.push(`🌟 Historically strongest in ${bestMonth.month}`);
    }

    const confidence = Math.round(Math.max(100 - (months * 5) - Math.abs(growthRate), 50));

    return {
      currentMonthRevenue: Math.round(currentMonthRevenue * 100) / 100,
      projectedRevenue,
      growthRate: Math.round(growthRate * 100) / 100,
      confidence,
      seasonalTrends,
      milestones,
      recommendations
    };
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 80) return 'success';
    if (confidence >= 60) return 'warning';
    return 'danger';
  };

  const getGrowthColor = (rate) => {
    if (rate > 0) return 'success';
    if (rate < -5) return 'danger';
    return 'primary';
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Calculating revenue projections...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-white mb-4">Error Loading Projections</h2>
            <p className="text-white/70 mb-4">{error}</p>
            <Button onClick={loadProjectionData} color="primary" variant="bordered">
              Try Again
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
            <TrendingUp size={20} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Revenue Projections</h2>
            <p className="text-white/60">Forecast future earnings and track growth</p>
          </div>
        </div>
        
        <Select
          value={projectionPeriod}
          onChange={(e) => setProjectionPeriod(e.target.value)}
          className="w-32"
          size="sm"
        >
          <SelectItem key="3" value="3">3 months</SelectItem>
          <SelectItem key="6" value="6">6 months</SelectItem>
          <SelectItem key="12" value="12">12 months</SelectItem>
        </Select>
      </motion.div>

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <DollarSign size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Current Month</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(projectionData.currentMonthRevenue)}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <TrendingUp size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Growth Rate</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold text-white">
                    {projectionData.growthRate > 0 ? '+' : ''}{projectionData.growthRate}%
                  </p>
                  <Chip 
                    size="sm" 
                    color={getGrowthColor(projectionData.growthRate)}
                    variant="flat"
                  >
                    {projectionData.growthRate > 0 ? 'Growing' : projectionData.growthRate < 0 ? 'Declining' : 'Stable'}
                  </Chip>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Target size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Confidence Level</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold text-white">{projectionData.confidence}%</p>
                  <Chip 
                    size="sm" 
                    color={getConfidenceColor(projectionData.confidence)}
                    variant="flat"
                  >
                    {projectionData.confidence >= 80 ? 'High' : projectionData.confidence >= 60 ? 'Medium' : 'Low'}
                  </Chip>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <BarChart3 size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Total Projected</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(
                    projectionData.projectedRevenue.reduce((sum, p) => sum + p.totalProjection, 0)
                  )}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Revenue Projections Chart */}
      {projectionData.projectedRevenue.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Monthly Projections</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {projectionData.projectedRevenue.map((projection, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Calendar size={16} className="text-white/60" />
                        <h4 className="text-white font-medium">{projection.month}</h4>
                        <Chip 
                          size="sm" 
                          color={getConfidenceColor(projection.confidence)}
                          variant="flat"
                        >
                          {projection.confidence}% confidence
                        </Chip>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-white/60">Base Projection</p>
                          <p className="text-white font-medium">{formatCurrency(projection.baseProjection)}</p>
                        </div>
                        <div>
                          <p className="text-white/60">Project Potential</p>
                          <p className="text-white font-medium">{formatCurrency(projection.projectPotential)}</p>
                        </div>
                        <div>
                          <p className="text-white/60">Total Projection</p>
                          <p className="text-white font-bold text-lg">{formatCurrency(projection.totalProjection)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Milestones */}
      {projectionData.milestones.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Target size={20} className="text-yellow-400" />
                <h3 className="text-lg font-semibold text-white">Revenue Milestones</h3>
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {projectionData.milestones.map((milestone, index) => (
                  <div key={index} className="p-4 bg-white/5 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-medium">{milestone.title}</h4>
                      <p className="text-white font-bold">{formatCurrency(milestone.target)}</p>
                    </div>
                    <div className="w-full bg-white/10 rounded-full h-3 mb-2">
                      <div 
                        className="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full"
                        style={{ width: `${Math.min(milestone.progress, 100)}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-white/60">
                      <span>Current: {formatCurrency(milestone.current)}</span>
                      <span>{Math.round(milestone.progress)}% complete</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Recommendations */}
      {projectionData.recommendations.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Zap size={20} className="text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Insights & Recommendations</h3>
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-2">
                {projectionData.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-center gap-3 p-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <p className="text-white/80">{recommendation}</p>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default RevenueProjections;
