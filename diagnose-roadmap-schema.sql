-- Diagnostic Script: Check Current Roadmap Schema
-- Run this first to understand the current database structure

-- Check the current roadmap table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'roadmap' 
ORDER BY ordinal_position;

-- Check current data in the roadmap table
SELECT 
    id,
    latest_feature,
    updated_at,
    created_at,
    pg_typeof(latest_feature) as latest_feature_type,
    pg_typeof(data) as data_type
FROM roadmap 
ORDER BY created_at DESC 
LIMIT 1;

-- Check if latest_feature contains JSON
SELECT 
    id,
    latest_feature,
    CASE 
        WHEN latest_feature::text ~ '^{.*}$' THEN 'Looks like JSON'
        ELSE 'Looks like plain text'
    END as format_check
FROM roadmap 
ORDER BY created_at DESC 
LIMIT 1;

-- Show the actual content
SELECT 
    'Current latest_feature content:' as info,
    latest_feature
FROM roadmap 
ORDER BY created_at DESC 
LIMIT 1;
