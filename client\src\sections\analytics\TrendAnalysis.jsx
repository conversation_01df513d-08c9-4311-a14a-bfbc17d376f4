import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { TrendingUp, TrendingDown, BarChart3, Calendar, Activity, Zap } from 'lucide-react';

/**
 * Trend Analysis Section
 * 
 * Displays trend analysis and forecasting including:
 * - Productivity trends over time
 * - Seasonal patterns
 * - Performance predictions
 * - Growth metrics
 */
const TrendAnalysis = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('90');
  const [trendData, setTrendData] = useState({
    productivityTrend: 'stable',
    trendPercentage: 0,
    weeklyTrends: [],
    monthlyTrends: [],
    seasonalPatterns: [],
    predictions: {
      nextWeekHours: 0,
      nextMonthHours: 0,
      growthRate: 0
    },
    insights: []
  });
  const [error, setError] = useState(null);

  // Load trend analysis data
  useEffect(() => {
    if (currentUser) {
      loadTrendData();
    }
  }, [currentUser, timeRange]);

  const loadTrendData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(timeRange));

      // Load contribution data for trend analysis
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          hours_logged,
          contribution_date,
          project_id,
          task_category,
          difficulty_level
        `)
        .eq('user_id', currentUser.id)
        .gte('contribution_date', startDate.toISOString())
        .lte('contribution_date', endDate.toISOString())
        .order('contribution_date', { ascending: true });

      if (contributionsError) throw contributionsError;

      // Load task completion data
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          completion_timestamp,
          estimated_hours,
          logged_hours,
          difficulty_level
        `)
        .eq('assigned_to', currentUser.id)
        .not('completion_timestamp', 'is', null)
        .gte('completion_timestamp', startDate.toISOString())
        .lte('completion_timestamp', endDate.toISOString())
        .order('completion_timestamp', { ascending: true });

      if (tasksError) throw tasksError;

      // Process trend data
      const processedData = processTrendAnalysis(contributions || [], tasks || [], parseInt(timeRange));
      setTrendData(processedData);

    } catch (error) {
      console.error('Error loading trend analysis:', error);
      setError(error.message);
      toast.error('Failed to load trend analysis');
    } finally {
      setLoading(false);
    }
  };

  const processTrendAnalysis = (contributions, tasks, dayRange) => {
    if (!contributions.length && !tasks.length) {
      return {
        productivityTrend: 'stable',
        trendPercentage: 0,
        weeklyTrends: [],
        monthlyTrends: [],
        seasonalPatterns: [],
        predictions: {
          nextWeekHours: 0,
          nextMonthHours: 0,
          growthRate: 0
        },
        insights: ['No data available for trend analysis']
      };
    }

    // Calculate weekly trends
    const weeklyData = [];
    const weeksToAnalyze = Math.ceil(dayRange / 7);
    
    for (let i = 0; i < weeksToAnalyze; i++) {
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - (i * 7) - 6);
      const weekEnd = new Date();
      weekEnd.setDate(weekEnd.getDate() - (i * 7));

      const weekContributions = contributions.filter(c => {
        const date = new Date(c.contribution_date);
        return date >= weekStart && date <= weekEnd;
      });

      const weekTasks = tasks.filter(t => {
        const date = new Date(t.completion_timestamp);
        return date >= weekStart && date <= weekEnd;
      });

      const weekHours = weekContributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0);
      const weekTaskCount = weekTasks.length;

      weeklyData.unshift({
        week: `Week ${weeksToAnalyze - i}`,
        startDate: weekStart.toISOString().split('T')[0],
        hours: Math.round(weekHours * 100) / 100,
        tasks: weekTaskCount,
        contributions: weekContributions.length
      });
    }

    // Calculate monthly trends (if range is long enough)
    const monthlyData = [];
    if (dayRange >= 60) {
      const monthsToAnalyze = Math.ceil(dayRange / 30);
      
      for (let i = 0; i < monthsToAnalyze; i++) {
        const monthStart = new Date();
        monthStart.setDate(monthStart.getDate() - (i * 30) - 29);
        const monthEnd = new Date();
        monthEnd.setDate(monthEnd.getDate() - (i * 30));

        const monthContributions = contributions.filter(c => {
          const date = new Date(c.contribution_date);
          return date >= monthStart && date <= monthEnd;
        });

        const monthHours = monthContributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0);

        monthlyData.unshift({
          month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          hours: Math.round(monthHours * 100) / 100,
          contributions: monthContributions.length
        });
      }
    }

    // Calculate productivity trend
    const recentWeeks = weeklyData.slice(-2);
    let productivityTrend = 'stable';
    let trendPercentage = 0;

    if (recentWeeks.length === 2) {
      const [previousWeek, currentWeek] = recentWeeks;
      if (currentWeek.hours > 0 && previousWeek.hours > 0) {
        trendPercentage = Math.round(((currentWeek.hours - previousWeek.hours) / previousWeek.hours) * 100);
        if (trendPercentage > 5) {
          productivityTrend = 'up';
        } else if (trendPercentage < -5) {
          productivityTrend = 'down';
        }
      }
    }

    // Calculate seasonal patterns (day of week analysis)
    const dayPatterns = {};
    contributions.forEach(c => {
      const day = new Date(c.contribution_date).toLocaleDateString('en-US', { weekday: 'long' });
      if (!dayPatterns[day]) {
        dayPatterns[day] = { hours: 0, count: 0 };
      }
      dayPatterns[day].hours += c.hours_logged || 0;
      dayPatterns[day].count += 1;
    });

    const seasonalPatterns = Object.entries(dayPatterns).map(([day, data]) => ({
      day,
      averageHours: Math.round((data.hours / Math.max(data.count, 1)) * 100) / 100,
      totalContributions: data.count
    })).sort((a, b) => b.averageHours - a.averageHours);

    // Generate predictions
    const totalHours = contributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0);
    const averageWeeklyHours = weeklyData.length > 0 
      ? weeklyData.reduce((sum, w) => sum + w.hours, 0) / weeklyData.length
      : 0;

    const predictions = {
      nextWeekHours: Math.round(averageWeeklyHours * 100) / 100,
      nextMonthHours: Math.round(averageWeeklyHours * 4.3 * 100) / 100,
      growthRate: trendPercentage
    };

    // Generate insights
    const insights = [];
    
    if (productivityTrend === 'up') {
      insights.push(`📈 Productivity is trending upward by ${Math.abs(trendPercentage)}%`);
    } else if (productivityTrend === 'down') {
      insights.push(`📉 Productivity has decreased by ${Math.abs(trendPercentage)}%`);
    } else {
      insights.push('📊 Productivity remains stable');
    }

    if (seasonalPatterns.length > 0) {
      const bestDay = seasonalPatterns[0];
      insights.push(`🌟 Most productive day: ${bestDay.day} (${bestDay.averageHours}h avg)`);
    }

    if (averageWeeklyHours > 0) {
      insights.push(`⏱️ Weekly average: ${Math.round(averageWeeklyHours * 100) / 100} hours`);
    }

    const recentActivity = contributions.filter(c => {
      const date = new Date(c.contribution_date);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return date >= sevenDaysAgo;
    });

    if (recentActivity.length > 0) {
      insights.push(`🔥 ${recentActivity.length} contributions in the last 7 days`);
    }

    return {
      productivityTrend,
      trendPercentage,
      weeklyTrends: weeklyData,
      monthlyTrends: monthlyData,
      seasonalPatterns,
      predictions,
      insights
    };
  };

  const formatHours = (hours) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${Math.round(hours * 10) / 10}h`;
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={20} className="text-green-400" />;
      case 'down':
        return <TrendingDown size={20} className="text-red-400" />;
      default:
        return <Activity size={20} className="text-blue-400" />;
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'up':
        return 'success';
      case 'down':
        return 'danger';
      default:
        return 'primary';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Analyzing trends...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-white mb-4">Error Loading Trends</h2>
            <p className="text-white/70 mb-4">{error}</p>
            <Button onClick={loadTrendData} color="primary" variant="bordered">
              Try Again
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center">
            <BarChart3 size={20} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Trend Analysis</h2>
            <p className="text-white/60">Analyze patterns and predict future performance</p>
          </div>
        </div>
        
        <Select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="w-32"
          size="sm"
        >
          <SelectItem key="30" value="30">30 days</SelectItem>
          <SelectItem key="90" value="90">90 days</SelectItem>
          <SelectItem key="180" value="180">6 months</SelectItem>
        </Select>
      </motion.div>

      {/* Trend Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {getTrendIcon(trendData.productivityTrend)}
                <div>
                  <h3 className="text-xl font-bold text-white">Productivity Trend</h3>
                  <p className="text-white/60">Current trajectory analysis</p>
                </div>
              </div>
              <div className="text-right">
                <Chip 
                  size="lg" 
                  color={getTrendColor(trendData.productivityTrend)}
                  variant="flat"
                >
                  {trendData.productivityTrend.toUpperCase()}
                </Chip>
                {trendData.trendPercentage !== 0 && (
                  <p className="text-white/60 text-sm mt-1">
                    {trendData.trendPercentage > 0 ? '+' : ''}{trendData.trendPercentage}%
                  </p>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Weekly Trends */}
      {trendData.weeklyTrends.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Weekly Trends</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {trendData.weeklyTrends.slice(-8).map((week, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div>
                      <p className="text-white font-medium">{week.week}</p>
                      <p className="text-white/60 text-sm">{week.startDate}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium">{formatHours(week.hours)}</p>
                      <p className="text-white/60 text-sm">{week.contributions} contributions</p>
                    </div>
                    <div className="w-24 h-2 bg-white/10 rounded-full ml-4">
                      <div 
                        className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full"
                        style={{ 
                          width: `${Math.min((week.hours / Math.max(...trendData.weeklyTrends.map(w => w.hours))) * 100, 100)}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Predictions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6 text-center">
            <Calendar size={24} className="text-blue-400 mx-auto mb-2" />
            <p className="text-white/60 text-sm">Next Week Prediction</p>
            <p className="text-2xl font-bold text-white">{formatHours(trendData.predictions.nextWeekHours)}</p>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6 text-center">
            <Calendar size={24} className="text-purple-400 mx-auto mb-2" />
            <p className="text-white/60 text-sm">Next Month Prediction</p>
            <p className="text-2xl font-bold text-white">{formatHours(trendData.predictions.nextMonthHours)}</p>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6 text-center">
            <Zap size={24} className="text-orange-400 mx-auto mb-2" />
            <p className="text-white/60 text-sm">Growth Rate</p>
            <p className="text-2xl font-bold text-white">
              {trendData.predictions.growthRate > 0 ? '+' : ''}{trendData.predictions.growthRate}%
            </p>
          </CardBody>
        </Card>
      </motion.div>

      {/* Insights */}
      {trendData.insights.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Key Insights</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-2">
                {trendData.insights.map((insight, index) => (
                  <div key={index} className="flex items-center gap-3 p-2">
                    <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                    <p className="text-white/80">{insight}</p>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Seasonal Patterns */}
      {trendData.seasonalPatterns.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Weekly Patterns</h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
                {trendData.seasonalPatterns.map((pattern, index) => (
                  <div key={index} className="text-center p-3 bg-white/5 rounded-lg">
                    <p className="text-white font-medium text-sm">{pattern.day}</p>
                    <p className="text-white text-lg font-bold">{formatHours(pattern.averageHours)}</p>
                    <p className="text-white/60 text-xs">{pattern.totalContributions} contributions</p>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default TrendAnalysis;
