import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Select, SelectItem } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * System Monitoring Component - Enhanced System Health and Performance Monitoring
 * 
 * Features:
 * - Real-time system health dashboard
 * - Performance metrics and KPIs
 * - Error tracking and alerts
 * - Uptime monitoring and reporting
 * - Resource usage tracking
 */
const SystemMonitoring = ({ currentUser, className = "" }) => {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('1h');
  const [systemData, setSystemData] = useState({
    status: {
      overall: 'operational',
      uptime: 99.97,
      responseTime: 1.8,
      errorRate: 0.02,
      activeUsers: 1247,
      peakUsers: 1890
    },
    performance: {
      cpu: 78,
      memory: 65,
      storage: 42,
      bandwidth: 34,
      database: 56
    },
    metrics: {
      requestsPerMinute: 2450,
      averageResponseTime: 1.8,
      errorCount: 12,
      successRate: 99.98,
      throughput: 156.7
    },
    alerts: [
      {
        id: 'alert-1',
        type: 'warning',
        title: 'High CPU Usage',
        message: 'CPU usage above 75% threshold for 10 minutes',
        timestamp: '2025-01-16T11:45:00Z',
        severity: 'medium',
        status: 'active'
      },
      {
        id: 'alert-2',
        type: 'info',
        title: 'Database Query Optimization',
        message: 'Slow query detected and automatically optimized',
        timestamp: '2025-01-16T10:30:00Z',
        severity: 'low',
        status: 'resolved'
      },
      {
        id: 'alert-3',
        type: 'error',
        title: 'Payment Gateway Timeout',
        message: 'Payment processing experiencing delays',
        timestamp: '2025-01-16T09:15:00Z',
        severity: 'high',
        status: 'resolved'
      }
    ],
    services: [
      {
        name: 'Web Application',
        status: 'operational',
        uptime: 99.99,
        responseTime: 1.2,
        lastCheck: '2025-01-16T12:00:00Z'
      },
      {
        name: 'API Gateway',
        status: 'operational',
        uptime: 99.95,
        responseTime: 0.8,
        lastCheck: '2025-01-16T12:00:00Z'
      },
      {
        name: 'Database',
        status: 'operational',
        uptime: 100.0,
        responseTime: 0.3,
        lastCheck: '2025-01-16T12:00:00Z'
      },
      {
        name: 'Payment System',
        status: 'degraded',
        uptime: 98.5,
        responseTime: 3.2,
        lastCheck: '2025-01-16T12:00:00Z'
      },
      {
        name: 'File Storage',
        status: 'operational',
        uptime: 99.8,
        responseTime: 1.5,
        lastCheck: '2025-01-16T12:00:00Z'
      },
      {
        name: 'Email Service',
        status: 'operational',
        uptime: 99.9,
        responseTime: 2.1,
        lastCheck: '2025-01-16T12:00:00Z'
      }
    ]
  });

  // Load system monitoring data
  const loadSystemData = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Mock API call - in production this would call the monitoring API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Data is already set in state as mock data
      
    } catch (error) {
      console.error('Error loading system data:', error);
      toast.error('Failed to load system monitoring data');
    } finally {
      setLoading(false);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'operational': 'success',
      'degraded': 'warning',
      'outage': 'danger',
      'maintenance': 'secondary'
    };
    return colors[status] || 'default';
  };

  // Get alert color
  const getAlertColor = (type) => {
    const colors = {
      'error': 'danger',
      'warning': 'warning',
      'info': 'primary',
      'success': 'success'
    };
    return colors[type] || 'default';
  };

  // Get performance color based on value
  const getPerformanceColor = (value) => {
    if (value >= 80) return 'danger';
    if (value >= 60) return 'warning';
    return 'success';
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Handle refresh
  const handleRefresh = () => {
    loadSystemData();
    toast.success('System data refreshed');
  };

  useEffect(() => {
    loadSystemData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadSystemData, 30000);
    
    return () => clearInterval(interval);
  }, [timeRange]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">📊</span>
              <div>
                <h2 className="text-2xl font-bold">System Monitoring</h2>
                <p className="text-default-600">Real-time system health and performance tracking</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Select
                label="Time Range"
                selectedKeys={[timeRange]}
                onSelectionChange={(keys) => setTimeRange(Array.from(keys)[0])}
                className="w-32"
                size="sm"
              >
                <SelectItem key="1h">1 Hour</SelectItem>
                <SelectItem key="6h">6 Hours</SelectItem>
                <SelectItem key="24h">24 Hours</SelectItem>
                <SelectItem key="7d">7 Days</SelectItem>
              </Select>
              <Button
                color="primary"
                variant="flat"
                onClick={handleRefresh}
                startContent={<span>🔄</span>}
              >
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">🟢</span>
                <Chip color="success" variant="flat" size="sm">Status</Chip>
              </div>
              <div className="space-y-2">
                <div>
                  <div className="text-sm text-default-600">System Status</div>
                  <div className="text-lg font-bold text-green-600 capitalize">
                    {systemData.status.overall}
                  </div>
                </div>
                <div className="text-sm text-default-600">
                  Uptime: {systemData.status.uptime}%
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">⚡</span>
                <Chip color="primary" variant="flat" size="sm">Performance</Chip>
              </div>
              <div className="space-y-2">
                <div>
                  <div className="text-sm text-default-600">Response Time</div>
                  <div className="text-lg font-bold text-blue-600">
                    {systemData.status.responseTime}s
                  </div>
                </div>
                <div className="text-sm text-default-600">
                  Target: &lt;2s ✅
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">👥</span>
                <Chip color="secondary" variant="flat" size="sm">Users</Chip>
              </div>
              <div className="space-y-2">
                <div>
                  <div className="text-sm text-default-600">Active Users</div>
                  <div className="text-lg font-bold text-purple-600">
                    {systemData.status.activeUsers.toLocaleString()}
                  </div>
                </div>
                <div className="text-sm text-default-600">
                  Peak: {systemData.status.peakUsers.toLocaleString()}
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">🚨</span>
                <Chip color="warning" variant="flat" size="sm">Errors</Chip>
              </div>
              <div className="space-y-2">
                <div>
                  <div className="text-sm text-default-600">Error Rate</div>
                  <div className="text-lg font-bold text-orange-600">
                    {systemData.status.errorRate}%
                  </div>
                </div>
                <div className="text-sm text-default-600">
                  Target: &lt;0.1% ⚠️
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Resource Usage */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Resource Usage</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            {Object.entries(systemData.performance).map(([resource, value], index) => (
              <motion.div
                key={resource}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="mb-2">
                  <div className="text-sm font-medium capitalize">{resource}</div>
                  <div className={`text-2xl font-bold ${
                    value >= 80 ? 'text-danger' : 
                    value >= 60 ? 'text-warning' : 
                    'text-success'
                  }`}>
                    {value}%
                  </div>
                </div>
                <Progress
                  value={value}
                  color={getPerformanceColor(value)}
                  size="sm"
                  className="w-full"
                />
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Services Status */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Services Status</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {systemData.services.map((service, index) => (
              <motion.div
                key={service.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">{service.name}</h4>
                      <Chip
                        color={getStatusColor(service.status)}
                        size="sm"
                        variant="flat"
                      >
                        {service.status}
                      </Chip>
                    </div>
                    <div className="space-y-1 text-sm text-default-600">
                      <div>Uptime: {service.uptime}%</div>
                      <div>Response: {service.responseTime}s</div>
                      <div>Last Check: {formatTimestamp(service.lastCheck)}</div>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Performance Metrics</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {systemData.metrics.requestsPerMinute.toLocaleString()}
              </div>
              <div className="text-sm text-default-600">Requests/min</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">
                {systemData.metrics.averageResponseTime}s
              </div>
              <div className="text-sm text-default-600">Avg Response</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-warning">
                {systemData.metrics.errorCount}
              </div>
              <div className="text-sm text-default-600">Errors (1h)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">
                {systemData.metrics.successRate}%
              </div>
              <div className="text-sm text-default-600">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary">
                {systemData.metrics.throughput} MB/s
              </div>
              <div className="text-sm text-default-600">Throughput</div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Recent Alerts */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Recent Alerts</h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-3">
            {systemData.alerts.map((alert, index) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-start gap-3 p-3 border rounded-lg"
              >
                <div className="flex-shrink-0">
                  <Chip
                    color={getAlertColor(alert.type)}
                    size="sm"
                    variant="flat"
                  >
                    {alert.type}
                  </Chip>
                </div>
                <div className="flex-1">
                  <div className="font-semibold">{alert.title}</div>
                  <div className="text-sm text-default-600 mb-1">{alert.message}</div>
                  <div className="text-xs text-default-500">
                    {formatTimestamp(alert.timestamp)} • {alert.status}
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <Chip
                    color={alert.status === 'resolved' ? 'success' : 'warning'}
                    size="sm"
                    variant="dot"
                  >
                    {alert.status}
                  </Chip>
                </div>
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SystemMonitoring;
