# Project Wizard Enhancements for Agreement Generation

This document outlines the necessary changes to the project wizard to ensure it can collect all required data for generating complete agreements that match our example template.

## Overview

The current project wizard collects basic information but is missing several key fields required for generating complete agreements. This document identifies the missing fields, proposes UI changes, and outlines the data validation and transformation requirements.

## Missing Fields by Wizard Step

### Step 1: Project Basics

| Field | Current Status | Required Change |
|-------|---------------|----------------|
| Company Name | Missing | Add field for company name |
| Company Address | Missing | Add field for company address |
| Company State | Missing | Add field for company state |
| Company County | Missing | Add field for company county |
| Project Engine | Missing | Add field for project engine (for games/software) |
| Project Platforms | Missing | Add field for project platforms (for games/software) |
| Project Start Date | Exists but not used | Ensure proper usage in agreement |
| Project Launch Date | Exists but not used | Ensure proper usage in agreement |

### Step 2: Team & Contributors

| Field | Current Status | Required Change |
|-------|---------------|----------------|
| Owner Address | Missing | Add field for owner address |
| Owner State | Missing | Add field for owner state |
| Owner County | Missing | Add field for owner county |
| Owner Title | Missing | Add field for owner title |
| Contributor Address | Missing | Add field for contributor address |
| Contributor Is Company | Missing | Add toggle for contributor type (individual/company) |
| Contributor Company Name | Missing | Add field for contributor company name (if company) |
| Contributor Signer Name | Missing | Add field for contributor signer name (if company) |
| Contributor Signer Title | Missing | Add field for contributor signer title (if company) |

### Step 3: Royalty Model

| Field | Current Status | Required Change |
|-------|---------------|----------------|
| Minimum Payout Threshold | Missing | Add field for minimum payout threshold |
| Maximum Payout | Missing | Add field for maximum payout |
| Revenue Share Percentage | Missing | Add field for revenue share percentage |

### Step 4: Revenue Tranches

| Field | Current Status | Required Change |
|-------|---------------|----------------|
| Platform Fee Percentage | Exists but not used | Ensure proper usage in agreement |
| Minimum Revenue Threshold | Exists but not used | Ensure proper usage in agreement |
| Maximum Payout | Exists but not used | Ensure proper usage in agreement |
| Per Contributor Minimum | Exists but not used | Ensure proper usage in agreement |
| Per Contributor Maximum | Exists but not used | Ensure proper usage in agreement |

### Step 6: Milestones

| Field | Current Status | Required Change |
|-------|---------------|----------------|
| Deliverables | Exists but not used | Ensure proper usage in agreement |
| Approval Criteria | Exists but not used | Ensure proper usage in agreement |

## UI Flow Improvements

### Project Basics Step

1. **Add Company Information Section**:
   - Add a collapsible section for company information
   - Include fields for company name, address, state, and county
   - Add help text explaining the importance of this information for the agreement

2. **Enhance Project Type Selection**:
   - Add conditional fields based on project type
   - For games: show engine and platforms fields
   - For software: show platforms and technology stack fields
   - For music: show genre and distribution platform fields

3. **Improve Date Selection**:
   - Add clear labels and help text for start date and launch date
   - Add validation to ensure launch date is after start date
   - Add estimated duration calculation based on dates

### Team & Contributors Step

1. **Enhance Owner Information**:
   - Add a dedicated section for project owner information
   - Include fields for owner address, state, county, and title
   - Add validation to ensure at least one owner is specified

2. **Improve Contributor Information**:
   - Add toggle for contributor type (individual/company)
   - Add conditional fields based on contributor type
   - For companies: show company name, signer name, and signer title fields
   - Add field for contributor address

### Royalty Model Step

1. **Add Financial Parameters Section**:
   - Add fields for minimum payout threshold, maximum payout, and revenue share percentage
   - Add help text explaining the impact of these parameters
   - Add validation to ensure values are reasonable

### Milestones Step

1. **Enhance Milestone Information**:
   - Improve the UI for adding deliverables to each milestone
   - Add field for approval criteria
   - Add validation to ensure milestones have deliverables

## Data Validation Requirements

### Project Basics

- **Company Name**: Required if generating an agreement
- **Company Address**: Required if generating an agreement
- **Company State**: Required if generating an agreement
- **Project Name**: Required
- **Project Type**: Required
- **Project Description**: Required
- **Project Engine**: Required for game projects
- **Project Platforms**: Required for game and software projects

### Team & Contributors

- **Owner**: At least one owner is required
- **Owner Address**: Required if owner is specified
- **Owner Title**: Required if owner is specified
- **Contributor Address**: Required for each contributor
- **Contributor Company Name**: Required if contributor is a company
- **Contributor Signer Name**: Required if contributor is a company
- **Contributor Signer Title**: Required if contributor is a company

### Royalty Model

- **Minimum Payout Threshold**: Must be a positive number
- **Maximum Payout**: Must be greater than minimum payout threshold
- **Revenue Share Percentage**: Must be between 0 and 100

### Milestones

- **Milestone Title**: Required for each milestone
- **Milestone Target Date**: Required for each milestone
- **Deliverables**: At least one deliverable required for each milestone

## Data Transformation Logic

### Project Data Transformation

```javascript
// Transform project data for agreement generation
const transformProjectData = (projectData) => {
  return {
    name: projectData.name,
    title: projectData.name,
    description: projectData.description,
    project_type: projectData.project_type,
    company_name: projectData.company_name,
    engine: projectData.engine,
    platforms: projectData.platforms,
    estimated_duration: projectData.estimated_duration,
    start_date: projectData.start_date,
    launch_date: projectData.launch_date,
    // Add other fields as needed
  };
};
```

### Contributor Data Transformation

```javascript
// Transform contributor data for agreement generation
const transformContributorData = (contributors, currentUser) => {
  return {
    contributors: contributors.map(contributor => ({
      id: contributor.id,
      display_name: contributor.display_name,
      email: contributor.email,
      permission_level: contributor.permission_level,
      title: contributor.title,
      address: contributor.address,
      state: contributor.state,
      county: contributor.county,
      is_company: contributor.is_company,
      company_name: contributor.company_name,
      signer_name: contributor.signer_name,
      signer_title: contributor.signer_title
    })),
    currentUser: {
      email: currentUser.email,
      user_metadata: currentUser.user_metadata,
      address: currentUser.address
    }
  };
};
```

### Royalty Model Data Transformation

```javascript
// Transform royalty model data for agreement generation
const transformRoyaltyModelData = (royaltyModel) => {
  return {
    model_type: royaltyModel.model_type,
    model_schema: royaltyModel.model_schema,
    configuration: {
      tasks_weight: royaltyModel.configuration.tasks_weight,
      hours_weight: royaltyModel.configuration.hours_weight,
      difficulty_weight: royaltyModel.configuration.difficulty_weight
    },
    is_pre_expense: royaltyModel.is_pre_expense,
    contributor_percentage: royaltyModel.contributor_percentage,
    min_payout: royaltyModel.min_payout,
    max_payout: royaltyModel.max_payout
  };
};
```

## Implementation Plan

1. **Phase 1: Update Data Structures**
   - Update project data structure to include all required fields
   - Update database schema to store new fields
   - Update API endpoints to handle new fields

2. **Phase 2: Enhance UI Components**
   - Update ProjectBasics component to include company information
   - Update TeamContributors component to enhance owner and contributor information
   - Update RoyaltyModel component to include financial parameters
   - Update Milestones component to enhance milestone information

3. **Phase 3: Implement Data Validation**
   - Add client-side validation for all required fields
   - Add server-side validation to prevent invalid data
   - Add helpful error messages for validation failures

4. **Phase 4: Implement Data Transformation**
   - Create utility functions for transforming data between wizard and agreement generator
   - Update agreement generation code to use transformed data
   - Add logging and error handling for data transformation

5. **Phase 5: Testing and Refinement**
   - Test agreement generation with various project types and configurations
   - Refine UI based on user feedback
   - Optimize performance and user experience
