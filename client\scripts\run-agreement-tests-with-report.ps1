# Run all agreement tests and generate a report
Write-Host "Running all agreement tests and generating a report..." -ForegroundColor Cyan

# Create report directory if it doesn't exist
$reportDir = "test-output/reports"
if (-not (Test-Path $reportDir)) {
    New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
}

# Initialize report file
$reportFile = "$reportDir/agreement-test-report.md"
$date = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# Create report header
@"
# Agreement Test Report

Generated: $date

## Test Results

"@ | Out-File -FilePath $reportFile

# Run standard agreement tests
Write-Host "Step 1: Running standard agreement tests..." -ForegroundColor Yellow
$standardOutput = node scripts/run-agreement-tests.mjs 2>&1
$standardSuccess = $LASTEXITCODE -eq 0

# Append standard test results to report
@"
### Standard Agreement Tests

Status: $(if ($standardSuccess) { "✅ Passed" } else { "❌ Failed" })

\`\`\`
$standardOutput
\`\`\`

"@ | Out-File -FilePath $reportFile -Append

Write-Host ""

# Run minimal agreement tests
Write-Host "Step 2: Running minimal agreement tests..." -ForegroundColor Yellow
$minimalOutput = node scripts/test-minimal-agreement.mjs 2>&1
$minimalSuccess = $LASTEXITCODE -eq 0

# Append minimal test results to report
@"
### Minimal Agreement Tests

Status: $(if ($minimalSuccess) { "✅ Passed" } else { "❌ Failed" })

\`\`\`
$minimalOutput
\`\`\`

"@ | Out-File -FilePath $reportFile -Append

Write-Host ""

# Run reference agreement test
Write-Host "Step 3: Running reference agreement test..." -ForegroundColor Yellow
$referenceOutput = node scripts/test-reference-agreement.mjs 2>&1
$referenceSuccess = $LASTEXITCODE -eq 0

# Append reference test results to report
@"
### Reference Agreement Test

Status: $(if ($referenceSuccess) { "✅ Passed" } else { "❌ Failed" })

\`\`\`
$referenceOutput
\`\`\`

"@ | Out-File -FilePath $reportFile -Append

Write-Host ""

# Verify reference agreement
Write-Host "Step 4: Verifying reference agreement..." -ForegroundColor Yellow
$verifyReferenceOutput = node scripts/verify-reference-agreement.mjs 2>&1
$verifyReferenceSuccess = $LASTEXITCODE -eq 0

# Append verify reference results to report
@"
### Reference Agreement Verification

Status: $(if ($verifyReferenceSuccess) { "✅ Passed" } else { "❌ Failed" })

\`\`\`
$verifyReferenceOutput
\`\`\`

"@ | Out-File -FilePath $reportFile -Append

Write-Host ""

# Verify all agreements
Write-Host "Step 5: Verifying all agreements..." -ForegroundColor Yellow
$verifyAllOutput = node scripts/verify-agreements.mjs 2>&1
$verifyAllSuccess = $LASTEXITCODE -eq 0

# Append verify all results to report
@"
### All Agreements Verification

Status: $(if ($verifyAllSuccess) { "✅ Passed" } else { "❌ Failed" })

\`\`\`
$verifyAllOutput
\`\`\`

"@ | Out-File -FilePath $reportFile -Append

# Add summary to report
$overallSuccess = $standardSuccess -and $minimalSuccess -and $referenceSuccess -and $verifyReferenceSuccess -and $verifyAllSuccess

@"

## Summary

Overall Status: $(if ($overallSuccess) { "✅ All tests passed" } else { "❌ Some tests failed" })

| Test | Status |
|------|--------|
| Standard Agreement Tests | $(if ($standardSuccess) { "✅ Passed" } else { "❌ Failed" }) |
| Minimal Agreement Tests | $(if ($minimalSuccess) { "✅ Passed" } else { "❌ Failed" }) |
| Reference Agreement Test | $(if ($referenceSuccess) { "✅ Passed" } else { "❌ Failed" }) |
| Reference Agreement Verification | $(if ($verifyReferenceSuccess) { "✅ Passed" } else { "❌ Failed" }) |
| All Agreements Verification | $(if ($verifyAllSuccess) { "✅ Passed" } else { "❌ Failed" }) |

## Next Steps

Based on the test results, the following actions are recommended:

$(if (-not $standardSuccess) { "- Fix issues with standard agreement tests`n" } else { "" })
$(if (-not $minimalSuccess) { "- Fix issues with minimal agreement tests`n" } else { "" })
$(if (-not $referenceSuccess) { "- Fix issues with reference agreement test`n" } else { "" })
$(if (-not $verifyReferenceSuccess) { "- Fix discrepancies in reference agreement`n" } else { "" })
$(if (-not $verifyAllSuccess) { "- Fix issues with agreement verification`n" } else { "" })
$(if ($overallSuccess) { "- Proceed with implementing the project wizard enhancements`n- Add more test cases for different scenarios`n- Implement PDF generation and digital signatures" } else { "" })

"@ | Out-File -FilePath $reportFile -Append

Write-Host "All tests completed. Report generated at: $reportFile" -ForegroundColor Green

# Open the report file
Write-Host "Opening report file..." -ForegroundColor Cyan
Invoke-Item $reportFile

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
