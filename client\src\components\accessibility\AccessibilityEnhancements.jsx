import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardBody, Switch } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, 
  EyeOff, 
  Volume2, 
  VolumeX, 
  Keyboard, 
  MousePointer,
  Contrast,
  Type,
  Zap
} from 'lucide-react';

/**
 * Accessibility Enhancements Component
 * 
 * Provides comprehensive accessibility features:
 * - Focus management and indicators
 * - Screen reader support
 * - Keyboard navigation
 * - High contrast mode
 * - Reduced motion preferences
 * - Font size adjustments
 * - Skip links
 */

// Skip Links Component
export const SkipLinks = ({ links = [] }) => {
  const defaultLinks = [
    { href: '#main-content', label: 'Skip to main content' },
    { href: '#navigation', label: 'Skip to navigation' },
    { href: '#footer', label: 'Skip to footer' }
  ];
  
  const skipLinks = links.length > 0 ? links : defaultLinks;
  
  return (
    <div className="sr-only focus-within:not-sr-only">
      {skipLinks.map((link, index) => (
        <a
          key={index}
          href={link.href}
          className="absolute top-4 left-4 z-50 px-4 py-2 bg-primary text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          {link.label}
        </a>
      ))}
    </div>
  );
};

// Focus Trap Component
export const FocusTrap = ({ children, isActive = true, restoreFocus = true }) => {
  const containerRef = useRef(null);
  const previousActiveElement = useRef(null);
  
  useEffect(() => {
    if (!isActive) return;
    
    // Store the currently focused element
    previousActiveElement.current = document.activeElement;
    
    const container = containerRef.current;
    if (!container) return;
    
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    const handleTabKey = (e) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement?.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement?.focus();
            e.preventDefault();
          }
        }
      }
      
      if (e.key === 'Escape') {
        // Allow escape to close modals/dialogs
        const escapeEvent = new CustomEvent('focustrap:escape');
        container.dispatchEvent(escapeEvent);
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();
    
    return () => {
      container.removeEventListener('keydown', handleTabKey);
      if (restoreFocus && previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    };
  }, [isActive, restoreFocus]);
  
  return (
    <div ref={containerRef} className="focus-trap">
      {children}
    </div>
  );
};

// Screen Reader Announcements
export const ScreenReaderAnnouncement = ({ message, priority = 'polite' }) => {
  const [announcement, setAnnouncement] = useState('');
  
  useEffect(() => {
    if (message) {
      setAnnouncement(message);
      // Clear after announcement to allow re-announcements
      const timer = setTimeout(() => setAnnouncement(''), 1000);
      return () => clearTimeout(timer);
    }
  }, [message]);
  
  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {announcement}
    </div>
  );
};

// Accessibility Preferences Panel
export const AccessibilityPanel = ({ isOpen, onClose }) => {
  const [preferences, setPreferences] = useState({
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    focusIndicators: true,
    screenReaderMode: false
  });
  
  useEffect(() => {
    // Load preferences from localStorage
    const saved = localStorage.getItem('accessibility-preferences');
    if (saved) {
      setPreferences(JSON.parse(saved));
    }
  }, []);
  
  useEffect(() => {
    // Apply preferences to document
    const root = document.documentElement;
    
    if (preferences.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    if (preferences.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }
    
    if (preferences.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }
    
    if (preferences.focusIndicators) {
      root.classList.add('enhanced-focus');
    } else {
      root.classList.remove('enhanced-focus');
    }
    
    // Save to localStorage
    localStorage.setItem('accessibility-preferences', JSON.stringify(preferences));
  }, [preferences]);
  
  const updatePreference = (key, value) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  };
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <FocusTrap isActive={isOpen}>
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <Card className="w-full max-w-md">
                <CardBody className="p-6">
                  <h2 className="text-xl font-bold mb-4">Accessibility Settings</h2>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Contrast size={20} />
                        <span>High Contrast</span>
                      </div>
                      <Switch
                        isSelected={preferences.highContrast}
                        onValueChange={(value) => updatePreference('highContrast', value)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Zap size={20} />
                        <span>Reduced Motion</span>
                      </div>
                      <Switch
                        isSelected={preferences.reducedMotion}
                        onValueChange={(value) => updatePreference('reducedMotion', value)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Type size={20} />
                        <span>Large Text</span>
                      </div>
                      <Switch
                        isSelected={preferences.largeText}
                        onValueChange={(value) => updatePreference('largeText', value)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MousePointer size={20} />
                        <span>Enhanced Focus</span>
                      </div>
                      <Switch
                        isSelected={preferences.focusIndicators}
                        onValueChange={(value) => updatePreference('focusIndicators', value)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Volume2 size={20} />
                        <span>Screen Reader Mode</span>
                      </div>
                      <Switch
                        isSelected={preferences.screenReaderMode}
                        onValueChange={(value) => updatePreference('screenReaderMode', value)}
                      />
                    </div>
                  </div>
                  
                  <div className="flex gap-2 mt-6">
                    <Button
                      color="primary"
                      variant="solid"
                      onClick={onClose}
                      className="flex-1"
                    >
                      Save Settings
                    </Button>
                    <Button
                      color="default"
                      variant="bordered"
                      onClick={onClose}
                    >
                      Cancel
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </FocusTrap>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Accessibility Hook
export const useAccessibility = () => {
  const [preferences, setPreferences] = useState({
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    focusIndicators: true,
    screenReaderMode: false
  });
  
  const [announcement, setAnnouncement] = useState('');
  
  const announce = (message, priority = 'polite') => {
    setAnnouncement(message);
    setTimeout(() => setAnnouncement(''), 1000);
  };
  
  const focusElement = (selector) => {
    const element = document.querySelector(selector);
    if (element) {
      element.focus();
    }
  };
  
  return {
    preferences,
    setPreferences,
    announce,
    focusElement,
    announcement
  };
};

export default {
  SkipLinks,
  FocusTrap,
  ScreenReaderAnnouncement,
  AccessibilityPanel,
  useAccessibility
};
