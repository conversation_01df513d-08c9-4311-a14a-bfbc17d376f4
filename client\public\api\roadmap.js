// Public API for roadmap data
// This file should be placed in the client/public/api folder

// Import Supabase client
import { createClient } from 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/+esm';

// Initialize Supabase client
const supabaseUrl = 'https://lfnzfbvvvxvxvvvvvvvv.supabase.co'; // Replace with your Supabase URL
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'; // Replace with your Supabase anon key

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Function to get roadmap data
async function getRoadmapData() {
  try {
    // Get roadmap data from Supabase
    const { data, error } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (error) {
      throw error;
    }
    
    if (data && data.length > 0 && data[0].data) {
      return {
        success: true,
        data: data[0].data,
        stats: calculateStats(data[0].data)
      };
    } else {
      return {
        success: false,
        message: 'No roadmap data found'
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;
    
    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });
    
    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;
    
    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

// Handle API request
async function handleRequest() {
  // Set CORS headers
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type'
  };
  
  // Handle OPTIONS request (preflight)
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }
  
  // Only allow GET requests
  if (request.method !== 'GET') {
    return new Response(JSON.stringify({
      success: false,
      message: 'Method not allowed'
    }), {
      status: 405,
      headers
    });
  }
  
  // Get roadmap data
  const result = await getRoadmapData();
  
  // Return response
  return new Response(JSON.stringify(result), {
    status: result.success ? 200 : 500,
    headers
  });
}

// Export handler
export default handleRequest;
