import React from 'react';
import { isExtensionError } from '../../utils/browserExtensionHandler.js';

/**
 * Error Boundary specifically designed to catch and handle browser extension errors
 * that might bubble up through the React component tree
 */
class ExtensionErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Check if this is a browser extension error
    if (isExtensionError(error.message || error.toString())) {
      // Don't update state for extension errors - just ignore them
      return null;
    }
    
    // Update state for non-extension errors
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Check if this is a browser extension error
    if (isExtensionError(error.message || error.toString())) {
      // Log extension errors in development only
      if (process.env.NODE_ENV === 'development') {
        console.debug('[Extension Error Boundary] Browser extension error caught:', error, errorInfo);
      }
      // Don't render error UI for extension errors
      return;
    }

    // Handle non-extension errors normally
    console.error('[Error Boundary] Application error caught:', error, errorInfo);
    this.setState({
      hasError: true,
      error,
      errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI for actual application errors
      return (
        <div className="error-boundary-fallback">
          <div className="error-container">
            <h2>🚨 Something went wrong</h2>
            <p>An unexpected error occurred in the application.</p>
            <details className="error-details">
              <summary>Error Details</summary>
              <pre>{this.state.error && this.state.error.toString()}</pre>
              <pre>{this.state.errorInfo.componentStack}</pre>
            </details>
            <button 
              onClick={() => window.location.reload()}
              className="error-reload-button"
            >
              🔄 Reload Page
            </button>
          </div>
        </div>
      );
    }

    // Render children normally
    return this.props.children;
  }
}

export default ExtensionErrorBoundary;
