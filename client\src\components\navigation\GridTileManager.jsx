import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Switch, Divider, Chip } from '@heroui/react';
import { useNavigation } from '../../contexts/NavigationContext';

/**
 * Grid Tile Manager Component
 * 
 * Provides a side menu for managing tile visibility on the home grid.
 * Allows users to show/hide tiles and set defaults.
 */
const GridTileManager = ({ canvases, isOpen, onClose, className = "" }) => {
  const { hiddenCanvases, favoriteCanvases, actions } = useNavigation();
  const [localHidden, setLocalHidden] = useState(hiddenCanvases);
  const [localFavorites, setLocalFavorites] = useState(favoriteCanvases);

  // Default visible tiles as specified in the task
  const defaultVisibleTiles = ['home', 'start', 'track', 'earn'];

  // Sync with context when it changes
  useEffect(() => {
    setLocalHidden(hiddenCanvases);
    setLocalFavorites(favoriteCanvases);
  }, [hiddenCanvases, favoriteCanvases]);

  // Initialize defaults if no preferences are set
  useEffect(() => {
    if (hiddenCanvases.length === 0 && favoriteCanvases.length === 0) {
      // Hide all tiles except the default ones
      const allCanvasIds = Object.keys(canvases);
      const tilesToHide = allCanvasIds.filter(id => !defaultVisibleTiles.includes(id));
      
      tilesToHide.forEach(canvasId => {
        actions.toggleHidden(canvasId);
      });
    }
  }, [canvases, hiddenCanvases.length, favoriteCanvases.length, actions]);

  // Handle tile visibility toggle
  const handleTileToggle = (canvasId) => {
    actions.toggleHidden(canvasId);
    setLocalHidden(prev => 
      prev.includes(canvasId) 
        ? prev.filter(id => id !== canvasId)
        : [...prev, canvasId]
    );
  };

  // Handle favorite toggle
  const handleFavoriteToggle = (canvasId) => {
    actions.toggleFavorite(canvasId);
    setLocalFavorites(prev => 
      prev.includes(canvasId) 
        ? prev.filter(id => id !== canvasId)
        : [...prev, canvasId]
    );
  };

  // Reset to defaults
  const resetToDefaults = () => {
    const allCanvasIds = Object.keys(canvases);
    
    // Clear all current settings
    hiddenCanvases.forEach(canvasId => actions.toggleHidden(canvasId));
    favoriteCanvases.forEach(canvasId => actions.toggleFavorite(canvasId));
    
    // Hide all except defaults
    allCanvasIds.forEach(canvasId => {
      if (!defaultVisibleTiles.includes(canvasId)) {
        actions.toggleHidden(canvasId);
      }
    });
  };

  // Show all tiles
  const showAllTiles = () => {
    hiddenCanvases.forEach(canvasId => actions.toggleHidden(canvasId));
  };

  // Get canvas categories for organization
  const getCanvasCategory = (canvasId) => {
    const coreJourney = ['home', 'start', 'track', 'earn'];
    const secondary = ['projects', 'missions', 'teams', 'analytics'];
    const utility = ['profile', 'settings', 'notifications', 'help', 'bugs'];
    const admin = ['admin'];

    if (coreJourney.includes(canvasId)) return 'Core Journey';
    if (secondary.includes(canvasId)) return 'Secondary';
    if (utility.includes(canvasId)) return 'Utility';
    if (admin.includes(canvasId)) return 'Admin';
    return 'Other';
  };

  // Group canvases by category
  const groupedCanvases = Object.values(canvases).reduce((groups, canvas) => {
    const category = getCanvasCategory(canvas.id);
    if (!groups[category]) groups[category] = [];
    groups[category].push(canvas);
    return groups;
  }, {});

  // Sort categories in preferred order
  const categoryOrder = ['Core Journey', 'Secondary', 'Utility', 'Admin', 'Other'];
  const sortedCategories = categoryOrder.filter(cat => groupedCanvases[cat]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-[60] bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      >
        <motion.div
          initial={{ x: -400 }}
          animate={{ x: 0 }}
          exit={{ x: -400 }}
          transition={{ type: "spring", damping: 25, stiffness: 200 }}
          className="absolute left-0 top-0 h-full w-96 bg-background/95 backdrop-blur-md border-r border-divider shadow-2xl overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-bold">Grid Tiles</h2>
                <p className="text-sm text-default-600">Customize your home grid</p>
              </div>
              <Button
                isIconOnly
                variant="light"
                onClick={onClose}
                className="text-default-600 hover:text-default-900"
              >
                ✕
              </Button>
            </div>

            {/* Quick Actions */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Quick Actions</h3>
              <div className="flex gap-2 flex-wrap">
                <Button
                  size="sm"
                  variant="flat"
                  color="primary"
                  onClick={resetToDefaults}
                >
                  🔄 Reset to Defaults
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  color="success"
                  onClick={showAllTiles}
                >
                  👁️ Show All
                </Button>
              </div>
              <div className="mt-2 text-xs text-default-600">
                Defaults: Dashboard, Start, Track, Earn
              </div>
            </div>

            <Divider className="mb-6" />

            {/* Tile Categories */}
            <div className="space-y-6">
              {sortedCategories.map(category => (
                <div key={category}>
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    {category}
                    <Chip size="sm" variant="flat" color="default">
                      {groupedCanvases[category].length}
                    </Chip>
                  </h3>
                  
                  <div className="space-y-2">
                    {groupedCanvases[category].map(canvas => {
                      const isHidden = localHidden.includes(canvas.id);
                      const isFavorite = localFavorites.includes(canvas.id);
                      const isDefault = defaultVisibleTiles.includes(canvas.id);
                      
                      return (
                        <Card key={canvas.id} className="bg-default-50">
                          <CardBody className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <span className="text-2xl">{canvas.icon}</span>
                                <div>
                                  <div className="font-medium flex items-center gap-2">
                                    {canvas.title}
                                    {isDefault && (
                                      <Chip size="sm" color="primary" variant="flat">
                                        Default
                                      </Chip>
                                    )}
                                    {isFavorite && (
                                      <span className="text-yellow-500">⭐</span>
                                    )}
                                  </div>
                                  <div className="text-sm text-default-600">
                                    {canvas.description}
                                  </div>
                                </div>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Button
                                  isIconOnly
                                  size="sm"
                                  variant="light"
                                  onClick={() => handleFavoriteToggle(canvas.id)}
                                  className={isFavorite ? 'text-yellow-500' : 'text-default-400'}
                                >
                                  ⭐
                                </Button>
                                <Switch
                                  isSelected={!isHidden}
                                  onValueChange={() => handleTileToggle(canvas.id)}
                                  size="sm"
                                  color="primary"
                                />
                              </div>
                            </div>
                          </CardBody>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Footer Info */}
            <div className="mt-8 p-4 bg-default-100 rounded-lg">
              <div className="text-sm text-default-600">
                <div className="font-medium mb-1">Visibility Summary</div>
                <div>Visible: {Object.keys(canvases).length - localHidden.length}</div>
                <div>Hidden: {localHidden.length}</div>
                <div>Favorites: {localFavorites.length}</div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default GridTileManager;
