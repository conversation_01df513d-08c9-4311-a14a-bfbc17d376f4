# Run Wizard Agreement Tests
# This script runs both the reference agreement test and the wizard flow test

Write-Host "Running all agreement wizard flow tests..." -ForegroundColor Yellow

# Step 1: Run reference agreement test
Write-Host "Step 1: Running reference agreement test..." -ForegroundColor Yellow
node scripts/test-reference-agreement.mjs
if ($LASTEXITCODE -ne 0) {
    Write-Host "Reference agreement test failed with exit code $LASTEXITCODE" -ForegroundColor Red
    exit $LASTEXITCODE
}

# Step 2: Run wizard flow agreement test
Write-Host "Step 2: Running wizard flow agreement test..." -ForegroundColor Yellow
node scripts/test-wizard-flow-agreement.mjs
if ($LASTEXITCODE -ne 0) {
    Write-Host "Wizard flow agreement test failed with exit code $LASTEXITCODE" -ForegroundColor Red
    exit $LASTEXITCODE
}

# Step 3: Generate a report
Write-Host "Step 3: Generating test report..." -ForegroundColor Yellow

$reportFile = "test-output/reports/wizard-agreement-test-report.md"
$reportDir = Split-Path -Parent $reportFile
if (-not (Test-Path $reportDir)) {
    New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
}

$date = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

@"
# Wizard Agreement Test Report

Generated: $date

## Test Results

### Reference Agreement Test

Status: ✅ Passed

The reference agreement test generates an agreement that should match the example template
by providing all the necessary data to match the "Village of The Ages" example.

### Wizard Flow Agreement Test

Status: ✅ Passed

The wizard flow agreement test simulates a user going through all steps of the project wizard,
generates an agreement, and compares it with the reference template.

## Comparison Results

"@ | Out-File -FilePath $reportFile -Force

# Check if the diff file exists
$diffFile = "test-output/agreements/wizard-flow/wizard-reference-diff.md"
if (Test-Path $diffFile) {
    $diffContent = Get-Content $diffFile -Raw
    $diffSummary = "Differences found between wizard-generated agreement and reference agreement."
    
    # Count lines starting with + or -
    $addedLines = (Select-String -Path $diffFile -Pattern "^\+" -AllMatches).Matches.Count
    $removedLines = (Select-String -Path $diffFile -Pattern "^-" -AllMatches).Matches.Count
    
    # Calculate similarity percentage
    $totalLines = (Get-Content $diffFile).Count
    $unchangedLines = $totalLines - $addedLines - $removedLines
    $similarity = [math]::Round(($unchangedLines / $totalLines) * 100)
    
    @"
The wizard-generated agreement is $similarity% similar to the reference agreement.

- Added lines: $addedLines
- Removed lines: $removedLines
- Unchanged lines: $unchangedLines

### Sample Differences

```diff
$(Get-Content $diffFile -TotalCount 20)
...
```

Full diff file available at: $diffFile
"@ | Out-File -FilePath $reportFile -Append
} else {
    @"
No comparison results available. Make sure both tests ran successfully.
"@ | Out-File -FilePath $reportFile -Append
}

@"

## Summary

The end-to-end test verifies that the agreement generator works correctly in the context of the complete project wizard flow.
It confirms that:

1. All required sections are present in the generated agreement
2. All placeholders are properly replaced
3. Project-specific content is correctly inserted
4. The agreement maintains proper formatting and structure

## Next Steps

Based on the test results, consider the following actions:

1. Review any differences between the wizard-generated agreement and the reference agreement
2. Update the agreement generator if necessary to ensure consistent output
3. Add more test cases for different project types and configurations
4. Integrate this test into the CI/CD pipeline
"@ | Out-File -FilePath $reportFile -Append

# Open the report
Write-Host "Test report generated at: $reportFile" -ForegroundColor Green
Invoke-Item $reportFile

Write-Host "All tests completed successfully!" -ForegroundColor Green
