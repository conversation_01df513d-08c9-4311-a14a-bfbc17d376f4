import React from 'react';

const FileAttachmentsList = ({ files, compact = false }) => {
  if (!files || files.length === 0) {
    return null;
  }

  // Get appropriate icon based on file type
  const getFileIcon = (fileType) => {
    if (!fileType) return 'bi bi-file-earmark';
    
    const type = fileType.toLowerCase();
    
    if (type.includes('image')) return 'bi bi-file-earmark-image';
    if (type.includes('pdf')) return 'bi bi-file-earmark-pdf';
    if (type.includes('word') || type.includes('document')) return 'bi bi-file-earmark-word';
    if (type.includes('excel') || type.includes('spreadsheet')) return 'bi bi-file-earmark-excel';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'bi bi-file-earmark-ppt';
    if (type.includes('text')) return 'bi bi-file-earmark-text';
    if (type.includes('zip') || type.includes('compressed')) return 'bi bi-file-earmark-zip';
    if (type.includes('audio')) return 'bi bi-file-earmark-music';
    if (type.includes('video')) return 'bi bi-file-earmark-play';
    if (type.includes('code') || type.includes('javascript') || type.includes('html') || type.includes('css')) {
      return 'bi bi-file-earmark-code';
    }
    
    return 'bi bi-file-earmark';
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes || isNaN(bytes)) return 'Unknown size';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  return (
    <div className={`file-attachments-list ${compact ? 'compact' : ''}`}>
      {!compact && <h6>Attachments</h6>}
      <ul className="attachments-list">
        {files.map((file, index) => (
          <li key={index} className="attachment-item">
            <i className={getFileIcon(file.type)}></i>
            <a 
              href={file.url} 
              target="_blank" 
              rel="noopener noreferrer" 
              className="attachment-name"
              title={file.name}
            >
              {file.name}
            </a>
            {!compact && <span className="attachment-size">({formatFileSize(file.size)})</span>}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FileAttachmentsList;
