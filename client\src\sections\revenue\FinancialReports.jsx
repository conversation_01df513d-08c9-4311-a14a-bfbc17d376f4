import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Select, SelectItem, Chip } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { FileText, Download, Calendar, DollarSign, TrendingUp, PieChart } from 'lucide-react';

/**
 * Financial Reports Section
 * 
 * Displays comprehensive financial reporting including:
 * - Income statements
 * - Revenue breakdowns
 * - Tax summaries
 * - Downloadable reports
 */
const FinancialReports = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [reportPeriod, setReportPeriod] = useState('current_year');
  const [reportData, setReportData] = useState({
    totalRevenue: 0,
    totalExpenses: 0,
    netIncome: 0,
    revenueByProject: [],
    revenueByMonth: [],
    taxSummary: {
      taxableIncome: 0,
      estimatedTax: 0,
      deductions: 0
    },
    summary: {
      period: '',
      transactions: 0,
      averageMonthly: 0
    }
  });
  const [error, setError] = useState(null);
  const [generatingReport, setGeneratingReport] = useState(false);

  // Load financial report data
  useEffect(() => {
    if (currentUser) {
      loadReportData();
    }
  }, [currentUser, reportPeriod]);

  const loadReportData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate date range based on selected period
      const { startDate, endDate, periodLabel } = getDateRange(reportPeriod);

      // Load revenue data
      const { data: revenueData, error: revenueError } = await supabase
        .from('revenue')
        .select(`
          id,
          amount,
          currency,
          date_received,
          project_id,
          status,
          description,
          projects (
            id,
            name
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('status', 'confirmed')
        .gte('date_received', startDate.toISOString())
        .lte('date_received', endDate.toISOString())
        .order('date_received', { ascending: true });

      if (revenueError) throw revenueError;

      // Load expense data (if available)
      const { data: expenseData, error: expenseError } = await supabase
        .from('expenses')
        .select(`
          id,
          amount,
          currency,
          date,
          category,
          description,
          project_id
        `)
        .eq('user_id', currentUser.id)
        .gte('date', startDate.toISOString())
        .lte('date', endDate.toISOString())
        .order('date', { ascending: true });

      // Don't throw error if expenses table doesn't exist
      const expenses = expenseData || [];

      // Process financial report data
      const processedData = processFinancialData(
        revenueData || [],
        expenses,
        periodLabel,
        startDate,
        endDate
      );
      setReportData(processedData);

    } catch (error) {
      console.error('Error loading financial reports:', error);
      setError(error.message);
      toast.error('Failed to load financial reports');
    } finally {
      setLoading(false);
    }
  };

  const getDateRange = (period) => {
    const now = new Date();
    let startDate, endDate, periodLabel;

    switch (period) {
      case 'current_month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        periodLabel = now.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        break;
      case 'last_month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        periodLabel = startDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        break;
      case 'current_quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        startDate = new Date(now.getFullYear(), quarterStart, 1);
        endDate = new Date(now.getFullYear(), quarterStart + 3, 0);
        periodLabel = `Q${Math.floor(now.getMonth() / 3) + 1} ${now.getFullYear()}`;
        break;
      case 'current_year':
      default:
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear(), 11, 31);
        periodLabel = now.getFullYear().toString();
        break;
      case 'last_year':
        startDate = new Date(now.getFullYear() - 1, 0, 1);
        endDate = new Date(now.getFullYear() - 1, 11, 31);
        periodLabel = (now.getFullYear() - 1).toString();
        break;
    }

    return { startDate, endDate, periodLabel };
  };

  const processFinancialData = (revenue, expenses, periodLabel, startDate, endDate) => {
    // Calculate totals
    const totalRevenue = revenue.reduce((sum, r) => sum + (r.amount || 0), 0);
    const totalExpenses = expenses.reduce((sum, e) => sum + (e.amount || 0), 0);
    const netIncome = totalRevenue - totalExpenses;

    // Revenue by project
    const projectRevenue = {};
    revenue.forEach(r => {
      const projectName = r.projects?.name || 'Other';
      projectRevenue[projectName] = (projectRevenue[projectName] || 0) + (r.amount || 0);
    });

    const revenueByProject = Object.entries(projectRevenue)
      .map(([name, amount]) => ({
        project: name,
        amount: Math.round(amount * 100) / 100,
        percentage: totalRevenue > 0 ? Math.round((amount / totalRevenue) * 100) : 0
      }))
      .sort((a, b) => b.amount - a.amount);

    // Revenue by month
    const monthlyRevenue = {};
    revenue.forEach(r => {
      const date = new Date(r.date_received);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      monthlyRevenue[monthKey] = (monthlyRevenue[monthKey] || 0) + (r.amount || 0);
    });

    const revenueByMonth = Object.entries(monthlyRevenue)
      .map(([month, amount]) => ({
        month,
        amount: Math.round(amount * 100) / 100
      }))
      .sort((a, b) => new Date(a.month) - new Date(b.month));

    // Tax summary (simplified calculation)
    const taxableIncome = Math.max(netIncome, 0);
    const estimatedTaxRate = 0.25; // 25% estimated tax rate
    const estimatedTax = taxableIncome * estimatedTaxRate;
    const deductions = totalExpenses; // Simplified - expenses as deductions

    // Calculate average monthly revenue
    const monthsDiff = Math.max(1, Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24 * 30)));
    const averageMonthly = totalRevenue / monthsDiff;

    return {
      totalRevenue: Math.round(totalRevenue * 100) / 100,
      totalExpenses: Math.round(totalExpenses * 100) / 100,
      netIncome: Math.round(netIncome * 100) / 100,
      revenueByProject,
      revenueByMonth,
      taxSummary: {
        taxableIncome: Math.round(taxableIncome * 100) / 100,
        estimatedTax: Math.round(estimatedTax * 100) / 100,
        deductions: Math.round(deductions * 100) / 100
      },
      summary: {
        period: periodLabel,
        transactions: revenue.length + expenses.length,
        averageMonthly: Math.round(averageMonthly * 100) / 100
      }
    };
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const generateReport = async () => {
    setGeneratingReport(true);
    try {
      // In a real implementation, this would generate and download a PDF report
      // For now, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate processing
      toast.success('Financial report generated successfully!');
      
      // Here you would typically trigger a download
      // const reportData = generatePDFReport(reportData);
      // downloadFile(reportData, `financial-report-${reportPeriod}.pdf`);
      
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate report');
    } finally {
      setGeneratingReport(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading financial reports...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-white mb-4">Error Loading Reports</h2>
            <p className="text-white/70 mb-4">{error}</p>
            <Button onClick={loadReportData} color="primary" variant="bordered">
              Try Again
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center">
            <FileText size={20} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Financial Reports</h2>
            <p className="text-white/60">Comprehensive financial analysis and reporting</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Select
            value={reportPeriod}
            onChange={(e) => setReportPeriod(e.target.value)}
            className="w-40"
            size="sm"
          >
            <SelectItem key="current_month" value="current_month">Current Month</SelectItem>
            <SelectItem key="last_month" value="last_month">Last Month</SelectItem>
            <SelectItem key="current_quarter" value="current_quarter">Current Quarter</SelectItem>
            <SelectItem key="current_year" value="current_year">Current Year</SelectItem>
            <SelectItem key="last_year" value="last_year">Last Year</SelectItem>
          </Select>
          
          <Button
            color="primary"
            startContent={<Download size={18} />}
            onClick={generateReport}
            isLoading={generatingReport}
            disabled={generatingReport}
          >
            {generatingReport ? 'Generating...' : 'Download Report'}
          </Button>
        </div>
      </motion.div>

      {/* Summary Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <DollarSign size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Total Revenue</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(reportData.totalRevenue)}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <TrendingUp size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Net Income</p>
                <p className={`text-2xl font-bold ${reportData.netIncome >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatCurrency(reportData.netIncome)}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Calendar size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Monthly Average</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(reportData.summary.averageMonthly)}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <FileText size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Transactions</p>
                <p className="text-2xl font-bold text-white">{reportData.summary.transactions}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Report Period Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold text-white">
                Financial Summary - {reportData.summary.period}
              </h3>
              <Chip 
                color={reportData.netIncome >= 0 ? 'success' : 'danger'}
                variant="flat"
              >
                {reportData.netIncome >= 0 ? 'Profitable' : 'Loss'}
              </Chip>
            </div>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-3">
                <h4 className="text-white font-medium">Income Statement</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/60">Total Revenue</span>
                    <span className="text-white font-medium">{formatCurrency(reportData.totalRevenue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Total Expenses</span>
                    <span className="text-white font-medium">{formatCurrency(reportData.totalExpenses)}</span>
                  </div>
                  <div className="border-t border-white/20 pt-2">
                    <div className="flex justify-between">
                      <span className="text-white font-medium">Net Income</span>
                      <span className={`font-bold ${reportData.netIncome >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {formatCurrency(reportData.netIncome)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="text-white font-medium">Tax Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/60">Taxable Income</span>
                    <span className="text-white font-medium">{formatCurrency(reportData.taxSummary.taxableIncome)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Estimated Tax (25%)</span>
                    <span className="text-white font-medium">{formatCurrency(reportData.taxSummary.estimatedTax)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Deductions</span>
                    <span className="text-white font-medium">{formatCurrency(reportData.taxSummary.deductions)}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="text-white font-medium">Key Metrics</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/60">Profit Margin</span>
                    <span className="text-white font-medium">
                      {reportData.totalRevenue > 0 
                        ? Math.round((reportData.netIncome / reportData.totalRevenue) * 100)
                        : 0
                      }%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Expense Ratio</span>
                    <span className="text-white font-medium">
                      {reportData.totalRevenue > 0 
                        ? Math.round((reportData.totalExpenses / reportData.totalRevenue) * 100)
                        : 0
                      }%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Revenue Sources</span>
                    <span className="text-white font-medium">{reportData.revenueByProject.length}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Revenue by Project */}
      {reportData.revenueByProject.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <PieChart size={20} className="text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Revenue by Project</h3>
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {reportData.revenueByProject.map((project, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex-1">
                      <p className="text-white font-medium">{project.project}</p>
                      <div className="w-full bg-white/10 rounded-full h-2 mt-2">
                        <div 
                          className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full"
                          style={{ width: `${project.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-4 text-right">
                      <p className="text-white font-bold">{formatCurrency(project.amount)}</p>
                      <p className="text-white/60 text-sm">{project.percentage}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Monthly Revenue Trend */}
      {reportData.revenueByMonth.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Monthly Revenue Trend</h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {reportData.revenueByMonth.map((month, index) => (
                  <div key={index} className="text-center p-3 bg-white/5 rounded-lg">
                    <p className="text-white/60 text-sm">{month.month}</p>
                    <p className="text-white font-bold">{formatCurrency(month.amount)}</p>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default FinancialReports;
