# Project Wizard Agreement Tasks

This document outlines the specific tasks required to update the project wizard to collect all necessary information for generating complete agreements.

## Overview

The project wizard currently collects basic information but is missing several key fields required for generating complete agreements. This document provides a detailed task list for implementing the necessary changes.

## Current Status

The agreement generation system has been significantly enhanced with:

1. ✅ **Template Customization**: Users can now select from multiple agreement templates
2. ✅ **Agreement Validation**: A comprehensive validation system checks for legal completeness
3. ✅ **Project Type Handling**: The system correctly handles different project types
4. ✅ **Version Management**: Agreement versions are properly tracked with dates

However, the project wizard still needs to be updated to collect all necessary information for generating complete agreements. This is the next priority for the agreement generation system.

## Task List

### 1. Update Project Data Structure

- [ ] **Update Project Schema**
  - [ ] Add company information fields (name, address, state, county)
  - [ ] Add project-type-specific fields (engine, platforms, etc.)
  - [ ] Add owner information fields (title, address, state, county)
  - [ ] Add financial parameter fields (min_payout, max_payout, revenue_share)

- [ ] **Update Database Schema**
  - [ ] Create migration for adding new fields to projects table
  - [ ] Create migration for adding new fields to contributors table
  - [ ] Update API endpoints to handle new fields

- [ ] **Update Context Provider**
  - [ ] Update `ProjectWizardContext.jsx` to include new fields
  - [ ] Add validation for new fields
  - [ ] Update default values for new fields

### 2. Update Project Basics Component

- [ ] **Add Company Information Section**
  - [ ] Create collapsible section for company information
  - [ ] Add fields for company name, address, state, and county
  - [ ] Add validation for company information fields
  - [ ] Add help text explaining the importance of this information

- [ ] **Enhance Project Type Selection**
  - [ ] Update project type selection to show/hide fields based on type
  - [ ] Add fields for game projects (engine, platforms)
  - [ ] Add fields for software projects (platforms, technology stack)
  - [ ] Add fields for music projects (genre, distribution platforms)

- [ ] **Improve Date Selection**
  - [ ] Update date selection UI with better labels
  - [ ] Add validation to ensure launch date is after start date
  - [ ] Add estimated duration calculation based on dates
  - [ ] Add help text explaining the importance of accurate dates

### 3. Update Team & Contributors Component

- [ ] **Enhance Owner Information**
  - [ ] Create dedicated section for project owner information
  - [ ] Add fields for owner address, state, county, and title
  - [ ] Add validation to ensure at least one owner is specified
  - [ ] Add help text explaining the importance of owner information

- [ ] **Improve Contributor Information**
  - [ ] Add toggle for contributor type (individual/company)
  - [ ] Add conditional fields based on contributor type
  - [ ] Add field for contributor address
  - [ ] Add validation for contributor information
  - [ ] Add help text explaining the importance of contributor information

### 4. Update Royalty Model Component

- [ ] **Add Financial Parameters Section**
  - [ ] Create section for financial parameters
  - [ ] Add fields for minimum payout threshold, maximum payout, and revenue share percentage
  - [ ] Add validation to ensure values are reasonable
  - [ ] Add help text explaining the impact of these parameters

### 5. Update Milestones Component

- [ ] **Enhance Milestone Information**
  - [ ] Improve UI for adding deliverables to each milestone
  - [ ] Add field for approval criteria
  - [ ] Add validation to ensure milestones have deliverables
  - [ ] Add help text explaining the importance of detailed milestones

### 6. Update Review Agreement Component

- [ ] **Enhance Agreement Preview**
  - [ ] Update preview to show all new fields
  - [ ] Add highlighting for missing information
  - [ ] Add ability to edit missing information directly from preview
  - [ ] Add validation to ensure all required fields are filled

- [ ] **Improve Agreement Generation**
  - [x] Update agreement generation with project type-specific terminology
  - [x] Add template customization with multiple template options
  - [x] Implement agreement validation for legal completeness
  - [x] Fix agreement date update when regenerating
  - [ ] Update agreement generation to use all new fields
  - [ ] Add better error handling for missing information

## Implementation Details

### Project Data Structure Updates

```javascript
// Updated project data structure
const projectData = {
  // Basic Information
  name: "",
  description: "",
  project_type: "",
  thumbnail_url: "",

  // Company Information (New)
  company_name: "",
  company_address: "",
  company_state: "",
  company_county: "",

  // Project Type Specific Information (New)
  engine: "",
  platforms: "",
  genre: "",
  technology_stack: "",
  distribution_platforms: "",

  // Timeline Information
  start_date: null,
  launch_date: null,
  estimated_duration: 0,

  // Contributors
  contributors: [],

  // Owner Information (New)
  owner: {
    id: "",
    display_name: "",
    email: "",
    title: "",
    address: "",
    state: "",
    county: ""
  },

  // Royalty Model
  royalty_model: {
    model_type: "",
    model_schema: "",
    configuration: {},
    is_pre_expense: true,
    contributor_percentage: 50,

    // Financial Parameters (New)
    min_payout: 0,
    max_payout: 0,
    revenue_share: 0
  },

  // Milestones
  milestones: []
};
```

### Database Migration Example

```sql
-- Add new fields to projects table
ALTER TABLE projects
ADD COLUMN company_name TEXT,
ADD COLUMN company_address TEXT,
ADD COLUMN company_state TEXT,
ADD COLUMN company_county TEXT,
ADD COLUMN engine TEXT,
ADD COLUMN platforms TEXT,
ADD COLUMN genre TEXT,
ADD COLUMN technology_stack TEXT,
ADD COLUMN distribution_platforms TEXT;

-- Add new fields to royalty_models table
ALTER TABLE royalty_models
ADD COLUMN min_payout INTEGER DEFAULT 0,
ADD COLUMN max_payout INTEGER DEFAULT 0,
ADD COLUMN revenue_share INTEGER DEFAULT 50;

-- Add new fields to contributors table
ALTER TABLE contributors
ADD COLUMN title TEXT,
ADD COLUMN address TEXT,
ADD COLUMN state TEXT,
ADD COLUMN county TEXT,
ADD COLUMN is_company BOOLEAN DEFAULT FALSE,
ADD COLUMN company_name TEXT,
ADD COLUMN signer_name TEXT,
ADD COLUMN signer_title TEXT;
```

## UI Mockups

### Company Information Section

```
+-----------------------------------------------+
| Company Information                           |
+-----------------------------------------------+
| Company Name:                                 |
| [                                           ] |
|                                               |
| Company Address:                              |
| [                                           ] |
|                                               |
| State:                      County:           |
| [                    ]      [               ] |
+-----------------------------------------------+
```

### Contributor Type Toggle

```
+-----------------------------------------------+
| Contributor Type:                             |
|                                               |
| (O) Individual   ( ) Company                  |
|                                               |
| Name:                                         |
| [                                           ] |
|                                               |
| Email:                                        |
| [                                           ] |
|                                               |
| Address:                                      |
| [                                           ] |
+-----------------------------------------------+

+-----------------------------------------------+
| Contributor Type:                             |
|                                               |
| ( ) Individual   (O) Company                  |
|                                               |
| Company Name:                                 |
| [                                           ] |
|                                               |
| Signer Name:                                  |
| [                                           ] |
|                                               |
| Signer Title:                                 |
| [                                           ] |
|                                               |
| Company Address:                              |
| [                                           ] |
+-----------------------------------------------+
```

## Timeline

1. **Week 1**: Update project data structure and database schema
2. **Week 2**: Update Project Basics and Team & Contributors components
3. **Week 3**: Update Royalty Model and Milestones components
4. **Week 4**: Update Review Agreement component and test end-to-end

## Conclusion

Implementing these changes will ensure that the project wizard collects all the necessary information for generating complete agreements. This will improve the user experience and reduce the need for manual editing of agreements after generation.
