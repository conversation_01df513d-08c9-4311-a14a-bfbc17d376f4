#!/usr/bin/env node

/**
 * Debug Authentication Inconsistency
 * 
 * Investigates why some tests work with authentication while others don't
 */

const { chromium } = require('playwright');

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

async function debugAuthInconsistency() {
  console.log('🔍 DEBUGGING AUTHENTICATION INCONSISTENCY');
  console.log('='.repeat(60));
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Test 1: Basic authentication (like the working test)
    console.log('\n📍 TEST 1: Basic Authentication Flow');
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const needsAuth1 = await page.locator('input[type="email"]').isVisible();
    console.log(`   Login required: ${needsAuth1}`);
    
    if (needsAuth1) {
      console.log('   🔐 Performing authentication...');
      await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
      await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      
      // Wait extra time for auth to settle
      await page.waitForTimeout(3000);
      
      const homeContent = await page.textContent('body');
      const homeUrl = page.url();
      const gridTiles = await page.locator('[data-canvas-card]').count();
      
      console.log(`   📄 Home URL: ${homeUrl}`);
      console.log(`   📄 Content Length: ${homeContent?.length}`);
      console.log(`   🎯 Grid tiles: ${gridTiles}`);
      console.log(`   📄 Content Preview: ${homeContent?.substring(0, 200)}...`);
      
      const isStillLogin = homeContent?.includes('Welcome Back') && homeContent?.includes('Sign in to continue');
      console.log(`   🔐 Still showing login: ${isStillLogin}`);
    }
    
    // Test 2: Navigate to /start (the known working route)
    console.log('\n📍 TEST 2: Navigate to /start');
    await page.goto(`${PRODUCTION_URL}/start`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const startContent = await page.textContent('body');
    const startUrl = page.url();
    
    console.log(`   📄 Start URL: ${startUrl}`);
    console.log(`   📄 Content Length: ${startContent?.length}`);
    console.log(`   📄 Content Preview: ${startContent?.substring(0, 200)}...`);
    
    const isStartLogin = startContent?.includes('Welcome Back') && startContent?.includes('Sign in to continue');
    console.log(`   🔐 Showing login: ${isStartLogin}`);
    
    // Test 3: Check cookies and storage
    console.log('\n📍 TEST 3: Check Authentication State');
    
    const cookies = await page.context().cookies();
    console.log(`   🍪 Cookies count: ${cookies.length}`);
    cookies.forEach(cookie => {
      if (cookie.name.includes('auth') || cookie.name.includes('token') || cookie.name.includes('session') || cookie.name.includes('sb-')) {
        console.log(`     - ${cookie.name}: ${cookie.value.substring(0, 50)}...`);
      }
    });
    
    const localStorage = await page.evaluate(() => {
      const items = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('auth') || key.includes('token') || key.includes('supabase') || key.includes('sb-'))) {
          items[key] = localStorage.getItem(key)?.substring(0, 100);
        }
      }
      return items;
    });
    
    console.log(`   💾 LocalStorage auth items:`, Object.keys(localStorage));
    
    // Test 4: Try different navigation approaches
    console.log('\n📍 TEST 4: Different Navigation Approaches');
    
    // Approach A: Direct navigation
    console.log('   🔄 Approach A: Direct navigation to /track');
    await page.goto(`${PRODUCTION_URL}/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const trackContent = await page.textContent('body');
    const isTrackLogin = trackContent?.includes('Welcome Back') && trackContent?.includes('Sign in to continue');
    console.log(`     Content Length: ${trackContent?.length}`);
    console.log(`     Showing login: ${isTrackLogin}`);
    
    // Approach B: Go back to home first, then navigate
    console.log('   🔄 Approach B: Home first, then navigate to /earn');
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    await page.goto(`${PRODUCTION_URL}/earn`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const earnContent = await page.textContent('body');
    const isEarnLogin = earnContent?.includes('Welcome Back') && earnContent?.includes('Sign in to continue');
    console.log(`     Content Length: ${earnContent?.length}`);
    console.log(`     Showing login: ${isEarnLogin}`);
    
    // Approach C: Click grid tile navigation
    console.log('   🔄 Approach C: Grid tile navigation');
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const tiles = await page.locator('[data-canvas-card]').all();
    console.log(`     Grid tiles found: ${tiles.length}`);
    
    if (tiles.length > 0) {
      // Try clicking the first tile
      const firstTile = tiles[0];
      const tileText = await firstTile.textContent();
      console.log(`     Clicking tile: ${tileText?.substring(0, 50)}...`);
      
      await firstTile.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      const tileNavContent = await page.textContent('body');
      const tileNavUrl = page.url();
      const isTileNavLogin = tileNavContent?.includes('Welcome Back') && tileNavContent?.includes('Sign in to continue');
      
      console.log(`     Result URL: ${tileNavUrl}`);
      console.log(`     Content Length: ${tileNavContent?.length}`);
      console.log(`     Showing login: ${isTileNavLogin}`);
    }
    
    // Test 5: Check for any JavaScript errors
    console.log('\n📍 TEST 5: JavaScript Error Detection');
    
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    // Navigate to a few pages to trigger any errors
    await page.goto(`${PRODUCTION_URL}/projects`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    console.log(`   JavaScript errors detected: ${errors.length}`);
    if (errors.length > 0) {
      errors.forEach((error, index) => {
        console.log(`     ${index + 1}. ${error}`);
      });
    }
    
    // Test 6: Check network requests
    console.log('\n📍 TEST 6: Network Request Analysis');
    
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('auth') || request.url().includes('login') || request.url().includes('supabase')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: Object.keys(request.headers())
        });
      }
    });
    
    await page.goto(`${PRODUCTION_URL}/settings`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    console.log(`   Auth-related requests: ${requests.length}`);
    requests.forEach((req, index) => {
      console.log(`     ${index + 1}. ${req.method} ${req.url}`);
    });
    
    // Final summary
    console.log('\n📊 AUTHENTICATION INCONSISTENCY ANALYSIS');
    console.log('='.repeat(60));
    
    const finalContent = await page.textContent('body');
    const finalUrl = page.url();
    const isFinalLogin = finalContent?.includes('Welcome Back') && finalContent?.includes('Sign in to continue');
    
    console.log(`Final URL: ${finalUrl}`);
    console.log(`Final content length: ${finalContent?.length}`);
    console.log(`Showing login page: ${isFinalLogin}`);
    console.log(`Cookies present: ${cookies.length > 0}`);
    console.log(`LocalStorage auth: ${Object.keys(localStorage).length > 0}`);
    console.log(`JavaScript errors: ${errors.length}`);
    console.log(`Auth requests: ${requests.length}`);
    
    if (isFinalLogin) {
      console.log('\n❌ AUTHENTICATION IS FAILING');
      console.log('Possible causes:');
      console.log('1. Session timeout or expiration');
      console.log('2. Authentication state not persisting between navigations');
      console.log('3. Different browser context/session handling');
      console.log('4. Server-side authentication validation issues');
      console.log('5. JavaScript errors preventing proper auth state management');
    } else {
      console.log('\n✅ AUTHENTICATION IS WORKING');
      console.log('The inconsistency might be related to:');
      console.log('1. Timing issues in automated tests');
      console.log('2. Playwright configuration differences');
      console.log('3. Parallel test execution conflicts');
    }
    
  } catch (error) {
    console.error('❌ Error during debug:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugAuthInconsistency().catch(error => {
  console.error('❌ Failed to run debug:', error);
  process.exit(1);
});
