/**
 * Standalone test for the improved agreement generator
 * 
 * This script tests the improved agreement generator directly without
 * relying on the application code.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a simple mock for templateManager
const templateManager = {
  loadTemplate: async () => {
    return templateText;
  }
};

// Import the ImprovedAgreementGenerator class
class ImprovedAgreementGenerator {
  constructor() {
    // Today's date as default
    const today = new Date();
    this.currentDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  async loadTemplate() {
    return templateText;
  }
}

// Test data for different project types
const testProjects = [
  {
    name: "Dream Game",
    title: "Dream Game",
    description: "a collaborative game development project",
    project_type: "game",
    projectType: "game",
    is_public: true,
    platforms: "PC (Windows, macOS)",
    engine: "Unreal Engine 5",
    id: "game-test-1"
  },
  {
    name: "Dream Software",
    title: "Dream Software",
    description: "a collaborative software development project",
    project_type: "software",
    projectType: "software",
    is_public: true,
    platforms: "Web, Desktop (Windows, macOS)",
    engine: "React, Node.js",
    id: "software-test-1"
  },
  {
    name: "Dream Music",
    title: "Dream Music",
    description: "a collaborative music production project",
    project_type: "music",
    projectType: "music",
    is_public: true,
    platforms: "Digital streaming platforms",
    engine: "Pro Tools, Logic Pro",
    id: "music-test-1"
  }
];

// Test options
const testOptions = {
  contributors: [
    {
      permission_level: 'Owner',
      display_name: 'Gynell Journigan',
      company_name: 'Gynell Journigan LLC',
      address: '123 Business St, Suite 100, Wilmington, DE 19801',
      state: 'Delaware',
      city: 'Wilmington',
      county: 'New Castle County',
      email: '<EMAIL>',
      title: 'President'
    }
  ],
  currentUser: {
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Gynell Journigan'
    }
  },
  fullName: 'Gynell Journigan'
};

// Import the actual implementation
import('../src/utils/agreement/improvedAgreementGenerator.js')
  .then(module => {
    // Copy all prototype methods from the actual implementation
    Object.getOwnPropertyNames(module.ImprovedAgreementGenerator.prototype)
      .filter(name => name !== 'constructor' && name !== 'loadTemplate')
      .forEach(name => {
        ImprovedAgreementGenerator.prototype[name] = module.ImprovedAgreementGenerator.prototype[name];
      });
    console.log('Successfully imported ImprovedAgreementGenerator methods');
    
    // Run tests
    runTests();
  })
  .catch(error => {
    console.error('Error importing ImprovedAgreementGenerator:', error);
  });

// Run tests for each project type
function runTests() {
  console.log('🚀 Testing improved agreement generator...');

  for (const project of testProjects) {
    try {
      console.log(`Generating agreement for: ${project.name} (${project.project_type})`);
      
      // Create a new generator instance
      const generator = new ImprovedAgreementGenerator();
      
      // Generate the agreement
      const agreement = generator._processTemplate(
        templateText,
        project,
        {
          name: 'Gynell Journigan LLC',
          address: '123 Business St, Suite 100, Wilmington, DE 19801',
          state: 'Delaware',
          city: 'Wilmington',
          county: 'New Castle County',
          billingEmail: '<EMAIL>',
          signerName: 'Gynell Journigan',
          signerTitle: 'President',
          projectType: project.project_type
        },
        {
          name: 'Gynell Journigan',
          email: '<EMAIL>',
          isCompany: false
        },
        null, // royaltyModel
        [] // milestones
      );
      
      // Clean up the agreement
      const cleanedAgreement = generator._cleanupAgreementText(agreement);
      
      // Save the agreement to a file
      const outputPath = path.join(outputDir, `standalone-${project.project_type}-agreement.md`);
      fs.writeFileSync(outputPath, cleanedAgreement, 'utf8');
      
      console.log(`✅ Agreement saved to: ${outputPath}`);
      
      // Check for project-specific content
      const projectTypeCheck = checkProjectTypeContent(cleanedAgreement, project.project_type);
      console.log(`Project type check: ${projectTypeCheck ? '✅ Passed' : '❌ Failed'}`);
      
      // Check for company information
      const companyCheck = checkCompanyInfo(cleanedAgreement);
      console.log(`Company info check: ${companyCheck ? '✅ Passed' : '❌ Failed'}`);
      
      // Check for date formatting
      const dateCheck = checkDateFormatting(cleanedAgreement);
      console.log(`Date formatting check: ${dateCheck ? '✅ Passed' : '❌ Failed'}`);
      
      console.log('-----------------------------------');
    } catch (error) {
      console.error(`❌ Error generating agreement for ${project.name}:`, error);
    }
  }
}

/**
 * Check if the agreement contains appropriate content for the project type
 * @param {string} agreement - The generated agreement
 * @param {string} projectType - The project type
 * @returns {boolean} - Whether the check passed
 */
function checkProjectTypeContent(agreement, projectType) {
  // Check for project type specific content
  switch (projectType) {
    case 'game':
      return agreement.includes('game development') || 
             agreement.includes('Game development') || 
             agreement.includes('Core Gameplay');
    case 'software':
      return agreement.includes('software development') || 
             agreement.includes('Software development') || 
             agreement.includes('Core Functionality');
    case 'music':
      return agreement.includes('music production') || 
             agreement.includes('Music production') || 
             agreement.includes('Composition');
    default:
      return false;
  }
}

/**
 * Check if the agreement contains proper company information
 * @param {string} agreement - The generated agreement
 * @returns {boolean} - Whether the check passed
 */
function checkCompanyInfo(agreement) {
  return agreement.includes('Gynell Journigan LLC') && 
         agreement.includes('Delaware corporation') &&
         agreement.includes('New Castle County, Delaware');
}

/**
 * Check if the agreement has proper date formatting
 * @param {string} agreement - The generated agreement
 * @returns {boolean} - Whether the check passed
 */
function checkDateFormatting(agreement) {
  // Get today's date in the format used in the agreement
  const today = new Date();
  const formattedDate = today.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
  
  return agreement.includes(formattedDate);
}
