import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Avatar, Progress } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { FolderOpen, Plus, Users, Calendar, Target, TrendingUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * Project List Section
 * 
 * Displays user's projects including:
 * - Active and completed projects
 * - Project progress and metrics
 * - Team member information
 * - Quick actions and navigation
 */
const ProjectList = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [projects, setProjects] = useState([]);
  const [projectStats, setProjectStats] = useState({
    total: 0,
    active: 0,
    completed: 0,
    totalHours: 0
  });

  // Load user's projects
  useEffect(() => {
    if (currentUser) {
      loadProjects();
    }
  }, [currentUser]);

  const loadProjects = async () => {
    try {
      setLoading(true);

      // Load projects where user is a member
      const { data: projectMembers, error: membersError } = await supabase
        .from('project_members')
        .select(`
          project_id,
          role,
          joined_at,
          projects (
            id,
            name,
            description,
            status,
            created_at,
            target_completion_date,
            estimated_revenue,
            project_type
          )
        `)
        .eq('user_id', currentUser.id);

      if (membersError) throw membersError;

      // Get project IDs for additional data
      const projectIds = projectMembers?.map(pm => pm.project_id) || [];

      if (projectIds.length === 0) {
        setProjects([]);
        setProjectStats({ total: 0, active: 0, completed: 0, totalHours: 0 });
        return;
      }

      // Load project tasks for progress calculation
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select('id, project_id, status')
        .in('project_id', projectIds);

      if (tasksError) throw tasksError;

      // Load project contributions for hours calculation
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select('project_id, hours_logged')
        .in('project_id', projectIds);

      if (contributionsError) throw contributionsError;

      // Load team members for each project
      const { data: allMembers, error: allMembersError } = await supabase
        .from('project_members')
        .select(`
          project_id,
          user_id,
          role,
          users (
            id,
            display_name,
            avatar_url
          )
        `)
        .in('project_id', projectIds);

      if (allMembersError) throw allMembersError;

      // Process project data
      const processedProjects = projectMembers.map(member => {
        const project = member.projects;
        const projectTasks = tasks?.filter(t => t.project_id === project.id) || [];
        const completedTasks = projectTasks.filter(t => t.status === 'Done' || t.status === 'completed');
        const projectContributions = contributions?.filter(c => c.project_id === project.id) || [];
        const projectTeam = allMembers?.filter(m => m.project_id === project.id) || [];
        
        const totalHours = projectContributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0);
        const progress = projectTasks.length > 0 
          ? Math.round((completedTasks.length / projectTasks.length) * 100)
          : 0;

        return {
          ...project,
          userRole: member.role,
          joinedAt: member.joined_at,
          totalTasks: projectTasks.length,
          completedTasks: completedTasks.length,
          progress,
          totalHours: Math.round(totalHours * 100) / 100,
          teamMembers: projectTeam.slice(0, 5), // Show first 5 members
          totalMembers: projectTeam.length
        };
      });

      setProjects(processedProjects);

      // Calculate stats
      const stats = {
        total: processedProjects.length,
        active: processedProjects.filter(p => p.status === 'active').length,
        completed: processedProjects.filter(p => p.status === 'completed').length,
        totalHours: processedProjects.reduce((sum, p) => sum + p.totalHours, 0)
      };
      setProjectStats(stats);

    } catch (error) {
      console.error('Error loading projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'completed':
        return 'primary';
      case 'on_hold':
        return 'warning';
      case 'cancelled':
        return 'danger';
      default:
        return 'default';
    }
  };

  const getRoleColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'admin':
      case 'owner':
        return 'danger';
      case 'manager':
      case 'lead':
        return 'warning';
      case 'developer':
      case 'contributor':
        return 'primary';
      default:
        return 'default';
    }
  };

  const formatHours = (hours) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${Math.round(hours * 10) / 10}h`;
  };

  const handleProjectClick = (projectId) => {
    navigate(`/project/${projectId}`);
  };

  const handleCreateProject = () => {
    navigate('/project/create');
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading projects...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center">
            <FolderOpen size={20} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">My Projects</h2>
            <p className="text-white/60">Manage and track your project portfolio</p>
          </div>
        </div>
        
        <Button
          color="primary"
          startContent={<Plus size={18} />}
          onClick={handleCreateProject}
        >
          Create Project
        </Button>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <FolderOpen size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Total Projects</p>
                <p className="text-2xl font-bold text-white">{projectStats.total}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Target size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Active Projects</p>
                <p className="text-2xl font-bold text-white">{projectStats.active}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <TrendingUp size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Completed</p>
                <p className="text-2xl font-bold text-white">{projectStats.completed}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Calendar size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Total Hours</p>
                <p className="text-2xl font-bold text-white">{formatHours(projectStats.totalHours)}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Projects Grid */}
      {projects.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardBody className="p-12 text-center">
              <FolderOpen size={64} className="text-white/30 mx-auto mb-6" />
              <h3 className="text-xl font-bold text-white mb-4">No Projects Yet</h3>
              <p className="text-white/60 mb-6">
                Create your first project to start tracking contributions and collaborating with your team.
              </p>
              <Button
                color="primary"
                size="lg"
                startContent={<Plus size={20} />}
                onClick={handleCreateProject}
              >
                Create Your First Project
              </Button>
            </CardBody>
          </Card>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="cursor-pointer"
              onClick={() => handleProjectClick(project.id)}
            >
              <Card className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all h-full">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center">
                        <FolderOpen size={20} className="text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-white">{project.name}</h3>
                        <div className="flex items-center gap-2">
                          <Chip 
                            size="sm" 
                            color={getStatusColor(project.status)}
                            variant="flat"
                          >
                            {project.status}
                          </Chip>
                          <Chip 
                            size="sm" 
                            color={getRoleColor(project.userRole)}
                            variant="flat"
                          >
                            {project.userRole}
                          </Chip>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardBody className="pt-0">
                  <p className="text-white/70 text-sm mb-4 line-clamp-2">
                    {project.description || 'No description available'}
                  </p>

                  {/* Progress */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/60 text-sm">Progress</span>
                      <span className="text-white font-medium">{project.progress}%</span>
                    </div>
                    <Progress 
                      value={project.progress} 
                      color="primary"
                      size="sm"
                    />
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-white font-bold">{project.completedTasks}/{project.totalTasks}</p>
                      <p className="text-white/60 text-xs">Tasks</p>
                    </div>
                    <div className="text-center">
                      <p className="text-white font-bold">{formatHours(project.totalHours)}</p>
                      <p className="text-white/60 text-xs">Hours</p>
                    </div>
                  </div>

                  {/* Team Members */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users size={16} className="text-white/60" />
                      <span className="text-white/60 text-sm">{project.totalMembers} members</span>
                    </div>
                    <div className="flex -space-x-2">
                      {project.teamMembers.map((member, idx) => (
                        <Avatar
                          key={member.user_id}
                          src={member.users?.avatar_url}
                          name={member.users?.display_name}
                          size="sm"
                          className="border-2 border-white/20"
                        />
                      ))}
                      {project.totalMembers > 5 && (
                        <div className="w-8 h-8 rounded-full bg-white/20 border-2 border-white/20 flex items-center justify-center">
                          <span className="text-white text-xs">+{project.totalMembers - 5}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Dates */}
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <div className="flex items-center justify-between text-xs text-white/60">
                      <span>Created: {new Date(project.created_at).toLocaleDateString()}</span>
                      {project.target_completion_date && (
                        <span>Due: {new Date(project.target_completion_date).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}
    </div>
  );
};

export default ProjectList;
