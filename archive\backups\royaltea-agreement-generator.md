# Royaltea Agreement Generator System

## Overview

This document provides a comprehensive system for generating customized legal agreements for the Royaltea platform. It addresses issues with variable replacement, exhibits, and placeholder information to ensure agreements are properly formatted and legally sound regardless of the project type or input provided by the project creator.

## Current Status

The agreement generator has been significantly enhanced with the following features:

1. **Project Type Handling**: The system now correctly handles different project types with appropriate terminology replacements.
2. **Template Customization**: Users can select from multiple agreement templates (standard, simplified, detailed).
3. **Agreement Validation**: A comprehensive validation system checks for legal completeness and correctness.
4. **Version Management**: Agreement versions are properly tracked with dates updated on regeneration.

Remaining work includes:
1. Collecting all necessary information through the project wizard
2. Expanding the template library with more specialized templates
3. Enhancing the validation system with more project-specific rules
4. Improving error handling for edge cases

## Template Structure

The agreement follows this structure:
1. Header and identification
2. Recitals
3. Core agreement sections (Definitions, Confidentiality, etc.)
4. Signature blocks
5. Schedule A (Services description)
6. Schedule B (Compensation details)
7. Exhibit I (Project specifications)
8. Exhibit II (Project roadmap)

## Required Input Fields

### Project Creator Information
| Field | Required | Placeholder | Notes |
|-------|----------|-------------|-------|
| `creatorName` | Yes | "[Project Owner]" | Legal entity or individual name |
| `creatorAddress` | Yes | "[Project Owner Address]" | Full address |
| `creatorState` | Yes | "[State of Incorporation]" | State of incorporation if entity |
| `creatorCounty` | Yes | "[County]" | For jurisdiction purposes |
| `billingEmail` | Yes | "[Project Owner Email]" | Contact for invoices |
| `signerName` | Yes | "[Project Owner]" | Name of authorized signer |
| `signerTitle` | Yes | "[Project Owner]" | Title of authorized signer |

### Contributor Information
| Field | Required | Placeholder | Notes |
|-------|----------|-------------|-------|
| `contributorName` | Yes | "[Contributor]" | Full legal name |
| `contributorAddress` | Yes | "[Contributor Address]" | Full address |
| `isCompanyContributor` | Yes | false | Whether contributor is a company (true) or individual (false) |
| `contributorCompanyName` | If company | "[Contributor Company]" | Only required if isCompanyContributor=true |
| `contributorSignerName` | If company | "[Contributor Representative]" | Only required if isCompanyContributor=true |
| `contributorSignerTitle` | If company | "[Contributor Title]" | Only required if isCompanyContributor=true |
| `effectiveDate` | Yes | Current date (MM/DD/YYYY) | Agreement start date |

### Project Details
| Field | Required | Placeholder | Smart Inference |
|-------|----------|-------------|----------------|
| `projectName` | Yes | "[Project Title]" | No smart inference |
| `projectType` | Yes | "creative work" | Infer from description if possible |
| `projectDescription` | Yes | "a collaborative project" | No smart inference |
| `projectLength` | Yes | "medium" | "short" (1-2 months), "medium" (3-6 months), "long" (7+ months) |
| `durationMonths` | Yes | 4 | Infer from projectLength if not specified |
| `platforms` | No | Infer from project type | Games: "PC (Steam, Epic Games Store)" |
| `engine` | No | Infer from project type | Games: "Unreal Engine 5" |

### Financial Terms
| Field | Required | Fallback | Smart Inference |
|-------|----------|----------|----------------|
| `revenueShare` | Yes | Infer from project type | Games: "33%", Software: "25%", Art: "40%", Music: "50%" |
| `payoutThreshold` | Yes | Infer from project type | Games: "$100,000", Software: "$50,000", Music: "$500" |
| `maxIndividualPayment` | Yes | Infer from project type | Games: "$1,000,000", Software: "$500,000", Music: "$50,000" |
| `includeSequelRights` | No | true | Include sequel rights section if true |

### Milestones & Roadmap
| Field | Required | Fallback | Notes |
|-------|----------|----------|-------|
| `milestones` | No | Generate based on project type and duration | Array of milestone objects |
| `features` | No | Generate based on project type | Core features and technical requirements |

## Smart Inference Rules

### Project Type Inference
- If description contains "game", "player", "level" → projectType = "game"
- If description contains "app", "website", "platform" → projectType = "software"
- If description contains "song", "music", "audio" → projectType = "music"
- If description contains "art", "design", "visual" → projectType = "art"
- Default: "creative work"

### Duration Inference
- If projectLength = "short" → durationMonths = 1-2
- If projectLength = "medium" → durationMonths = 3-6
- If projectLength = "long" → durationMonths = 7-12

### Project-Specific Defaults

#### Game Projects
- Default platforms: "PC (Steam, Epic Games Store)"
- Default engine: "Unreal Engine 5"
- Default revenueShare: "33%"
- Default payoutThreshold: "$100,000"
- Default maxIndividualPayment: "$1,000,000"
- Default features: Village building, resource management, etc.

#### Software Projects
- Default platforms: "Web, Mobile"
- Default engine: "Custom Framework"
- Default revenueShare: "25%"
- Default payoutThreshold: "$50,000"
- Default maxIndividualPayment: "$500,000"
- Default features: Core functionality, user experience, etc.

#### Music Projects
- Default platforms: "Streaming Services"
- Default revenueShare: "50%"
- Default payoutThreshold: "$500"
- Default maxIndividualPayment: "$50,000"
- Default features: Composition, production, etc.

## Milestone Generation Logic

If milestones are not provided, generate based on project duration:

### Short Project (1-2 months)
Generate weekly milestones:
```javascript
[
  {
    name: "Initial Setup",
    deadline: "End of Week 1",
    description: [
      "Project requirements finalized",
      "Basic framework established",
      "Initial designs approved"
    ]
  },
  {
    name: "Core Development",
    deadline: "End of Week 2",
    description: [
      "Primary functionality implemented",
      "Key components developed",
      "First review completed"
    ]
  },
  // Additional weekly milestones as needed
]
```

### Medium Project (3-6 months)
Generate monthly milestones:
```javascript
[
  {
    name: "Foundation",
    deadline: "End of Month 1",
    description: [
      "Core systems functional",
      "Basic implementation complete",
      "First prototype delivered"
    ]
  },
  // Additional monthly milestones
]
```

### Long Project (7+ months)
Generate quarterly milestones:
```javascript
[
  {
    name: "Initial Quarter",
    deadline: "End of Month 3",
    description: [
      "Project foundation established",
      "Core components implemented",
      "Initial testing completed"
    ]
  },
  // Additional quarterly milestones
]
```

## Features Generation Logic

If features are not provided, generate based on project type:

### Game Projects
```javascript
{
  core: [
    {
      name: "Core Gameplay",
      items: [
        "Main gameplay loop",
        "Player interaction mechanics",
        "Progression system",
        "Difficulty scaling"
      ]
    },
    // Additional feature categories
  ],
  technical: [
    "Platform: [Platforms]",
    "Engine: [Engine]",
    "Minimum Specs: [To be detailed in technical documentation]",
    "Art Style: [Describe art style]",
    "Audio: [Describe audio requirements]"
  ]
}
```

### Software Projects
```javascript
{
  core: [
    {
      name: "Functionality",
      items: [
        "Core application features",
        "User authentication system",
        "Data management",
        "Integration capabilities"
      ]
    },
    // Additional feature categories
  ],
  technical: [
    "Platform: [Platform]",
    "Framework: [Framework]",
    "Database: [Database]",
    "APIs: [Required APIs]",
    "Deployment: [Deployment environment]"
  ]
}
```

## Implementation Guidelines

1. **Variable Replacement**:
   - Use a consistent variable format (e.g., `{{variableName}}`) throughout the template
   - Clearly identify missing information with appropriate placeholders
   - Always check if a variable exists before attempting replacement
   - Apply appropriate project-specific defaults when appropriate

2. **Field Collection Process**:
   - Create a step-by-step wizard for project creators to enter information
   - Clearly mark required fields vs. optional fields
   - Provide descriptive tooltips explaining each field's purpose
   - Save partial progress to allow creators to complete forms over multiple sessions

3. **Project-Specific Content**:
   - Categorize projects (game, software, music, etc.) and customize exhibits accordingly
   - Show previews of the agreement so creators can see how information is formatted
   - Allow creators to edit auto-generated sections (milestones, features) as needed

4. **User Experience**:
   - Highlight missing required fields with clear visual indicators
   - Provide inline guidance for complex fields (e.g., milestone creation)
   - Allow for previewing the complete agreement before finalization
   - Create a review process for both project creator and contributor

5. **Error Handling**:
   - Implement clear validation for all fields with specific error messages
   - Prevent agreement generation until all required fields are completed
   - Log any system errors without exposing technical details to users

## Technical Implementation

### 1. Template System

The system uses a flexible templating approach with:
- ✅ Variable replacement with smart fallbacks
- ✅ Multiple template options (standard, simplified, detailed)
- ✅ Project-specific terminology replacements
- ✅ Template manager for loading and managing templates

### 2. Variable Processing

The system processes variables in this order:
1. ✅ User-provided values from project data
2. ✅ Smart inferred values based on project type
3. ✅ Project-specific defaults for missing information
4. ✅ Generic placeholders for completely missing data

### 3. Milestone and Feature Generation

The system includes:
- ✅ Project type-specific exhibit generation
- ✅ Appropriate technical requirements based on project type
- ✅ Milestone formatting with proper dates and descriptions
- ✅ Fallback content for missing milestones

### 4. Document Structure Enforcement

The system ensures:
- ✅ Consistent section formatting
- ✅ Proper spacing and indentation
- ✅ Correct numbering for lists and sections
- ✅ Complete structure even with minimal input

### 5. Validation and Quality Control

The system includes:
- ✅ Comprehensive validation for legal completeness
- ✅ Project-specific validation rules
- ✅ Improvement suggestions for incomplete agreements
- ✅ Validation UI with detailed feedback

### 6. Preview and Export Functionality

The system provides:
- ✅ Preview of the complete agreement before finalizing
- ✅ PDF export with proper formatting
- ✅ Version tracking with proper date management
- ✅ Digital signature support
```

## Document Structure Example

### Main Agreement Template
```
PROJECT OWNER

CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of {{effectiveDate}}, by and between {{creatorName}} (the "Company") and {{contributorName}} (the "Contributor").

Recitals

WHEREAS, the Company desires to procure services of the Contributor, and the Contributor is willing to provide services to the Company, specifically as provided on Schedule A to this Agreement (the "Services") for the consideration as provided on Schedule B to this Agreement (the "Consideration"), and the Company has asked the Contributor to enter into this Agreement as a part of such arrangement;

...

[rest of agreement sections]

...

IN WITNESS WHEREOF, this Agreement has been executed by the parties as of the date set forth above.

COMPANY:
{{creatorName}}

By: ______________________
Name: {{signerName}}
Title: {{signerTitle}}
Date: ______________________

CONTRIBUTOR:
{{contributorSignatureBlock}}

SCHEDULE A
...

SCHEDULE B
...

EXHIBIT I
...

EXHIBIT II
...
```

### Handling Exhibits and Schedules

The most challenging parts of the agreement are the Schedules and Exhibits, which need substantial default content when not provided by the project creator.

### Schedule A (Services Description)

For Schedule A, use this template structure:

```
SCHEDULE A

Description of Services

This project involves development work on "{{projectName}}," {{projectDescription}}.

1. Services
   a. General. Pursuant to the terms and conditions of this Agreement and subject to Company's acceptance, Contributor shall:
      i. develop the Work Product following the requirements and technical specifications set forth in Exhibit I and in accordance with the roadmap set forth in Exhibit II and Good Industry Practice ("Developing Services"); and
      ii. provide the Support Services in accordance with Good Industry Practice.

   b. Performance. Contributor understands and agrees that Contributor is solely responsible for the control and supervision of the means by which the Services are provided consistent with the goal of successfully completing the Services on time. Contributor shall allocate sufficient resources to ensure that it performs its Services under this Agreement in accordance with the roadmap in Exhibit II and in accordance with Good Industry Practice.

   c. Co-operation.
      i. During the period from the Effective Date until Launch, unless directed otherwise by Company, Contributor shall attend at least weekly calls with Company, with frequency increasing during crunch periods as needed, and provide builds (application versions) every two (2) weeks with Company.
      ii. The Parties shall share responsibility for Work Product Management as agreed between the Parties from time to time and as outlined in Exhibit II. If the Parties are unable to reach agreement in respect of a decision in relation to Work Product Management, Company's decision shall prevail.
      iii. If there is additional need for development that is not in the agreed roadmap the Parties will negotiate in good faith the timeline for Contributor to deliver such further development. The Parties will also negotiate in good faith the costs to deliver such further development.

   d. Cessation of Services following Launch & Right of First Refusal.
      i. Following Launch, Company may determine that it no longer wishes to receive the Services and Contributor may determine that it no longer wishes to provide the Services, each at its sole discretion. In such case the relevant Party will notify the other Party in writing.
      ii. If Contributor notifies Company that it no longer wishes to provide Services to Company, Contributor's obligations to provide the Services will cease within 7 (seven) days of that notice (or such other period as agreed by the Parties) and this Agreement shall terminate automatically.
      iii. If Company intends to continue development of the Work Product after Contributor ceases providing Services, Company may notify Contributor in writing, and both Parties may negotiate in good faith the terms for such further development.

2. Approval.
   a. When Contributor considers that it has progressed the Work Product such that it reaches a Milestone, the Work Product shall be submitted to Company for written approval ("Approval"). Company will assess and/or test each delivered Milestone and will notify Contributor if it is accepted or rejected within 7 (seven) business days after receipt, though the parties acknowledge that in practice, feedback will typically be provided as soon as possible as determined by the development team. In case the Work Product does not, in Company's reasonable opinion, satisfy the Milestone or otherwise meet the technical specifications set forth in Exhibit I, Contributor will, at Company's request, promptly repair or redo the Services wholly or in part, with an initial response time of 7 (seven) days from the date on which feedback is received. For complex issues or features that reasonably require additional time, Contributor shall demonstrate meaningful progress during weekly meetings until the issue is resolved to Company's satisfaction.

   b. Notwithstanding Section 2(a), if Company fails to notify Contributor that the Milestone is rejected within 7 (seven) days of its submission it shall be deemed approved. In case of deemed acceptance, Company shall still be entitled to provide feedback on the delivered Milestone and Contributor shall promptly repair or redo the Services wholly or in part, whenever the Services are deemed to be incomplete or not in conformity with the provisions of this Agreement or incompatible for the intended purposes.

   c. Unless Section 2(b) applies, if Contributor fails to demonstrate meaningful progress on repairing or redoing the Services within the applicable timeframes, Company may suspend any payment obligation until such time as meaningful progress is demonstrated to Company's satisfaction. If the Contributor fails to demonstrate meaningful progress for a period exceeding 14 (fourteen) days despite weekly check-ins, the Company may terminate this Agreement. For purposes of this Agreement, "meaningful progress" shall include, but not be limited to, regular communication, clear articulation of challenges, implementation of agreed-upon solutions, and demonstration of advancement toward resolving the identified issues.

   d. Source Code. Contributor shall provide to Company not less than once every month (or such frequency agreed by the Parties), and each time the Work Product is submitted for Approval, an up-to-date copy of any source code for the Work Product which comprises Background IP of Contributor or Developed IP.

   e. Change Control. Any changes to the Specifications will be set forth in writing and signed by both Parties before they shall take effect. Contributor may not unreasonably decline to accept any change requests that reduce the cost of performance of the Services for Contributor. Contributor may not decline any change requests that increase the cost or magnitude of performance of the Services for Contributor, provided that the changes are reasonable in scope.
```
