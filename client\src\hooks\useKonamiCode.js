import { useState, useEffect } from 'react';

/**
 * Konami Code Hook
 *
 * Detects the classic Konami Code sequence:
 * ↑ ↑ ↓ ↓ ← → ← → B A
 *
 * @param {function} callback - Function to call when sequence is completed
 * @param {number} resetTime - Time in ms to reset sequence (default: 3000)
 */
const useKonamiCode = (callback, resetTime = 3000) => {
  const [sequence, setSequence] = useState([]);
  const [timeoutId, setTimeoutId] = useState(null);

  // The classic Konami Code sequence
  const konamiSequence = [
    'ArrowUp',
    'ArrowUp',
    'ArrowDown',
    'ArrowDown',
    'ArrowLeft',
    'ArrowRight',
    'ArrowLeft',
    'ArrowRight',
    'KeyB',
    'KeyA'
  ];

  useEffect(() => {
    const handleKeyDown = (event) => {
      // Clear existing timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Add the key to sequence
      const newSequence = [...sequence, event.code];

      // Check if the sequence matches the beginning of Konami code
      const isValidSequence = konamiSequence.slice(0, newSequence.length)
        .every((key, index) => key === newSequence[index]);

      if (isValidSequence) {
        setSequence(newSequence);

        // Check if complete sequence is entered
        if (newSequence.length === konamiSequence.length) {
          callback();
          setSequence([]); // Reset sequence
          return;
        }

        // Set timeout to reset sequence
        const newTimeoutId = setTimeout(() => {
          setSequence([]);
        }, resetTime);
        setTimeoutId(newTimeoutId);
      } else {
        // Invalid key, reset sequence
        setSequence([]);
      }
    };

    // Use capture phase to ensure this runs before other handlers - DISABLED to prevent responsiveness issues
    // document.addEventListener('keydown', handleKeyDown, true);

    return () => {
      // document.removeEventListener('keydown', handleKeyDown, true);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [sequence, callback, resetTime, timeoutId]);

  // Return current progress for debugging
  return {
    progress: sequence.length,
    total: konamiSequence.length,
    isActive: sequence.length > 0
  };
};

export default useKonamiCode;
