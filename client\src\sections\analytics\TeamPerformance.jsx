import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Avatar, Chip, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Users, TrendingUp, Award, Clock, Target, Star } from 'lucide-react';

/**
 * Team Performance Section
 * 
 * Displays team collaboration and performance analytics including:
 * - Team member contributions
 * - Collaboration patterns
 * - Performance rankings
 * - Team efficiency metrics
 */
const TeamPerformance = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [selectedTeam, setSelectedTeam] = useState('all');
  const [teams, setTeams] = useState([]);
  const [performanceData, setPerformanceData] = useState({
    totalTeamMembers: 0,
    activeCollaborators: 0,
    teamEfficiency: 0,
    collaborationScore: 0,
    memberPerformance: [],
    teamProjects: [],
    collaborationMatrix: [],
    topPerformers: []
  });
  const [error, setError] = useState(null);

  // Load team performance data
  useEffect(() => {
    if (currentUser) {
      loadTeamsAndPerformance();
    }
  }, [currentUser, selectedTeam]);

  const loadTeamsAndPerformance = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load user's teams
      const { data: userTeams, error: teamsError } = await supabase
        .from('project_members')
        .select(`
          project_id,
          role,
          projects (
            id,
            name,
            status,
            created_at
          )
        `)
        .eq('user_id', currentUser.id);

      if (teamsError) throw teamsError;

      const uniqueProjects = userTeams?.reduce((acc, member) => {
        if (member.projects && !acc.find(p => p.id === member.projects.id)) {
          acc.push({
            ...member.projects,
            userRole: member.role
          });
        }
        return acc;
      }, []) || [];

      setTeams(uniqueProjects);

      // Load team performance metrics
      const projectIds = uniqueProjects.map(p => p.id);
      if (projectIds.length === 0) {
        setPerformanceData({
          totalTeamMembers: 0,
          activeCollaborators: 0,
          teamEfficiency: 0,
          collaborationScore: 0,
          memberPerformance: [],
          teamProjects: [],
          collaborationMatrix: [],
          topPerformers: []
        });
        return;
      }

      // Filter projects if specific team selected
      const filteredProjectIds = selectedTeam === 'all' 
        ? projectIds 
        : [selectedTeam];

      // Load team members for projects
      const { data: teamMembers, error: membersError } = await supabase
        .from('project_members')
        .select(`
          user_id,
          role,
          project_id,
          joined_at,
          users (
            id,
            display_name,
            avatar_url
          ),
          projects (
            id,
            name
          )
        `)
        .in('project_id', filteredProjectIds);

      if (membersError) throw membersError;

      // Load contributions for team analysis
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          user_id,
          project_id,
          hours_logged,
          contribution_date,
          task_category,
          users (
            id,
            display_name,
            avatar_url
          ),
          projects (
            id,
            name
          )
        `)
        .in('project_id', filteredProjectIds);

      if (contributionsError) throw contributionsError;

      // Load tasks for completion analysis
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          project_id,
          assigned_to,
          status,
          created_at,
          completion_timestamp,
          estimated_hours,
          logged_hours
        `)
        .in('project_id', filteredProjectIds);

      if (tasksError) throw tasksError;

      // Process performance data
      const processedData = processTeamPerformance(
        uniqueProjects.filter(p => selectedTeam === 'all' || p.id === selectedTeam),
        teamMembers || [],
        contributions || [],
        tasks || []
      );
      setPerformanceData(processedData);

    } catch (error) {
      console.error('Error loading team performance:', error);
      setError(error.message);
      toast.error('Failed to load team performance data');
    } finally {
      setLoading(false);
    }
  };

  const processTeamPerformance = (projects, teamMembers, contributions, tasks) => {
    // Get unique team members
    const uniqueMembers = teamMembers.reduce((acc, member) => {
      if (!acc.find(m => m.user_id === member.user_id)) {
        acc.push(member);
      }
      return acc;
    }, []);

    const totalTeamMembers = uniqueMembers.length;
    
    // Calculate active collaborators (members with contributions in last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentContributors = new Set(
      contributions
        .filter(c => new Date(c.contribution_date) >= thirtyDaysAgo)
        .map(c => c.user_id)
    );
    const activeCollaborators = recentContributors.size;

    // Calculate member performance
    const memberPerformance = uniqueMembers.map(member => {
      const memberContributions = contributions.filter(c => c.user_id === member.user_id);
      const memberTasks = tasks.filter(t => t.assigned_to === member.user_id);
      const completedTasks = memberTasks.filter(t => t.status === 'Done' || t.status === 'completed');
      
      const totalHours = memberContributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0);
      const completionRate = memberTasks.length > 0 
        ? Math.round((completedTasks.length / memberTasks.length) * 100)
        : 0;
      
      return {
        user_id: member.user_id,
        name: member.users?.display_name || 'Unknown User',
        avatar_url: member.users?.avatar_url,
        role: member.role,
        totalHours: Math.round(totalHours * 100) / 100,
        totalContributions: memberContributions.length,
        totalTasks: memberTasks.length,
        completedTasks: completedTasks.length,
        completionRate,
        isActive: recentContributors.has(member.user_id),
        projects: teamMembers
          .filter(tm => tm.user_id === member.user_id)
          .map(tm => tm.projects?.name)
          .filter(Boolean)
      };
    });

    // Calculate team efficiency
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.status === 'Done' || t.status === 'completed').length;
    const teamEfficiency = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // Calculate collaboration score (based on cross-project participation)
    const collaborationScore = Math.round(
      (activeCollaborators / Math.max(totalTeamMembers, 1)) * 100
    );

    // Get top performers
    const topPerformers = memberPerformance
      .sort((a, b) => {
        // Sort by completion rate first, then by total hours
        if (b.completionRate !== a.completionRate) {
          return b.completionRate - a.completionRate;
        }
        return b.totalHours - a.totalHours;
      })
      .slice(0, 5);

    // Team projects summary
    const teamProjects = projects.map(project => {
      const projectMembers = teamMembers.filter(tm => tm.project_id === project.id);
      const projectContributions = contributions.filter(c => c.project_id === project.id);
      const projectTasks = tasks.filter(t => t.project_id === project.id);
      const completedProjectTasks = projectTasks.filter(t => t.status === 'Done' || t.status === 'completed');
      
      return {
        id: project.id,
        name: project.name,
        status: project.status,
        memberCount: projectMembers.length,
        totalHours: projectContributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0),
        totalTasks: projectTasks.length,
        completedTasks: completedProjectTasks.length,
        completionRate: projectTasks.length > 0 
          ? Math.round((completedProjectTasks.length / projectTasks.length) * 100)
          : 0
      };
    });

    return {
      totalTeamMembers,
      activeCollaborators,
      teamEfficiency,
      collaborationScore,
      memberPerformance,
      teamProjects,
      collaborationMatrix: [], // Could be expanded for detailed collaboration analysis
      topPerformers
    };
  };

  const formatHours = (hours) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${Math.round(hours * 10) / 10}h`;
  };

  const getRoleColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'admin':
      case 'owner':
        return 'danger';
      case 'manager':
      case 'lead':
        return 'warning';
      case 'developer':
      case 'contributor':
        return 'primary';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading team performance...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-white mb-4">Error Loading Performance Data</h2>
            <p className="text-white/70 mb-4">{error}</p>
            <Button onClick={loadTeamsAndPerformance} color="primary" variant="bordered">
              Try Again
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
            <Users size={20} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Team Performance</h2>
            <p className="text-white/60">Analyze team collaboration and productivity</p>
          </div>
        </div>
        
        <Select
          value={selectedTeam}
          onChange={(e) => setSelectedTeam(e.target.value)}
          className="w-48"
          size="sm"
        >
          <SelectItem key="all" value="all">All Teams</SelectItem>
          {teams.map(team => (
            <SelectItem key={team.id} value={team.id}>
              {team.name}
            </SelectItem>
          ))}
        </Select>
      </motion.div>

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Users size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Team Members</p>
                <p className="text-2xl font-bold text-white">{performanceData.totalTeamMembers}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <TrendingUp size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Active Contributors</p>
                <p className="text-2xl font-bold text-white">{performanceData.activeCollaborators}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Target size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Team Efficiency</p>
                <p className="text-2xl font-bold text-white">{performanceData.teamEfficiency}%</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Star size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Collaboration Score</p>
                <p className="text-2xl font-bold text-white">{performanceData.collaborationScore}%</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Top Performers */}
      {performanceData.topPerformers.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Award size={20} className="text-yellow-400" />
                <h3 className="text-lg font-semibold text-white">Top Performers</h3>
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {performanceData.topPerformers.map((performer, index) => (
                  <div key={performer.user_id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <span className="text-white/60 text-sm">#{index + 1}</span>
                        <Avatar
                          src={performer.avatar_url}
                          name={performer.name}
                          size="sm"
                        />
                      </div>
                      <div>
                        <p className="text-white font-medium">{performer.name}</p>
                        <div className="flex items-center gap-2">
                          <Chip 
                            size="sm" 
                            color={getRoleColor(performer.role)}
                            variant="flat"
                          >
                            {performer.role}
                          </Chip>
                          {performer.isActive && (
                            <Chip size="sm" color="success" variant="dot">
                              Active
                            </Chip>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium">{performer.completionRate}% completion</p>
                      <p className="text-white/60 text-sm">
                        {formatHours(performer.totalHours)} • {performer.totalContributions} contributions
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Team Projects */}
      {performanceData.teamProjects.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Team Projects</h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {performanceData.teamProjects.map((project, index) => (
                  <div key={project.id} className="p-4 bg-white/5 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-medium">{project.name}</h4>
                      <Chip 
                        size="sm" 
                        color={project.status === 'completed' ? 'success' : 'primary'}
                        variant="flat"
                      >
                        {project.status}
                      </Chip>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">Progress</span>
                        <span className="text-white">{project.completionRate}%</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                          style={{ width: `${project.completionRate}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm text-white/60">
                        <span>{project.memberCount} members</span>
                        <span>{formatHours(project.totalHours)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default TeamPerformance;
