/**
 * End-to-End Project Wizard Flow Agreement Test
 *
 * This script simulates a user going through all steps of the project wizard,
 * generates an agreement, and compares it with the reference template.
 *
 * Usage: node test-wizard-flow-agreement.mjs
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { diffLines } from 'diff';
import chalk from 'chalk';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements/wizard-flow');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Define the reference agreement path for comparison
const referenceAgreementPath = path.join(__dirname, '../test-output/agreements/reference/reference-agreement.md');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Define test data matching the Village of The Ages reference project
// This simulates the data that would be collected through the wizard steps
const initialProjectData = {
  name: "Village of The Ages",
  title: "Village of The Ages",
  description: "a 3D village simulation game with high-fidelity visuals where players guide communities through historical progressions and manage resource-based challenges in an open world environment with realistic graphics and cinematic storytelling",
  project_type: "game",
  is_public: true,
  launch_date: new Date("2023-04-30").toISOString(),
  company_name: "City of Gamers Inc.",
  // Not explicitly setting engine to test the auto-detection
  // engine: "Unreal Engine 5",
  platforms: "PC, Mac, Consoles",
  estimated_duration: 4, // 4 months as in the example
  // Not explicitly setting languages to test the auto-detection
  // languages: ["C++", "Blueprint"]
};

// Simulate the project wizard flow
console.log('🚀 Starting End-to-End Project Wizard Flow Test');
console.log('📁 Output directory:', outputDir);

// Step 1: Project Basics
console.log('\n📋 Step 1: Project Basics');
let projectData = { ...initialProjectData };
console.log('  ✓ Project name:', projectData.name);
console.log('  ✓ Project type:', projectData.project_type);
console.log('  ✓ Description:', projectData.description);

// Step 2: Team & Contributors
console.log('\n👥 Step 2: Team & Contributors');
projectData.contributors = [
  {
    id: "owner-id",
    display_name: "Gynell Journigan",
    email: "<EMAIL>",
    permission_level: "Owner",
    title: "President",
    address: "1205 43rd Street, Suite B, Orlando, Florida 32839",
    state: "Florida",
    county: "Orange County",
    role: "Project Owner",
    status: "active"
  },
  {
    id: "contributor-id",
    display_name: "John Doe",
    email: "<EMAIL>",
    permission_level: "Contributor",
    title: "Developer",
    address: "123 Main St, Anytown, CA 12345",
    role: "Developer",
    status: "active"
  }
];
console.log(`  ✓ Added ${projectData.contributors.length} contributors`);

// Step 3: Royalty Model
console.log('\n💰 Step 3: Royalty Model');
projectData.royalty_model = {
  model_type: "custom",
  model_schema: "cog",
  configuration: {
    tasks_weight: 30,
    hours_weight: 30,
    difficulty_weight: 40
  },
  is_pre_expense: true,
  contributor_percentage: 50,
  min_payout: 100000,
  max_payout: 1000000
};
console.log('  ✓ Selected custom royalty model');
console.log('  ✓ Tasks weight:', projectData.royalty_model.configuration.tasks_weight + '%');
console.log('  ✓ Hours weight:', projectData.royalty_model.configuration.hours_weight + '%');
console.log('  ✓ Difficulty weight:', projectData.royalty_model.configuration.difficulty_weight + '%');

// Step 4: Revenue Tranches
console.log('\n📊 Step 4: Revenue Tranches');
projectData.revenue_tranches = [
  {
    id: "tranche-1",
    name: "Initial Release",
    start_date: new Date("2023-05-01").toISOString(),
    end_date: new Date("2023-12-31").toISOString(),
    platform_fee_config: {
      percentage: 30,
      apply_before: true
    },
    distribution_thresholds: {
      minimum_revenue: 100000,
      maximum_payout: 1000000
    },
    rollover_config: "next"
  }
];
console.log('  ✓ Added 1 revenue tranche');

// Step 5: Contribution Tracking
console.log('\n📝 Step 5: Contribution Tracking');
projectData.contribution_tracking = {
  track_tasks: true,
  track_hours: true,
  track_difficulty: true,
  task_types: [
    { name: "Programming", difficulty: "Medium", points: 5 },
    { name: "Art", difficulty: "Hard", points: 10 },
    { name: "QA", difficulty: "Easy", points: 2 },
    { name: "Writing", difficulty: "Medium", points: 5 },
    { name: "Engineering", difficulty: "Hard", points: 10 }
  ]
};
console.log('  ✓ Configured contribution tracking');

// Step 6: Milestones
console.log('\n🏁 Step 6: Milestones');
projectData.milestones = [
  {
    id: "milestone-1",
    title: "First Playable",
    description: "Core mechanics implemented",
    target_date: new Date("2023-01-31").toISOString(),
    deliverables: [
      "Core mechanics implemented",
      "Basic building and resource systems functional",
      "First era playable"
    ]
  },
  {
    id: "milestone-2",
    title: "Feature Complete",
    description: "All core features implemented",
    target_date: new Date("2023-03-31").toISOString(),
    deliverables: [
      "All core features implemented",
      "Multiple eras playable",
      "Core systems integrated"
    ]
  },
  {
    id: "milestone-3",
    title: "Release Candidate",
    description: "All features polished",
    target_date: new Date("2023-04-15").toISOString(),
    deliverables: [
      "All features polished",
      "Performance optimized",
      "Major bugs resolved"
    ]
  },
  {
    id: "milestone-4",
    title: "Launch",
    description: "Game ready for early access release",
    target_date: new Date("2023-04-30").toISOString(),
    deliverables: [
      "Game ready for early access release",
      "Store page and marketing assets complete",
      "Launch plan executed"
    ]
  }
];
console.log(`  ✓ Added ${projectData.milestones.length} milestones`);

// Step 7: Review & Agreement
console.log('\n📄 Step 7: Review & Agreement');

// Simulate the agreement generation in the wizard
const currentUser = {
  id: "contributor-id",
  email: "<EMAIL>",
  user_metadata: { full_name: "John Doe" },
  address: "123 Main St, Anytown, CA 12345"
};

const options = {
  contributors: projectData.contributors,
  currentUser: currentUser,
  royaltyModel: projectData.royalty_model,
  milestones: projectData.milestones,
  fullName: "John Doe",
  // Use a fixed date for testing to ensure consistent output
  agreementDate: new Date("2025-05-17")
};

try {
  // Generate the agreement using the same flow as the wizard
  console.log('  ✓ Generating agreement...');
  const wizardAgreement = generator.generateAgreement(templateText, projectData, options);

  // Save the generated agreement
  const wizardOutputPath = path.join(outputDir, 'wizard-flow-agreement.md');
  fs.writeFileSync(wizardOutputPath, wizardAgreement, 'utf8');
  console.log(`  ✓ Agreement saved to: ${wizardOutputPath}`);

  // Compare with reference agreement if it exists
  if (fs.existsSync(referenceAgreementPath)) {
    console.log('\n🔍 Comparing with reference agreement...');
    const referenceAgreement = fs.readFileSync(referenceAgreementPath, 'utf8');

    // Normalize line endings
    const normalizedWizard = wizardAgreement.replace(/\r\n/g, '\n');
    const normalizedReference = referenceAgreement.replace(/\r\n/g, '\n');

    // Generate diff
    const diff = diffLines(normalizedReference, normalizedWizard);

    // Count differences
    let addedLines = 0;
    let removedLines = 0;
    let unchangedLines = 0;
    let diffDetails = [];

    diff.forEach((part) => {
      // Count lines
      const lines = part.value.split('\n').length - 1;

      if (part.added) {
        addedLines += lines;
        // Store first few added lines for reporting
        if (diffDetails.length < 5 && part.value.trim()) {
          diffDetails.push({ type: 'added', value: part.value.split('\n')[0] });
        }
      } else if (part.removed) {
        removedLines += lines;
        // Store first few removed lines for reporting
        if (diffDetails.length < 5 && part.value.trim()) {
          diffDetails.push({ type: 'removed', value: part.value.split('\n')[0] });
        }
      } else {
        unchangedLines += lines;
      }
    });

    // Save diff output
    const diffOutputPath = path.join(outputDir, 'wizard-reference-diff.md');
    let diffOutput = '';
    diff.forEach((part) => {
      if (part.added) {
        diffOutput += `+ ${part.value}`;
      } else if (part.removed) {
        diffOutput += `- ${part.value}`;
      } else {
        diffOutput += `  ${part.value}`;
      }
    });
    fs.writeFileSync(diffOutputPath, diffOutput, 'utf8');

    // Print summary
    console.log('\n📊 Comparison Summary:');
    console.log(`  Total lines in reference: ${normalizedReference.split('\n').length}`);
    console.log(`  Total lines in wizard: ${normalizedWizard.split('\n').length}`);
    console.log(`  Added lines: ${addedLines}`);
    console.log(`  Removed lines: ${removedLines}`);
    console.log(`  Unchanged lines: ${unchangedLines}`);
    console.log(`  Diff saved to: ${diffOutputPath}`);

    // Show some differences if any
    if (diffDetails.length > 0) {
      console.log('\n  Sample differences:');
      diffDetails.forEach(detail => {
        console.log(`    ${detail.type === 'added' ? '+' : '-'} ${detail.value.substring(0, 80)}${detail.value.length > 80 ? '...' : ''}`);
      });
    }

    // Determine if the files are similar enough
    const similarityThreshold = 0.9; // 90% similarity
    const totalLines = addedLines + removedLines + unchangedLines;
    const similarity = unchangedLines / totalLines;

    if (similarity >= similarityThreshold) {
      console.log(`\n✅ The wizard-generated agreement is ${Math.round(similarity * 100)}% similar to the reference agreement.`);
    } else {
      console.log(`\n⚠️ The wizard-generated agreement is only ${Math.round(similarity * 100)}% similar to the reference agreement.`);
    }
  } else {
    console.log('\n⚠️ Reference agreement not found. Skipping comparison.');
    console.log(`Expected reference at: ${referenceAgreementPath}`);
    console.log('Run test-reference-agreement.mjs first to generate the reference agreement.');
  }

  // Validate the agreement content
  console.log('\n🔍 Validating agreement content...');

  // Function to validate agreement content
  const validateAgreement = (agreement) => {
    const results = {
      sections: { pass: true, missing: [] },
      placeholders: { pass: true, found: [] },
      content: { pass: true, missing: [] },
      formatting: { pass: true, issues: [] },
      exhibits: { pass: true, issues: [] },
      schedules: { pass: true, issues: [] }
    };

    // Check for required sections
    const requiredSections = [
      "## 1. Definitions",
      "## 2. Treatment of Confidential Information",
      "## 3. Ownership of Work Product",
      "## 4. Non-Disparagement",
      "## 5. Termination",
      "## 6. Equitable Remedies",
      "## 7. Assignment",
      "## 8. Waivers and Amendments",
      "## 9. Survival",
      "## 10. Status as Independent Contractor",
      "## 11. Representations and Warranties",
      "## 12. Indemnification",
      "## 13. Entire Agreement",
      "## 14. Governing Law",
      "## 15. Consent to Jurisdiction",
      "## 16. Settlement of Disputes",
      "## 17. Titles and Subtitles",
      "## 18. Opportunity to Consult",
      "## 19. Gender; Singular and Plural",
      "## 20. Notice",
      "## 21. Counterparts",
      "## EXHIBIT I",
      "## EXHIBIT II",
      "## SCHEDULE A",
      "## SCHEDULE B"
    ];

    requiredSections.forEach(section => {
      if (!agreement.includes(section)) {
        results.sections.pass = false;
        results.sections.missing.push(section);
      }
    });

    // Check for unwanted placeholders
    const unwantedPlaceholders = [
      "\\[Project Name\\]",
      "\\[Date\\]",
      "\\[Project Owner\\]",
      "\\[Contributor\\]",
      "\\[_+\\]",
      "\\[ \\], 20\\[__\\]",
      "\\[If a company\\]",
      "\\[If an individual\\]"
    ];

    unwantedPlaceholders.forEach(placeholder => {
      const regex = new RegExp(placeholder, "g");
      if (regex.test(agreement)) {
        results.placeholders.pass = false;
        results.placeholders.found.push(placeholder);
      }
    });

    // Check for project-specific content
    const projectSpecificContent = [
      "Village of The Ages",
      "City of Gamers Inc.",
      "Gynell Journigan",
      "John Doe",
      // Use a partial match for the description since it's now longer
      "village simulation game",
      "historical progressions",
      "1205 43rd Street, Suite B, Orlando, Florida 32839",
      "<EMAIL>"
    ];

    projectSpecificContent.forEach(content => {
      if (!agreement.includes(content)) {
        results.content.pass = false;
        results.content.missing.push(content);
      }
    });

    // Check formatting
    const formattingChecks = [
      { pattern: /\n\n\n\n/, description: "Too many consecutive newlines" },
      { pattern: /\*\*\s+\*\*/, description: "Empty bold text" },
      { pattern: /\n\s+\n/, description: "Lines with only whitespace" }
    ];

    formattingChecks.forEach(check => {
      if (check.pattern.test(agreement)) {
        results.formatting.pass = false;
        results.formatting.issues.push(check.description);
      }
    });

    // Check exhibits
    const exhibitIPattern = /## EXHIBIT I[\s\S]*?(?=## EXHIBIT II|$)/;
    const exhibitIIPattern = /## EXHIBIT II[\s\S]*?(?=## SCHEDULE|$)/;

    const exhibitI = agreement.match(exhibitIPattern);
    const exhibitII = agreement.match(exhibitIIPattern);

    if (!exhibitI) {
      results.exhibits.pass = false;
      results.exhibits.issues.push("EXHIBIT I is missing or malformed");
    } else if (!exhibitI[0].includes("SPECIFICATIONS")) {
      results.exhibits.pass = false;
      results.exhibits.issues.push("EXHIBIT I is missing SPECIFICATIONS section");
    }

    if (!exhibitII) {
      results.exhibits.pass = false;
      results.exhibits.issues.push("EXHIBIT II is missing or malformed");
    } else if (!exhibitII[0].includes("PRODUCT ROADMAP")) {
      results.exhibits.pass = false;
      results.exhibits.issues.push("EXHIBIT II is missing PRODUCT ROADMAP section");
    }

    // Check schedules
    const scheduleAPattern = /## SCHEDULE A[\s\S]*?(?=## SCHEDULE B|$)/;
    const scheduleBPattern = /## SCHEDULE B[\s\S]*?(?=## EXHIBIT|$)/;

    const scheduleA = agreement.match(scheduleAPattern);
    const scheduleB = agreement.match(scheduleBPattern);

    // Check for Description of Services section - it might be after SCHEDULE A or as a separate section
    const descriptionOfServicesPattern = /## Description of Services|### Description of Services/;
    const hasDescriptionOfServices = descriptionOfServicesPattern.test(agreement);

    if (!scheduleA) {
      results.schedules.pass = false;
      results.schedules.issues.push("SCHEDULE A is missing or malformed");
    } else if (!hasDescriptionOfServices) {
      results.schedules.pass = false;
      results.schedules.issues.push("Description of Services section is missing");
    }

    // Check for COMPENSATION section - it might be after SCHEDULE B or as a separate section
    const compensationPattern = /## COMPENSATION|### COMPENSATION/;
    const hasCompensation = compensationPattern.test(agreement);

    if (!scheduleB) {
      results.schedules.pass = false;
      results.schedules.issues.push("SCHEDULE B is missing or malformed");
    } else if (!hasCompensation) {
      results.schedules.pass = false;
      results.schedules.issues.push("COMPENSATION section is missing");
    }

    // Check for signature blocks
    if (!agreement.includes("IN WITNESS WHEREOF")) {
      results.formatting.pass = false;
      results.formatting.issues.push("Missing signature section (IN WITNESS WHEREOF)");
    }

    // Check for date in agreement header
    const datePattern = /This Contributor Agreement \(this "Agreement"\) is effective as of ([^,]+),/;
    const dateMatch = agreement.match(datePattern);

    if (!dateMatch) {
      results.formatting.pass = false;
      results.formatting.issues.push("Missing or malformed date in agreement header");
    }

    return results;
  };

  // Run validation
  const validationResults = validateAgreement(wizardAgreement);

  // Report validation results

  // Sections
  if (validationResults.sections.pass) {
    console.log('  ✓ All required sections are present');
  } else {
    console.log('  ❌ Missing sections:');
    validationResults.sections.missing.forEach(section => {
      console.log(`    - ${section}`);
    });
  }

  // Placeholders
  if (validationResults.placeholders.pass) {
    console.log('  ✓ No unwanted placeholders found');
  } else {
    console.log('  ❌ Found unwanted placeholders:');
    validationResults.placeholders.found.forEach(placeholder => {
      console.log(`    - ${placeholder}`);
    });
  }

  // Content
  if (validationResults.content.pass) {
    console.log('  ✓ All project-specific content is present');
  } else {
    console.log('  ❌ Missing project-specific content:');
    validationResults.content.missing.forEach(content => {
      console.log(`    - ${content}`);
    });
  }

  // Formatting
  if (validationResults.formatting.pass) {
    console.log('  ✓ Agreement formatting is correct');
  } else {
    console.log('  ❌ Formatting issues:');
    validationResults.formatting.issues.forEach(issue => {
      console.log(`    - ${issue}`);
    });
  }

  // Exhibits
  if (validationResults.exhibits.pass) {
    console.log('  ✓ Exhibits are properly formatted');
  } else {
    console.log('  ❌ Exhibit issues:');
    validationResults.exhibits.issues.forEach(issue => {
      console.log(`    - ${issue}`);
    });
  }

  // Schedules
  if (validationResults.schedules.pass) {
    console.log('  ✓ Schedules are properly formatted');
  } else {
    console.log('  ❌ Schedule issues:');
    validationResults.schedules.issues.forEach(issue => {
      console.log(`    - ${issue}`);
    });
  }

  // Save validation results
  const validationOutputPath = path.join(outputDir, 'validation-results.json');
  fs.writeFileSync(validationOutputPath, JSON.stringify(validationResults, null, 2), 'utf8');
  console.log(`  ✓ Validation results saved to: ${validationOutputPath}`);

  // Final result
  const allPassed = Object.values(validationResults).every(category => category.pass);

  if (allPassed) {
    console.log('\n✅ Agreement validation passed!');
  } else {
    console.log('\n❌ Agreement validation failed!');
  }

} catch (error) {
  console.error('\n❌ Error generating agreement:', error);
}
