# Apply Screenshot Migration to Supabase

To enable screenshot functionality in bug reports, you need to apply the following SQL migration to your Supabase database.

## Steps:

1. Go to your Supabase dashboard: https://supabase.com/dashboard
2. Navigate to your project: `hqqlrrqvjcetoxbdjgzx`
3. Go to the SQL Editor
4. Copy and paste the following SQL code:

```sql
-- Add screenshots column to bug_reports table
-- This will store an array of screenshot objects with url, name, and path

-- Add screenshots column to store screenshot metadata
ALTER TABLE public.bug_reports
ADD COLUMN IF NOT EXISTS screenshots JSONB DEFAULT '[]'::jsonb;

-- Add comment for the new column
COMMENT ON COLUMN public.bug_reports.screenshots IS 'Array of screenshot objects with url, name, and path properties';

-- Create storage bucket for bug attachments if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('bug-attachments', 'bug-attachments', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for bug attachments bucket
-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload bug attachments"
ON storage.objects
FOR INSERT
WITH CHECK (
    bucket_id = 'bug-attachments'
    AND auth.role() = 'authenticated'
);

-- Allow public read access to bug attachments
CREATE POLICY "Public read access to bug attachments"
ON storage.objects
FOR SELECT
USING (bucket_id = 'bug-attachments');

-- Allow users to delete their own uploaded attachments
CREATE POLICY "Users can delete their own bug attachments"
ON storage.objects
FOR DELETE
USING (
    bucket_id = 'bug-attachments'
    AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow admins to delete any bug attachments
CREATE POLICY "Admins can delete any bug attachments"
ON storage.objects
FOR DELETE
USING (
    bucket_id = 'bug-attachments'
    AND EXISTS (
        SELECT 1 FROM public.users
        WHERE users.id = auth.uid() AND users.is_admin = true
    )
);
```

5. Click "Run" to execute the migration
6. Verify the migration was successful by checking:
   - The `bug_reports` table now has a `screenshots` column
   - The `bug-attachments` storage bucket exists
   - The storage policies are in place

## What this enables:

- **Paste screenshots**: Users can paste screenshots directly with Ctrl+V
- **Upload files**: Users can select image files to upload
- **Copy/Download**: Screenshots can be copied to clipboard or downloaded
- **Admin management**: Admins can view, copy, and download screenshots from bug reports
- **Reopen fixed bugs**: Admins can reopen "fixed" bugs if they need to be readdressed
- **Secure storage**: Screenshots are stored in Supabase Storage with proper access controls

## Testing:

After applying the migration, test the functionality by:
1. Going to the bug report form
2. Pasting a screenshot with Ctrl+V
3. Uploading an image file
4. Submitting the bug report
5. Viewing the bug report in the admin dashboard
6. Testing copy/download functionality
