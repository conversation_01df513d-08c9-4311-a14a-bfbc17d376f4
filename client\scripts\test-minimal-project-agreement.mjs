/**
 * Test Minimal Project Agreement Generation
 *
 * This script tests the agreement generation for a project with minimal description data.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements/minimal-project');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Define test data for a project with minimal description
const minimalProjectData = {
  name: "MinimalProject",
  title: "MinimalProject",
  description: "a project", // Minimal description
  project_type: "software", // Only specify project type
  is_public: true,
  // No engine, platforms, or languages specified
};

console.log('🚀 Starting Minimal Project Agreement Test');
console.log('📁 Output directory:', outputDir);

// Simulate the agreement generation
const currentUser = {
  id: "contributor-id",
  email: "<EMAIL>",
  user_metadata: { full_name: "Jane Smith" },
  address: "123 Main St, Anytown, CA 12345"
};

const options = {
  contributors: [
    {
      id: "owner-id",
      display_name: "John Doe",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "CEO",
      address: "456 Business Ave, Anytown, CA 12345",
      state: "California",
      county: "Anytown County",
      role: "Project Owner",
      status: "active"
    },
    {
      id: "contributor-id",
      display_name: "Jane Smith",
      email: "<EMAIL>",
      permission_level: "Contributor",
      title: "Developer",
      address: "123 Main St, Anytown, CA 12345",
      role: "Developer",
      status: "active"
    }
  ],
  currentUser: currentUser,
  royaltyModel: {
    model_type: "custom",
    model_schema: "cog",
    configuration: {
      tasks_weight: 33,
      hours_weight: 33,
      difficulty_weight: 34
    },
    is_pre_expense: true,
    contributor_percentage: 50,
    min_payout: 10000,
    max_payout: 100000
  },
  milestones: [
    {
      id: "milestone-1",
      title: "First Milestone",
      description: "Complete initial setup",
      target_date: new Date("2023-06-30").toISOString(),
      deliverables: [
        "Initial setup complete"
      ]
    }
  ],
  fullName: "Jane Smith",
  // Use a fixed date for testing to ensure consistent output
  agreementDate: new Date("2025-05-17")
};

try {
  // Generate the agreement
  console.log('  ✓ Generating agreement...');
  const minimalAgreement = generator.generateAgreement(templateText, minimalProjectData, options);

  // Save the generated agreement
  const outputPath = path.join(outputDir, 'minimal-project-agreement.md');
  fs.writeFileSync(outputPath, minimalAgreement, 'utf8');
  console.log(`  ✓ Agreement saved to: ${outputPath}`);

  // Validate the agreement
  console.log('\n🔍 Validating minimal project agreement...');
  
  // Check for default values that should be applied
  const defaultValues = [
    'software application', // Default term for software projects
    'users', // Default term for users
    'modules', // Default term for levels in software
    'React', // Default framework for software
    'JavaScript', // Default language for React
  ];
  
  let defaultValuesFound = 0;
  
  defaultValues.forEach(term => {
    if (minimalAgreement.includes(term)) {
      defaultValuesFound++;
      console.log(`  ✓ Found default term: "${term}"`);
    } else {
      console.log(`  ❌ Missing default term: "${term}"`);
    }
  });
  
  // Check for required sections
  const requiredSections = [
    '## EXHIBIT I',
    '## EXHIBIT II',
    '## SCHEDULE A',
    '## SCHEDULE B'
  ];
  
  let missingSections = [];
  requiredSections.forEach(section => {
    if (!minimalAgreement.includes(section)) {
      missingSections.push(section);
    }
  });
  
  if (missingSections.length === 0) {
    console.log('  ✓ All required sections are present');
  } else {
    console.log('  ❌ Missing sections:');
    missingSections.forEach(section => {
      console.log(`    - ${section}`);
    });
  }
  
  // Final validation result
  if (defaultValuesFound >= 3 && missingSections.length === 0) {
    console.log('\n✅ Minimal project agreement validation passed!');
  } else {
    console.log('\n❌ Minimal project agreement validation failed!');
    if (defaultValuesFound < 3) {
      console.log(`  - Not enough default values found (${defaultValuesFound}/3 required)`);
    }
    if (missingSections.length > 0) {
      console.log(`  - Missing ${missingSections.length} required sections`);
    }
  }
} catch (error) {
  console.error('\n❌ Error generating agreement:', error);
}
