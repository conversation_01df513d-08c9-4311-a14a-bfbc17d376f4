/**
 * Royaltea Service Worker
 * 
 * Production-optimized service worker providing:
 * - Intelligent caching strategies
 * - Offline functionality
 * - Background sync
 * - Push notifications support
 * - Performance optimization
 * - Cache management and cleanup
 */

const CACHE_NAME = 'royaltea-v1.0.0';
const STATIC_CACHE = 'royaltea-static-v1.0.0';
const DYNAMIC_CACHE = 'royaltea-dynamic-v1.0.0';
const API_CACHE = 'royaltea-api-v1.0.0';

// Cache strategies configuration
const CACHE_CONFIG = {
  // Static assets (cache first)
  staticAssets: [
    '/',
    '/manifest.json',
    '/icons/icon-192x192.png',
    '/icons/icon-512x512.png'
  ],
  
  // Cache durations
  durations: {
    static: 7 * 24 * 60 * 60 * 1000,    // 7 days
    dynamic: 24 * 60 * 60 * 1000,       // 1 day
    api: 5 * 60 * 1000,                 // 5 minutes
    images: 30 * 24 * 60 * 60 * 1000    // 30 days
  },
  
  // Maximum cache sizes
  maxEntries: {
    static: 100,
    dynamic: 50,
    api: 30,
    images: 60
  }
};

// URL patterns for different caching strategies
const URL_PATTERNS = {
  static: /\.(js|css|woff2?|png|jpg|jpeg|gif|svg|ico)$/,
  api: /\/api\/|supabase\.co/,
  images: /\.(png|jpg|jpeg|gif|svg|webp|avif)$/,
  html: /\.html$|\/$/
};

/**
 * Service Worker Installation
 */
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Caching static assets...');
        return cache.addAll(CACHE_CONFIG.staticAssets);
      })
      .then(() => {
        console.log('[SW] Static assets cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static assets:', error);
      })
  );
});

/**
 * Service Worker Activation
 */
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      cleanupOldCaches(),
      // Take control of all clients
      self.clients.claim()
    ]).then(() => {
      console.log('[SW] Service worker activated successfully');
    })
  );
});

/**
 * Fetch Event Handler
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  // Apply appropriate caching strategy
  if (URL_PATTERNS.static.test(url.pathname)) {
    event.respondWith(handleStaticAssets(request));
  } else if (URL_PATTERNS.api.test(url.href)) {
    event.respondWith(handleAPIRequests(request));
  } else if (URL_PATTERNS.images.test(url.pathname)) {
    event.respondWith(handleImages(request));
  } else if (URL_PATTERNS.html.test(url.pathname) || url.pathname === '/') {
    event.respondWith(handleHTMLRequests(request));
  } else {
    event.respondWith(handleDynamicRequests(request));
  }
});

/**
 * Handle static assets (Cache First strategy)
 */
async function handleStaticAssets(request) {
  try {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      // Check if cache is still valid
      const cacheTime = await getCacheTime(request.url, STATIC_CACHE);
      if (cacheTime && (Date.now() - cacheTime) < CACHE_CONFIG.durations.static) {
        return cachedResponse;
      }
    }
    
    // Fetch from network and update cache
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      await cache.put(request, networkResponse.clone());
      await setCacheTime(request.url, STATIC_CACHE);
    }
    
    return networkResponse;
    
  } catch (error) {
    console.error('[SW] Static asset fetch failed:', error);
    
    // Return cached version if available
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline fallback
    return new Response('Offline', { status: 503 });
  }
}

/**
 * Handle API requests (Network First with cache fallback)
 */
async function handleAPIRequests(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(API_CACHE);
      await cache.put(request, networkResponse.clone());
      await setCacheTime(request.url, API_CACHE);
      
      // Cleanup old API cache entries
      await cleanupCache(API_CACHE, CACHE_CONFIG.maxEntries.api);
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('[SW] API request failed, trying cache:', request.url);
    
    // Fallback to cache
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      // Check if cache is still valid
      const cacheTime = await getCacheTime(request.url, API_CACHE);
      if (cacheTime && (Date.now() - cacheTime) < CACHE_CONFIG.durations.api) {
        return cachedResponse;
      }
    }
    
    // Return error response
    return new Response(
      JSON.stringify({ error: 'Network unavailable', offline: true }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle image requests (Cache First with network fallback)
 */
async function handleImages(request) {
  try {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      const cacheTime = await getCacheTime(request.url, DYNAMIC_CACHE);
      if (cacheTime && (Date.now() - cacheTime) < CACHE_CONFIG.durations.images) {
        return cachedResponse;
      }
    }
    
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      await cache.put(request, networkResponse.clone());
      await setCacheTime(request.url, DYNAMIC_CACHE);
      
      // Cleanup old image cache entries
      await cleanupCache(DYNAMIC_CACHE, CACHE_CONFIG.maxEntries.images);
    }
    
    return networkResponse;
    
  } catch (error) {
    // Return cached version if available
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return placeholder image
    return new Response('', { status: 404 });
  }
}

/**
 * Handle HTML requests (Network First with cache fallback)
 */
async function handleHTMLRequests(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      await cache.put(request, networkResponse.clone());
      await setCacheTime(request.url, DYNAMIC_CACHE);
    }
    
    return networkResponse;
    
  } catch (error) {
    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback to index.html for SPA routing
    const indexResponse = await caches.match('/');
    if (indexResponse) {
      return indexResponse;
    }
    
    // Return offline page
    return new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Offline - Royaltea</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .offline { color: #666; }
          </style>
        </head>
        <body>
          <div class="offline">
            <h1>You're offline</h1>
            <p>Please check your internet connection and try again.</p>
            <button onclick="window.location.reload()">Retry</button>
          </div>
        </body>
      </html>
    `, {
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

/**
 * Handle dynamic requests (Network First)
 */
async function handleDynamicRequests(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      await cache.put(request, networkResponse.clone());
      await setCacheTime(request.url, DYNAMIC_CACHE);
      
      // Cleanup old dynamic cache entries
      await cleanupCache(DYNAMIC_CACHE, CACHE_CONFIG.maxEntries.dynamic);
    }
    
    return networkResponse;
    
  } catch (error) {
    const cachedResponse = await caches.match(request);
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

/**
 * Clean up old caches
 */
async function cleanupOldCaches() {
  const cacheNames = await caches.keys();
  const validCaches = [CACHE_NAME, STATIC_CACHE, DYNAMIC_CACHE, API_CACHE];
  
  const deletePromises = cacheNames
    .filter(cacheName => !validCaches.includes(cacheName))
    .map(cacheName => {
      console.log('[SW] Deleting old cache:', cacheName);
      return caches.delete(cacheName);
    });
  
  return Promise.all(deletePromises);
}

/**
 * Clean up cache entries when limit is exceeded
 */
async function cleanupCache(cacheName, maxEntries) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  
  if (keys.length > maxEntries) {
    const entriesToDelete = keys.slice(0, keys.length - maxEntries);
    const deletePromises = entriesToDelete.map(key => cache.delete(key));
    await Promise.all(deletePromises);
    console.log(`[SW] Cleaned up ${entriesToDelete.length} entries from ${cacheName}`);
  }
}

/**
 * Set cache timestamp
 */
async function setCacheTime(url, cacheName) {
  const cache = await caches.open(`${cacheName}-meta`);
  const response = new Response(Date.now().toString());
  await cache.put(url, response);
}

/**
 * Get cache timestamp
 */
async function getCacheTime(url, cacheName) {
  try {
    const cache = await caches.open(`${cacheName}-meta`);
    const response = await cache.match(url);
    if (response) {
      const timestamp = await response.text();
      return parseInt(timestamp, 10);
    }
  } catch (error) {
    console.warn('[SW] Failed to get cache time:', error);
  }
  return null;
}

/**
 * Background Sync
 */
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

/**
 * Perform background sync
 */
async function doBackgroundSync() {
  try {
    // Sync any pending data when connection is restored
    console.log('[SW] Performing background sync...');
    
    // This would sync any offline actions, form submissions, etc.
    // Implementation depends on specific app requirements
    
  } catch (error) {
    console.error('[SW] Background sync failed:', error);
  }
}

/**
 * Push Notifications
 */
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: 'You have new updates in Royaltea',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Updates',
        icon: '/icons/action-explore.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/action-close.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('Royaltea', options)
  );
});

/**
 * Notification Click Handler
 */
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.action);
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('[SW] Service worker script loaded');
