// Authentication Persistence Test
const { test, expect } = require('@playwright/test');

/**
 * Test to investigate authentication persistence issues
 * when navigating between different routes
 */

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Authentication Persistence Investigation', () => {
  test('should investigate authentication state across navigation', async ({ page }) => {
    console.log('🔍 Investigating authentication persistence...');
    
    // Step 1: Navigate to home and authenticate
    console.log('📍 Step 1: Initial authentication');
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if login is needed
    const needsAuth = await page.locator('input[type="email"]').isVisible();
    console.log(`   Login required: ${needsAuth}`);
    
    if (needsAuth) {
      console.log('   🔐 Performing authentication...');
      await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
      await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      console.log('   ✅ Authentication completed');
    }
    
    // Step 2: Verify we're on the authenticated dashboard
    await page.waitForTimeout(3000);
    const homeContent = await page.textContent('body');
    const homeUrl = page.url();
    const homeTitle = await page.title();
    
    console.log(`   📄 Home URL: ${homeUrl}`);
    console.log(`   📄 Home Title: ${homeTitle}`);
    console.log(`   📄 Home Content Length: ${homeContent?.length}`);
    console.log(`   📄 Home Content Preview: ${homeContent?.substring(0, 200)}...`);
    
    // Check for grid tiles (should be present on authenticated dashboard)
    const gridTiles = await page.locator('[data-canvas-card]').count();
    console.log(`   🎯 Grid tiles found: ${gridTiles}`);
    
    // Step 3: Test navigation to different routes
    const testRoutes = ['/start', '/track', '/earn', '/projects'];
    
    for (const route of testRoutes) {
      console.log(`\n📍 Testing route: ${route}`);
      
      // Navigate to the route
      await page.goto(`${PRODUCTION_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Check current state
      const currentUrl = page.url();
      const currentTitle = await page.title();
      const currentContent = await page.textContent('body');
      
      console.log(`   🌐 Current URL: ${currentUrl}`);
      console.log(`   📄 Current Title: ${currentTitle}`);
      console.log(`   📄 Content Length: ${currentContent?.length}`);
      console.log(`   📄 Content Preview: ${currentContent?.substring(0, 200)}...`);
      
      // Check if we're still authenticated
      const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
      console.log(`   🔐 Needs authentication: ${stillNeedsAuth}`);
      
      // Check for specific content indicators
      const hasGridTiles = await page.locator('[data-canvas-card]').count();
      const hasLoginForm = await page.locator('input[type="email"]').count();
      const hasWelcomeBack = currentContent?.includes('Welcome Back');
      const hasSignIn = currentContent?.includes('Sign in to continue');
      
      console.log(`   🎯 Grid tiles: ${hasGridTiles}`);
      console.log(`   📝 Login form: ${hasLoginForm}`);
      console.log(`   👋 Welcome Back text: ${hasWelcomeBack}`);
      console.log(`   🔑 Sign in text: ${hasSignIn}`);
      
      // Determine page state
      if (stillNeedsAuth || hasLoginForm > 0 || hasWelcomeBack || hasSignIn) {
        console.log(`   ❌ ${route}: Showing login page (authentication lost)`);
      } else if (hasGridTiles > 0) {
        console.log(`   ✅ ${route}: Showing dashboard (authenticated)`);
      } else {
        console.log(`   ⚠️  ${route}: Unknown state (no login, no grid)`);
      }
    }
    
    // Step 4: Test direct navigation with authentication
    console.log(`\n📍 Testing direct navigation with fresh authentication`);
    
    // Go back to home and re-authenticate
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const needsReauth = await page.locator('input[type="email"]').isVisible();
    if (needsReauth) {
      console.log('   🔐 Re-authenticating...');
      await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
      await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
    }
    
    // Now try clicking on grid tiles instead of direct navigation
    await page.waitForTimeout(3000);
    const tiles = await page.locator('[data-canvas-card]').all();
    
    if (tiles.length > 0) {
      console.log(`\n📍 Testing grid tile navigation (${tiles.length} tiles found)`);
      
      // Test first few tiles
      for (let i = 0; i < Math.min(tiles.length, 3); i++) {
        // Go back to home
        await page.goto(PRODUCTION_URL);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Get fresh tile reference
        const freshTiles = await page.locator('[data-canvas-card]').all();
        if (freshTiles[i]) {
          const tileText = await freshTiles[i].textContent();
          const tileName = tileText?.split('\n')[0]?.trim() || `Tile ${i + 1}`;
          
          console.log(`   🎯 Clicking tile: ${tileName}`);
          
          await freshTiles[i].click();
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);
          
          const resultUrl = page.url();
          const resultContent = await page.textContent('body');
          const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
          
          console.log(`     🌐 Result URL: ${resultUrl}`);
          console.log(`     🔐 Needs auth: ${stillNeedsAuth}`);
          console.log(`     📄 Content length: ${resultContent?.length}`);
          
          if (stillNeedsAuth) {
            console.log(`     ❌ ${tileName}: Authentication lost after tile click`);
          } else {
            console.log(`     ✅ ${tileName}: Successfully navigated while authenticated`);
          }
        }
      }
    } else {
      console.log('   ❌ No grid tiles found for navigation testing');
    }
    
    // Step 5: Check browser storage and cookies
    console.log(`\n📍 Checking browser storage and authentication state`);
    
    // Check cookies
    const cookies = await page.context().cookies();
    console.log(`   🍪 Cookies count: ${cookies.length}`);
    cookies.forEach(cookie => {
      if (cookie.name.includes('auth') || cookie.name.includes('token') || cookie.name.includes('session')) {
        console.log(`     - ${cookie.name}: ${cookie.value.substring(0, 50)}...`);
      }
    });
    
    // Check localStorage
    const localStorage = await page.evaluate(() => {
      const items = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('auth') || key.includes('token') || key.includes('supabase'))) {
          items[key] = localStorage.getItem(key)?.substring(0, 100);
        }
      }
      return items;
    });
    
    console.log(`   💾 LocalStorage auth items:`, localStorage);
    
    // This test is for investigation, so it should always pass
    expect(true).toBe(true);
  });
});
