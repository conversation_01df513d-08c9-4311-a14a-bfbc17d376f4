# Retro Profile System Migration Instructions

This document provides instructions for applying the Retro Profile system database migration using the Supabase CLI.

## Prerequisites

1. Install the Supabase CLI if you haven't already:
   ```bash
   # Using npm
   npm install -g supabase

   # Using Homebrew (macOS)
   brew install supabase/tap/supabase
   ```

2. Log in to Supabase:
   ```bash
   supabase login
   ```

## Migration Steps

### Option 1: Using Supabase CLI (Recommended)

1. Navigate to your project directory:
   ```bash
   cd /path/to/royaltea
   ```

2. Link your project to Supabase:
   ```bash
   supabase link --project-ref hqqlrrqvjcetoxbdjgzx
   ```

3. Push the migration to your Supabase project:
   ```bash
   supabase db push
   ```

   This will apply all pending migrations, including the Retro Profile system migration.

### Option 2: Using Supabase Dashboard

If you prefer to apply the migration manually through the Supabase Dashboard:

1. Log in to the [Supabase Dashboard](https://app.supabase.io)
2. Select your project
3. Go to the SQL Editor
4. Copy the contents of the migration file: `supabase/migrations/20240701000003_create_retro_profile_system.sql`
5. Paste the SQL into the SQL Editor
6. Click "Run" to execute the migration

## Verification

After applying the migration, you can verify that it was successful by checking if the new columns exist in the `users` table:

1. Go to the Supabase Dashboard
2. Select your project
3. Go to the Table Editor
4. Select the `users` table
5. Check if the following columns exist:
   - `headline`
   - `location`
   - `website`
   - `cover_image_url`
   - `status_message`
   - `availability_status`
   - `profile_views`
   - `theme_settings`
   - `custom_css`
   - `profile_song_url`
   - `privacy_settings`

6. Also verify that the following tables were created:
   - `profile_comments`
   - `profile_views`
   - `top_collaborators`
   - `profile_themes`

## Troubleshooting

If you encounter any issues during the migration:

1. Check the Supabase logs for error messages
2. Ensure you have the necessary permissions to execute SQL statements
3. If specific statements fail, you can try executing them individually through the SQL Editor

For more information on using the Supabase CLI, refer to the [official documentation](https://supabase.com/docs/reference/cli/usage).
