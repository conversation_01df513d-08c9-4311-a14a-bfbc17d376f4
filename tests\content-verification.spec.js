// Content and Component Verification Test
const { test, expect } = require('@playwright/test');

/**
 * Comprehensive test to verify content and components are loading properly
 * on each page of the Royaltea platform
 */

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Define expected content for each working page
const PAGE_EXPECTATIONS = {
  '/': {
    name: 'Dashboard',
    expectedElements: [
      'Welcome', 'Dashboard', 'Active Projects', 'Recent Activity'
    ],
    requiredSelectors: [
      '[data-canvas-card]', // Grid tiles should be present
      'h1, h2, h3', // Some heading
      'button, a' // Interactive elements
    ]
  },
  '/start': {
    name: 'Start',
    expectedElements: [
      'Start', 'Begin', 'Journey', 'Project'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/track': {
    name: 'Track',
    expectedElements: [
      'Track', 'Progress', 'Work', 'Contribution'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/earn': {
    name: 'Earn',
    expectedElements: [
      'Earn', 'Revenue', 'Payment', 'Income'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/projects': {
    name: 'Projects/Ventures',
    expectedElements: [
      'Project', 'Venture', 'Management'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/project/wizard': {
    name: 'Project Wizard',
    expectedElements: [
      'Wizard', 'Create', 'New', 'Project', 'Venture'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'form, input, button'
    ]
  },
  '/missions': {
    name: 'Mission Board',
    expectedElements: [
      'Mission', 'Board', 'Discover', 'Claim'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/validation/metrics': {
    name: 'Validation',
    expectedElements: [
      'Validation', 'Metrics', 'Contribution'
    ],
    requiredSelectors: [
      'h1, h2, h3'
    ]
  },
  '/revenue': {
    name: 'Revenue',
    expectedElements: [
      'Revenue', 'Tracking', 'Payment', 'Earning'
    ],
    requiredSelectors: [
      'h1, h2, h3'
    ]
  },
  '/analytics/contributions': {
    name: 'Analytics',
    expectedElements: [
      'Analytics', 'Performance', 'Insight', 'Contribution'
    ],
    requiredSelectors: [
      'h1, h2, h3'
    ]
  },
  '/analytics/insights': {
    name: 'AI Insights',
    expectedElements: [
      'AI', 'Insight', 'Intelligence', 'Analysis'
    ],
    requiredSelectors: [
      'h1, h2, h3'
    ]
  },
  '/profile': {
    name: 'Profile',
    expectedElements: [
      'Profile', 'User', 'Account', 'Settings'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/teams': {
    name: 'Alliances/Teams',
    expectedElements: [
      'Alliance', 'Team', 'Management', 'Member'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/social': {
    name: 'Social',
    expectedElements: [
      'Social', 'Community', 'Feature'
    ],
    requiredSelectors: [
      'h1, h2, h3'
    ]
  },
  '/settings': {
    name: 'Settings',
    expectedElements: [
      'Settings', 'Configuration', 'Preference'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'form, input, button'
    ]
  },
  '/notifications': {
    name: 'Notifications',
    expectedElements: [
      'Notification', 'Alert', 'Message'
    ],
    requiredSelectors: [
      'h1, h2, h3'
    ]
  },
  '/bugs': {
    name: 'Bug Reports',
    expectedElements: [
      'Bug', 'Report', 'Issue'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'form, input, button'
    ]
  },
  '/learn': {
    name: 'Learning',
    expectedElements: [
      'Learn', 'Education', 'Tutorial', 'Guide'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  },
  '/help': {
    name: 'Help Center',
    expectedElements: [
      'Help', 'Support', 'FAQ', 'Documentation'
    ],
    requiredSelectors: [
      'h1, h2, h3',
      'button, a'
    ]
  }
};

test.describe('Content and Component Verification', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to production and authenticate
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const needsAuth = await page.locator('input[type="email"]').isVisible();
    
    if (needsAuth) {
      await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
      await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
    }
  });

  // Test each page individually
  Object.entries(PAGE_EXPECTATIONS).forEach(([route, expectations]) => {
    test(`should verify content on ${expectations.name} (${route})`, async ({ page }) => {
      console.log(`🔍 Testing ${expectations.name} at ${route}`);
      
      // Navigate to the page
      await page.goto(`${PRODUCTION_URL}${route}`);
      await page.waitForLoadState('networkidle');
      
      // Wait for content to load
      await page.waitForTimeout(2000);
      
      // Get page info
      const title = await page.title();
      const url = page.url();
      const bodyText = await page.textContent('body');
      
      console.log(`   📄 Title: ${title}`);
      console.log(`   🌐 URL: ${url}`);
      
      // Check if page loaded successfully (not 404)
      expect(title).not.toMatch(/404|not found/i);
      expect(bodyText).not.toMatch(/404|page not found/i);
      
      // Check for required selectors
      for (const selector of expectations.requiredSelectors) {
        const elements = await page.locator(selector).count();
        console.log(`   ✅ ${selector}: ${elements} elements found`);
        expect(elements).toBeGreaterThan(0);
      }
      
      // Check for expected content (at least one should match)
      let contentFound = false;
      const foundContent = [];
      
      for (const expectedText of expectations.expectedElements) {
        if (bodyText && bodyText.toLowerCase().includes(expectedText.toLowerCase())) {
          foundContent.push(expectedText);
          contentFound = true;
        }
      }
      
      if (contentFound) {
        console.log(`   📝 Content found: ${foundContent.join(', ')}`);
      } else {
        console.log(`   ⚠️  Expected content not found. Page content preview:`);
        console.log(`   ${bodyText?.substring(0, 200)}...`);
      }
      
      // At least some expected content should be present
      expect(contentFound).toBe(true);
      
      // Check for React/JavaScript errors
      const errors = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      // Wait a moment for any async operations
      await page.waitForTimeout(1000);
      
      if (errors.length > 0) {
        console.log(`   ⚠️  JavaScript errors: ${errors.length}`);
        errors.forEach(error => console.log(`     - ${error}`));
      }
      
      console.log(`   ✅ ${expectations.name} verification complete`);
    });
  });

  test('should verify all pages have basic layout components', async ({ page }) => {
    console.log('🔍 Testing basic layout components across all pages...');
    
    const results = [];
    
    for (const [route, expectations] of Object.entries(PAGE_EXPECTATIONS)) {
      await page.goto(`${PRODUCTION_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      const layoutCheck = {
        route: route,
        name: expectations.name,
        hasHeading: await page.locator('h1, h2, h3').count() > 0,
        hasNavigation: await page.locator('nav, [role="navigation"]').count() > 0,
        hasInteractiveElements: await page.locator('button, a, input').count() > 0,
        hasMainContent: await page.locator('main, [role="main"]').count() > 0,
        bodyTextLength: (await page.textContent('body'))?.length || 0
      };
      
      results.push(layoutCheck);
      
      console.log(`   ${expectations.name}: ` +
        `${layoutCheck.hasHeading ? '✅' : '❌'} Heading | ` +
        `${layoutCheck.hasInteractiveElements ? '✅' : '❌'} Interactive | ` +
        `${layoutCheck.bodyTextLength > 100 ? '✅' : '❌'} Content`);
    }
    
    // Generate summary
    const totalPages = results.length;
    const pagesWithHeadings = results.filter(r => r.hasHeading).length;
    const pagesWithInteractive = results.filter(r => r.hasInteractiveElements).length;
    const pagesWithContent = results.filter(r => r.bodyTextLength > 100).length;
    
    console.log('');
    console.log('📊 LAYOUT COMPONENT SUMMARY');
    console.log(`   Total Pages: ${totalPages}`);
    console.log(`   Pages with Headings: ${pagesWithHeadings}/${totalPages}`);
    console.log(`   Pages with Interactive Elements: ${pagesWithInteractive}/${totalPages}`);
    console.log(`   Pages with Substantial Content: ${pagesWithContent}/${totalPages}`);
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalPages,
        pagesWithHeadings,
        pagesWithInteractive,
        pagesWithContent
      },
      pages: results
    };
    
    const fs = require('fs');
    const path = require('path');
    
    const reportsDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const reportPath = path.join(reportsDir, `content-verification-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📄 Report saved: ${reportPath}`);
    
    // Expect most pages to have basic components
    expect(pagesWithHeadings / totalPages).toBeGreaterThan(0.8); // 80% should have headings
    expect(pagesWithInteractive / totalPages).toBeGreaterThan(0.7); // 70% should have interactive elements
    expect(pagesWithContent / totalPages).toBeGreaterThan(0.9); // 90% should have substantial content
  });
});
