<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SPA Routing Fix</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #0f172a;
    }
    .button {
      display: inline-block;
      background-color: #3b82f6;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      text-decoration: none;
      margin: 5px;
      cursor: pointer;
    }
    .button:hover {
      background-color: #2563eb;
    }
    pre {
      background-color: #f1f5f9;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .info {
      background-color: #e0f2fe;
      border-left: 4px solid #0ea5e9;
      padding: 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>SPA Routing Fix</h1>
  
  <div class="info">
    <p>This tool helps diagnose and fix Single Page Application (SPA) routing issues on Netlify.</p>
    <p>If you're experiencing 404 errors when refreshing pages or using the back button, this might help.</p>
  </div>
  
  <h2>Current URL Information</h2>
  <pre id="url-info"></pre>
  
  <h2>Test Navigation</h2>
  <div>
    <a href="/" class="button">Home</a>
    <a href="/profile" class="button">Profile</a>
    <a href="/admin/roadmap" class="button">Roadmap</a>
    <a href="/non-existent-page" class="button">404 Page</a>
  </div>
  
  <h2>Browser History</h2>
  <div>
    <button onclick="goBack()" class="button">Go Back</button>
    <button onclick="goForward()" class="button">Go Forward</button>
    <button onclick="refreshPage()" class="button">Refresh Page</button>
  </div>
  
  <h2>Fix Routing Issues</h2>
  <div>
    <button onclick="clearCache()" class="button">Clear Browser Cache</button>
    <button onclick="fixLocalStorage()" class="button">Fix localStorage</button>
    <button onclick="testRedirects()" class="button">Test Redirects</button>
  </div>
  
  <script>
    // Display URL information
    function updateUrlInfo() {
      const urlInfo = {
        href: window.location.href,
        protocol: window.location.protocol,
        host: window.location.host,
        pathname: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash,
        origin: window.location.origin
      };
      
      document.getElementById('url-info').textContent = JSON.stringify(urlInfo, null, 2);
    }
    
    // Navigation functions
    function goBack() {
      window.history.back();
    }
    
    function goForward() {
      window.history.forward();
    }
    
    function refreshPage() {
      window.location.reload();
    }
    
    // Fix functions
    function clearCache() {
      if (confirm('This will clear your browser cache for this site. Continue?')) {
        // Clear localStorage
        localStorage.clear();
        
        // Clear sessionStorage
        sessionStorage.clear();
        
        // Clear cookies (for this domain)
        document.cookie.split(';').forEach(cookie => {
          const eqPos = cookie.indexOf('=');
          const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
          document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
        });
        
        // Force reload from server
        window.location.reload(true);
      }
    }
    
    function fixLocalStorage() {
      try {
        // Test localStorage
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        
        // Check for corrupted data
        const keys = Object.keys(localStorage);
        let corrupted = [];
        
        keys.forEach(key => {
          try {
            JSON.parse(localStorage.getItem(key));
          } catch (e) {
            corrupted.push(key);
          }
        });
        
        if (corrupted.length > 0) {
          if (confirm(`Found ${corrupted.length} corrupted items in localStorage. Remove them?`)) {
            corrupted.forEach(key => localStorage.removeItem(key));
            alert('Corrupted items removed.');
          }
        } else {
          alert('No issues found with localStorage.');
        }
      } catch (e) {
        alert('Error accessing localStorage: ' + e.message);
      }
    }
    
    function testRedirects() {
      // Create a test URL that should trigger the Netlify redirect
      const testUrl = window.location.origin + '/test-redirect-' + Date.now();
      
      // Open in a new tab
      window.open(testUrl, '_blank');
    }
    
    // Initialize
    updateUrlInfo();
    
    // Update URL info when the URL changes
    window.addEventListener('popstate', updateUrlInfo);
  </script>
</body>
</html>
