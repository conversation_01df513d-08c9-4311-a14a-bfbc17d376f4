# CITY OF GAMERS INC.

# DETAILED CONTRIBUTOR AGREEMENT

THIS CONTRIBUTOR AGREEMENT (this "Agreement") is made as of [ ], 20[__] by and between City of Gamers Inc., a Florida corporation ("Company"), and [_____] (the "Contributor").

## BACKGROUND

A. Company is developing a creative work titled "Village of The Ages" (the "Work Product").

B. Contributor has agreed to provide certain services to Company in connection with the Work Product.

C. The parties wish to set out the terms on which Contributor will provide the services and assign all intellectual property rights in the deliverables to Company.

D. This Agreement includes comprehensive provisions regarding intellectual property rights, confidentiality, revenue sharing, and other matters relevant to the collaborative development of the Work Product.

## AGREEMENT

In consideration of the mutual promises contained in this Agreement and other good and valuable consideration, the receipt and sufficiency of which are hereby acknowledged, the parties agree as follows:

## 1. DEFINITIONS

1.1 **"Confidential Information"** means all non-public information disclosed by Company to Contributor, whether orally, in writing, or by any other means, that is designated as confidential or that reasonably should be understood to be confidential given the nature of the information and the circumstances of disclosure, including but not limited to: (a) non-public information relating to Company's technology, customers, business plans, marketing activities, finances and other business affairs; (b) third-party information that Company is obligated to keep confidential; (c) the terms and conditions of this Agreement; and (d) any technical or business information of a third party provided to Company by Contributor in connection with the Services.

1.2 **"Contribution Points"** means the points assigned to Contributor based on the Contribution Point System for the purpose of calculating Contributor's share of revenue.

1.3 **"Contribution Point System"** means the system used by Company to track and calculate contributions to the Work Product, as further described in Schedule B.

1.4 **"Deliverables"** means all work product resulting from the Services, including but not limited to: (a) all software, source code, object code, documentation, and other materials developed by Contributor in connection with the Services; (b) all modifications, improvements, and derivative works of the foregoing; and (c) all intellectual property rights in the foregoing.

1.5 **"Developed IP"** means all intellectual property rights in the Deliverables, including but not limited to: (a) patents, patent applications, and patent rights; (b) copyrights, copyright registrations and applications, and all other rights corresponding to the foregoing throughout the world; (c) rights in trade secrets and other confidential information; and (d) any other intellectual property rights in the Deliverables.

1.6 **"Good Industry Practice"** means the exercise of the degree of skill, care, prudence, efficiency, foresight and timeliness which would reasonably be expected from a skilled and experienced person engaged in the same type of undertaking as that of Contributor under the same or similar circumstances.

1.7 **"Launch"** means the date on which the Work Product is first made available to the public.

1.8 **"Milestone"** means a specific deliverable or set of deliverables to be completed by Contributor as part of the Services, as set forth in Exhibit II.

1.9 **"Net Revenue"** means the gross revenue received by Company from the exploitation of the Work Product, less the deductions specified in Schedule B.

1.10 **"Programs"** means any and all computer programs, applications, and code, including but not limited to source code, object code, firmware, development tools, files, records, data, and related documentation, and all portions, copies, modifications, improvements, and derivative works thereof, developed by Contributor in connection with the development and coding of video games, software, and related products and services.

1.11 **"Revenue Tranch"** means a specific portion of revenue allocated to a specific period or version of the Work Product, as further described in Schedule B.

1.12 **"Services"** means the services to be provided by Contributor as described in Schedule A.

1.13 **"Support Services"** means the maintenance, technical support, and other services to be provided by Contributor after Launch, as described in Schedule A.

1.14 **"Work Product"** means the creative work titled "Village of The Ages" being developed by Company, including all versions, updates, and modifications thereof.

## 2. SERVICES

2.1 **Scope of Services.** Contributor shall provide the Services in accordance with the specifications set forth in Exhibit I and the roadmap set forth in Exhibit II. Contributor shall perform the Services in a professional manner and in accordance with Good Industry Practice.

2.2 **Changes to Services.** Company may request changes to the Services by providing written notice to Contributor. Contributor shall use commercially reasonable efforts to accommodate such changes, provided that if such changes will materially increase the cost or time required for Contributor to perform the Services, the parties shall negotiate in good faith an appropriate adjustment to the compensation and/or schedule.

2.3 **Acceptance of Deliverables.** Company shall have the right to review and test all Deliverables. If Company determines that a Deliverable does not conform to the specifications set forth in Exhibit I, Company shall notify Contributor of such nonconformity, and Contributor shall promptly correct such nonconformity at no additional cost to Company.

2.4 **Schedule.** Contributor shall perform the Services in accordance with the schedule set forth in Exhibit II. Time is of the essence in the performance of the Services.

2.5 **Reporting.** Contributor shall provide regular reports to Company regarding the status of the Services, as specified in Schedule A.

2.6 **Subcontractors.** Contributor shall not subcontract any portion of the Services without Company's prior written consent. If Company consents to the use of a subcontractor, Contributor shall remain responsible for the performance of the Services and shall ensure that the subcontractor complies with all terms of this Agreement.

## 3. COMPENSATION

3.1 **Revenue Share.** As consideration for the Services, Contributor shall be entitled to receive a share of Net Revenue as set forth in Schedule B.

3.2 **Expenses.** Contributor shall be responsible for all expenses incurred in connection with the performance of the Services, unless otherwise specified in Schedule B.

3.3 **Taxes.** Contributor shall be responsible for all taxes applicable to the compensation received under this Agreement.

3.4 **Records and Audit.** Company shall maintain accurate records of all revenue received from the exploitation of the Work Product. Contributor shall have the right to audit such records as specified in Schedule B.

## 4. INTELLECTUAL PROPERTY

4.1 **Assignment of Developed IP.** Contributor hereby irrevocably assigns to Company all right, title, and interest in and to the Developed IP, including all intellectual property rights therein. Contributor agrees to execute such documents and take such actions as Company may reasonably request to perfect Company's ownership of the Developed IP.

4.2 **Waiver of Moral Rights.** Contributor hereby irrevocably waives all moral rights in the Deliverables, including but not limited to the right to be identified as the author of the Deliverables and the right to object to derogatory treatment of the Deliverables.

4.3 **Background IP.** Contributor shall retain ownership of all intellectual property rights owned by Contributor prior to the commencement of the Services or developed by Contributor outside the scope of the Services ("Background IP"). Contributor hereby grants to Company a non-exclusive, perpetual, irrevocable, worldwide, royalty-free license to use, reproduce, modify, distribute, and otherwise exploit the Background IP solely in connection with the Work Product.

4.4 **Third-Party Materials.** Contributor shall not incorporate any third-party materials into the Deliverables without Company's prior written consent. If Company consents to the incorporation of third-party materials, Contributor shall obtain all necessary licenses and permissions for Company to use such materials in connection with the Work Product.

4.5 **Open Source Software.** Contributor shall not incorporate any open source software into the Deliverables without Company's prior written consent. If Company consents to the incorporation of open source software, Contributor shall ensure that such incorporation does not: (a) require Company to disclose or distribute the Work Product in source code form; (b) require Company to license the Work Product for the purpose of making derivative works; or (c) impose any other material limitation or restriction on Company's rights to use or distribute the Work Product.

## 5. TERM AND TERMINATION

5.1 **Term.** This Agreement shall commence on the date first written above and continue until the Services are completed, unless terminated earlier in accordance with this Agreement.

5.2 **Termination for Breach.** Either party may terminate this Agreement if the other party materially breaches this Agreement and fails to cure such breach within thirty (30) days after receiving written notice thereof.

5.3 **Termination for Convenience.** Company may terminate this Agreement for convenience upon thirty (30) days' written notice to Contributor. In the event of such termination, Company shall pay Contributor for all Services performed up to the date of termination.

5.4 **Effect of Termination.** Upon termination of this Agreement, Contributor shall promptly deliver to Company all Deliverables, whether completed or in progress. The provisions of Sections 3, 4, 6, 7, and 8 shall survive the termination of this Agreement.

## 6. CONFIDENTIALITY

6.1 **Confidentiality Obligations.** Contributor shall: (a) hold the Confidential Information in strict confidence; (b) not disclose the Confidential Information to any third party without Company's prior written consent; (c) use the Confidential Information solely for the purpose of performing the Services; and (d) take all reasonable precautions to protect the confidentiality of the Confidential Information, including implementing reasonable security measures.

6.2 **Exceptions.** The confidentiality obligations in Section 6.1 shall not apply to information that: (a) is or becomes publicly available through no fault of Contributor; (b) was known to Contributor prior to disclosure by Company; (c) is rightfully received by Contributor from a third party without a duty of confidentiality; or (d) is required to be disclosed by law or court order, provided that Contributor shall provide Company with prompt notice of such requirement and cooperate with Company in seeking a protective order or other appropriate remedy.

6.3 **Return of Confidential Information.** Upon termination of this Agreement or at Company's request, Contributor shall promptly return to Company all Confidential Information in Contributor's possession or control.

## 7. REPRESENTATIONS AND WARRANTIES

7.1 **Mutual Representations and Warranties.** Each party represents and warrants that: (a) it has the full right, power, and authority to enter into this Agreement and to perform its obligations hereunder; (b) the execution and delivery of this Agreement and the performance of its obligations hereunder do not and will not conflict with or result in a breach of any other agreement to which it is a party or by which it is bound; and (c) it will comply with all applicable laws in connection with this Agreement.

7.2 **Contributor Representations and Warranties.** Contributor represents and warrants that: (a) the Deliverables will be original works of authorship created solely by Contributor; (b) the Deliverables will not infringe, misappropriate, or otherwise violate any intellectual property rights or other rights of any third party; (c) Contributor has the right to assign the Developed IP to Company as set forth in Section 4.1; and (d) the Deliverables will conform to the specifications set forth in Exhibit I.

7.3 **Disclaimer.** EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, NEITHER PARTY MAKES ANY REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.

## 8. LIMITATION OF LIABILITY

8.1 **Exclusion of Consequential Damages.** EXCEPT FOR BREACHES OF SECTIONS 4 (INTELLECTUAL PROPERTY) OR 6 (CONFIDENTIALITY), NEITHER PARTY SHALL BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES ARISING OUT OF OR RELATING TO THIS AGREEMENT, EVEN IF THE PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

8.2 **Limitation of Liability.** EXCEPT FOR BREACHES OF SECTIONS 4 (INTELLECTUAL PROPERTY) OR 6 (CONFIDENTIALITY), EACH PARTY'S TOTAL LIABILITY ARISING OUT OF OR RELATING TO THIS AGREEMENT SHALL NOT EXCEED THE GREATER OF: (A) THE TOTAL AMOUNT PAID OR PAYABLE BY COMPANY TO CONTRIBUTOR UNDER THIS AGREEMENT; OR (B) $10,000.

## 9. INDEMNIFICATION

9.1 **Contributor Indemnification.** Contributor shall indemnify, defend, and hold harmless Company and its officers, directors, employees, agents, and affiliates from and against any and all claims, damages, liabilities, costs, and expenses (including reasonable attorneys' fees) arising out of or relating to: (a) Contributor's breach of any representation, warranty, or covenant in this Agreement; (b) Contributor's negligence or willful misconduct; or (c) any claim that the Deliverables infringe, misappropriate, or otherwise violate any intellectual property rights or other rights of any third party.

9.2 **Company Indemnification.** Company shall indemnify, defend, and hold harmless Contributor from and against any and all claims, damages, liabilities, costs, and expenses (including reasonable attorneys' fees) arising out of or relating to: (a) Company's breach of any representation, warranty, or covenant in this Agreement; or (b) Company's negligence or willful misconduct.

9.3 **Indemnification Procedure.** The indemnified party shall: (a) promptly notify the indemnifying party of any claim for which indemnification is sought; (b) give the indemnifying party sole control over the defense and settlement of such claim; and (c) provide the indemnifying party with reasonable cooperation in the defense and settlement of such claim, at the indemnifying party's expense. The indemnified party may participate in the defense and settlement of such claim at its own expense.

## 10. GENERAL PROVISIONS

10.1 **Independent Contractor.** Contributor is an independent contractor and not an employee, agent, or partner of Company. Contributor shall be solely responsible for all taxes, withholdings, and other statutory or contractual obligations of any sort.

10.2 **Assignment.** Contributor may not assign this Agreement or any rights or obligations hereunder without Company's prior written consent. Company may assign this Agreement or any rights or obligations hereunder without Contributor's consent. Subject to the foregoing, this Agreement shall be binding upon and inure to the benefit of the parties and their respective successors and permitted assigns.

10.3 **Notices.** All notices under this Agreement shall be in writing and shall be deemed given when delivered personally, sent by confirmed email, or sent by certified or registered mail, return receipt requested, to the address specified in the signature block or to such other address as the party may specify in writing.

10.4 **Governing Law.** This Agreement shall be governed by and construed in accordance with the laws of the State of Florida, without regard to its conflict of laws principles.

10.5 **Dispute Resolution.** Any dispute arising out of or relating to this Agreement shall be resolved in the courts of Orange County, Florida, and the parties hereby consent to the exclusive jurisdiction of such courts.

10.6 **Entire Agreement.** This Agreement, including all schedules and exhibits hereto, constitutes the entire agreement between the parties with respect to the subject matter hereof and supersedes all prior or contemporaneous communications, representations, or agreements, whether oral or written.

10.7 **Amendment.** This Agreement may be amended only by a written instrument signed by both parties.

10.8 **Waiver.** No waiver of any provision of this Agreement shall be effective unless in writing and signed by the party against whom such waiver is sought to be enforced. No failure or delay by either party in exercising any right, power, or remedy under this Agreement shall operate as a waiver of any such right, power, or remedy.

10.9 **Severability.** If any provision of this Agreement is held to be invalid or unenforceable, the remaining provisions shall continue in full force and effect.

10.10 **Counterparts.** This Agreement may be executed in counterparts, each of which shall be deemed an original, but all of which together shall constitute one and the same instrument.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

**COMPANY:**

City of Gamers Inc.

By: _________________________
Name: Gynell Journigan
Title: President
Date: _______________________
Address: 1205 43rd Street, Suite B, Orlando, Florida 32839

**CONTRIBUTOR:**

[If a company]
_____________________________
By: _________________________
Name: _______________________
Title: _______________________
Date: _______________________
Address: ____________________

[If an individual]
Name: _______________________
Date: _______________________
Address: ____________________

## SCHEDULE A
### Description of Services

This project involves game development work on "Village of The Ages," a digital project where a village simulation game where players guide communities through historical progressions and manage resource-based challenges.

1. **Services**

   a. **General.** Pursuant to the terms and conditions of this Agreement and subject to Company's acceptance, Contributor shall:
      i. develop the Work Product following the requirements and technical specifications set forth in Exhibit I and in accordance with the roadmap set forth in Exhibit II and Good Industry Practice ("Developing Services"); and
      ii. provide the Support Services in accordance with Good Industry Practice.

   b. **Performance.** Contributor understands and agrees that Contributor is solely responsible for the control and supervision of the means by which the Services are provided consistent with the goal of successfully completing the Services on time. Contributor shall allocate sufficient resources to ensure that it performs its Services under this Agreement in accordance with the roadmap in Exhibit II and in accordance with Good Industry Practice.

   c. **Co-operation.**
      i. During the period from the Effective Date until Launch, unless directed otherwise by Company, Contributor shall attend at least weekly calls with Company, with frequency increasing during crunch periods as needed, and provide builds (application versions) every two (2) weeks with Company.
      ii. The Parties shall share responsibility for Work Product Management as agreed between the Parties from time to time and as outlined in Exhibit II. If the Parties are unable to reach agreement in respect of a decision in relation to Work Product Management, Company's decision shall prevail.
      iii. If there is additional need for development that is not in the agreed roadmap the Parties will negotiate in good faith the timeline for Contributor to deliver such further development. The Parties will also negotiate in good faith the costs to deliver such further development.

   d. **Cessation of Services following Launch & Right of First Refusal.**
      i. Following Launch, Company may determine that it no longer wishes to receive the Services and Contributor may determine that it no longer wishes to provide the Services, each at its sole discretion. In such case the relevant Party will notify the other Party in writing.
      ii. If Contributor notifies Company that it no longer wishes to provide Services to Company, Contributor's obligations to provide the Services will cease within 7 (seven) days of that notice (or such other period as agreed by the Parties) and this Agreement shall terminate automatically.
      iii. If Company intends to continue development of the Work Product after Contributor ceases providing Services, Company may notify Contributor in writing, and both Parties may negotiate in good faith the terms for such further development.

2. **Approval.**

   a. When Contributor considers that it has progressed the Work Product such that it reaches a Milestone, the Work Product shall be submitted to Company for written approval ("Approval"). Company will assess and/or test each delivered Milestone and will notify Contributor if it is accepted or rejected within 7 (seven) business days after receipt, though the parties acknowledge that in practice, feedback will typically be provided as soon as possible as determined by the development team. In case the Work Product does not, in Company's reasonable opinion, satisfy the Milestone or otherwise meet the technical specifications set forth in Exhibit I, Contributor will, at Company's request, promptly repair or redo the Services wholly or in part, with an initial response time of 7 (seven) days from the date on which feedback is received. For complex issues or features that reasonably require additional time, Contributor shall demonstrate meaningful progress during weekly meetings until the issue is resolved to Company's satisfaction.

   b. Notwithstanding Section 2(a), if Company fails to notify Contributor that the Milestone is rejected within 7 (seven) days of its submission it shall be deemed approved. In case of deemed acceptance, Company shall still be entitled to provide feedback on the delivered Milestone and Contributor shall promptly repair or redo the Services wholly or in part, whenever the Services are deemed to be incomplete or not in conformity with the provisions of this Agreement or incompatible for the intended purposes.

   c. Unless Section 2(b) applies, if Contributor fails to demonstrate meaningful progress on repairing or redoing the Services within the applicable timeframes, Company may suspend any payment obligation until such time as meaningful progress is demonstrated to Company's satisfaction. If the Contributor fails to demonstrate meaningful progress for a period exceeding 14 (fourteen) days despite weekly check-ins, the Company may terminate this Agreement. For purposes of this Agreement, "meaningful progress" shall include, but not be limited to, regular communication, clear articulation of challenges, implementation of agreed-upon solutions, and demonstration of advancement toward resolving the identified issues.

   d. **Source Code.** Contributor shall provide to Company not less than once every month (or such frequency agreed by the Parties), and each time the Work Product is submitted for Approval, an up-to-date copy of any source code for the Work Product which comprises Background IP of Contributor or Developed IP.

   e. **Change Control.** Any changes to the Specifications will be set forth in writing and signed by both Parties before they shall take effect. Contributor may not unreasonably decline to accept any change requests that reduce the cost of performance of the Services for Contributor. Contributor may not decline any change requests that increase the cost or magnitude of performance of the Services for Contributor, provided that the changes are reasonable in scope.

## SCHEDULE B
### COMPENSATION

1. **Revenue Share Model**

   a. **Revenue Share Percentage:** Contributor shall be entitled to 33% of Net Revenue generated by the Work Product, subject to the terms and conditions set forth in this Agreement.

   b. **Calculation Method:** The CoG Model will be used to calculate Contributor's share of revenue, with the following weights:
      - Tasks completed: 33%
      - Hours contributed: 33%
      - Task difficulty: 34%

   c. **Minimum Payout Threshold:** No payments shall be made until the Contributor's share of Net Revenue reaches $1,000.00.

   d. **Maximum Payout Cap:** The total compensation to Contributor under this Agreement shall not exceed $1,000,000.00.

2. **Payment Terms**

   a. **Payment Schedule:** Payments shall be made quarterly within 30 days after the end of each calendar quarter.

   b. **Payment Method:** Payments shall be made via electronic transfer to Contributor's designated account.

   c. **Reporting:** Company shall provide Contributor with a detailed report of revenue calculations with each payment.

3. **Revenue Tranches**

   a. **Definition:** Revenue shall be allocated according to the following tranches:
      - Initial Release: From launch date until 12 months post-launch
      - Ongoing Revenue: All revenue generated after the Initial Release period

   b. **Platform Fees:** Platform fees (e.g., Steam, Epic Games Store, console platform fees) shall be deducted before calculating Net Revenue.

4. **Contribution Tracking**

   a. **Task Tracking:** All tasks must be properly documented in the Company's project management system.

   b. **Time Tracking:** Hours must be logged accurately using the Company's time tracking system.

   c. **Approval Process:** All contributions must be approved by the project manager before being counted toward revenue share calculations.

5. **Termination and Buyout**

   a. **Early Termination:** If this Agreement is terminated prior to Launch, Contributor shall be entitled to a pro-rated share of revenue based on contributions made up to the termination date.

   b. **Buyout Option:** Company reserves the right to buy out Contributor's revenue share rights at any time for a lump sum payment equal to 3x the projected annual revenue share, based on the previous 12 months of revenue or reasonable projections if less than 12 months of data is available.

## EXHIBIT I
### SPECIFICATIONS

**Village of The Ages - Project Specifications**

**Project Overview:**
A village simulation game where players guide communities through historical progressions and manage resource-based challenges. The project features engaging gameplay mechanics and progression systems designed to create a compelling player experience.

**Core Features:**

1. **Core Gameplay Mechanics**
   - Primary gameplay systems
   - Player interaction model
   - Challenge and progression systems
   - Resource management

2. **Game World & Environment**
   - World design and structure
   - Environmental systems
   - Interactive elements
   - Visual style and atmosphere

3. **Player Experience**
   - Character development
   - Skill progression
   - Reward systems
   - Engagement mechanics

4. **Technical Features**
   - Performance optimization
   - Cross-platform compatibility
   - Multiplayer functionality (if applicable)
   - Save system and persistence

**Technical Requirements:**

- Platform: PC, Mobile
- Engine: Unity
- Programming: C#
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- Version Control: Git-based source control with proper branching strategy

## EXHIBIT II
### PRODUCT ROADMAP

**Village of The Ages - Development Roadmap**

**Phase 1: Foundation (Month 1-2)**
- Core game systems functional
- Basic gameplay mechanics implemented
- First playable prototype delivered

**Phase 2: Core Gameplay (Month 3-4)**
- Primary gameplay loops completed
- Player progression framework implemented
- Environmental systems integrated

**Phase 3: Content Development (Month 5-6)**
- Game world fully implemented
- Complete feature set integrated
- Full UI implementation

**Phase 4: Polish & Finalization (Month 7-8)**
- Balance adjustments based on testing
- Performance optimization
- Final art and audio integration
- Release preparation

**Milestones:**

1. **Milestone 1: Foundation (End of Month 2)**
   - Core game systems functional
   - Basic gameplay mechanics implemented
   - First playable prototype delivered

2. **Milestone 2: Core Gameplay (End of Month 4)**
   - Primary gameplay loops completed
   - Player progression framework implemented
   - Environmental systems integrated

3. **Milestone 3: Content Development (End of Month 6)**
   - Game world fully implemented
   - Complete feature set integrated
   - Full UI implementation

4. **Milestone 4: Polish & Finalization (End of Month 8)**
   - Balance adjustments based on testing
   - Performance optimization
   - Final art and audio integration
   - Release preparation
