/**
 * Adapter for the NewAgreementGenerator class
 * 
 * This adapter allows the NewAgreementGenerator class to be used in a Node.js environment
 * by converting the ES module to CommonJS.
 */

// Import the original class definition
const originalCode = require('fs').readFileSync(
  require('path').join(__dirname, '../src/utils/agreement/newAgreementGenerator.js'),
  'utf8'
);

// Extract the class definition
const classDefinition = originalCode.match(/export class NewAgreementGenerator \{[\s\S]*\}/)[0];

// Remove the export keyword
const modifiedClassDefinition = classDefinition.replace('export class', 'class');

// Create a new module with the class definition
const moduleCode = `
${modifiedClassDefinition}

module.exports = { NewAgreementGenerator };
`;

// Write the module to a temporary file
const tempFilePath = require('path').join(__dirname, 'temp-agreement-generator.js');
require('fs').writeFileSync(tempFilePath, moduleCode, 'utf8');

// Export the class
module.exports = require('./temp-agreement-generator');
