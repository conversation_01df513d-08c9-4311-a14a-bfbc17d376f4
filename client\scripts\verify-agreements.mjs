/**
 * Agreement Verification Script (ES Module Version)
 *
 * This script verifies the generated agreements by checking for:
 * 1. Proper placeholder replacement
 * 2. Project-specific content
 * 3. Correct formatting
 *
 * Usage: node verify-agreements.mjs
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define the output directory
const outputDir = path.join(__dirname, '../test-output/agreements');

// Define the verification criteria for each project type
const verificationCriteria = {
  'game-project': {
    projectName: 'Stellar Conquest',
    companyName: 'Cosmic Games LLC',
    contributorName: '<PERSON>',
    projectType: 'game',
    expectedPhrases: [
      'Stellar Conquest',
      'Cosmic Games LLC',
      'space strategy game',
      'interstellar empires',
      'Jane Smith',
      'California corporation',
      'San Francisco County',
      'Unity',
      'PC, Mac, Mobile'
    ],
    unexpectedPhrases: [
      'Village of The Ages',
      'City of Gamers Inc.',
      'village simulation game',
      '<PERSON><PERSON><PERSON>',
      'Florida corporation',
      'Orange County'
    ]
  },
  'music-project': {
    projectName: 'Harmonic Echoes',
    companyName: 'Echo Studios',
    contributorName: 'Sarah <PERSON>',
    projectType: 'music',
    expectedPhrases: [
      'Harmonic Echoes',
      'Echo Studios',
      'collaborative album',
      'ambient electronic music',
      'Alex Rivera',
      'Tennessee corporation',
      'Davidson County',
      'Streaming Services',
      'tracks',
      'listening experience'
    ],
    unexpectedPhrases: [
      'Village of The Ages',
      'City of Gamers Inc.',
      'village simulation game',
      'Gynell Journigan',
      'Florida corporation',
      'Orange County',
      'gameplay',
      'players'
    ]
  },
  'software-project': {
    projectName: 'TaskFlow Pro',
    companyName: 'Productive Solutions Inc.',
    contributorName: 'Michael Chen',
    projectType: 'software',
    expectedPhrases: [
      'TaskFlow Pro',
      'Productive Solutions Inc.',
      'productivity app',
      'team collaboration',
      'project management',
      'Texas corporation',
      'Travis County',
      'React Native',
      'Web, iOS, Android',
      'user experience'
    ],
    unexpectedPhrases: [
      'Village of The Ages',
      'City of Gamers Inc.',
      'village simulation game',
      'Gynell Journigan',
      'Florida corporation',
      'Orange County',
      'gameplay',
      'players'
    ]
  }
};

/**
 * Verify a generated agreement
 * @param {string} filename - The filename of the agreement
 * @returns {Object} - Verification results
 */
function verifyAgreement(filename) {
  // Extract project type from filename
  const projectType = filename.split('-agreement.md')[0];

  // Get verification criteria for this project type
  const criteria = verificationCriteria[projectType];
  if (!criteria) {
    return {
      success: false,
      errors: [`No verification criteria found for project type: ${projectType}`]
    };
  }

  // Read the agreement file
  const filePath = path.join(outputDir, filename);
  const agreementText = fs.readFileSync(filePath, 'utf8');

  // Initialize verification results
  const results = {
    filename,
    projectType,
    success: true,
    errors: [],
    warnings: []
  };

  // Check for expected phrases
  for (const phrase of criteria.expectedPhrases) {
    // For music project, allow some flexibility in terminology
    if (projectType === 'music-project' &&
        (phrase === 'tracks' && agreementText.includes('track compositions')) ||
        (phrase === 'Streaming Services' && agreementText.includes('streaming platforms'))) {
      // These are acceptable alternatives
      continue;
    }

    if (!agreementText.includes(phrase)) {
      results.errors.push(`Expected phrase not found: "${phrase}"`);
      results.success = false;
    }
  }

  // Check for unexpected phrases
  for (const phrase of criteria.unexpectedPhrases) {
    if (agreementText.includes(phrase)) {
      results.errors.push(`Unexpected phrase found: "${phrase}"`);
      results.success = false;
    }
  }

  // Check for remaining placeholders
  const placeholderRegex = /\[([^\]]+)\]/g;
  const placeholders = [];
  let match;

  while ((match = placeholderRegex.exec(agreementText)) !== null) {
    // Ignore some common placeholders that might be legitimate
    const ignoredPlaceholders = ['Project Address', 'Project Owner Email'];
    if (!ignoredPlaceholders.includes(match[1])) {
      placeholders.push(match[1]);
    }
  }

  if (placeholders.length > 0) {
    results.warnings.push(`Possible unreplaced placeholders found: ${placeholders.join(', ')}`);
  }

  // Check for project-specific terminology
  if (criteria.projectType === 'music' && agreementText.includes('gameplay')) {
    results.errors.push('Game-specific terminology found in music project agreement');
    results.success = false;
  }

  if (criteria.projectType === 'software' && agreementText.includes('gameplay')) {
    results.errors.push('Game-specific terminology found in software project agreement');
    results.success = false;
  }

  // Check for exhibits
  if (!agreementText.includes('## EXHIBIT I') || !agreementText.includes('## EXHIBIT II')) {
    results.errors.push('Exhibits not found or improperly formatted');
    results.success = false;
  }

  return results;
}

/**
 * Run verification on all generated agreements
 */
function runVerification() {
  console.log('🔍 Verifying generated agreements...');

  try {
    // Check if output directory exists
    if (!fs.existsSync(outputDir)) {
      console.error(`❌ Output directory not found: ${outputDir}`);
      console.error('Run the test-agreement-generator.mjs script first to generate agreements.');
      return;
    }

    // Get all .md files in the output directory
    const files = fs.readdirSync(outputDir).filter(file => file.endsWith('-agreement.md'));

    if (files.length === 0) {
      console.error('❌ No agreement files found in the output directory.');
      console.error('Run the test-agreement-generator.mjs script first to generate agreements.');
      return;
    }

    console.log(`Found ${files.length} agreement files to verify.`);

    // Verify each agreement
    const results = [];
    for (const file of files) {
      console.log(`\nVerifying: ${file}`);
      const result = verifyAgreement(file);
      results.push(result);

      // Display results
      if (result.success) {
        console.log(`✅ Verification passed for ${file}`);
      } else {
        console.log(`❌ Verification failed for ${file}`);
      }

      if (result.errors.length > 0) {
        console.log('   Errors:');
        result.errors.forEach(error => console.log(`   - ${error}`));
      }

      if (result.warnings.length > 0) {
        console.log('   Warnings:');
        result.warnings.forEach(warning => console.log(`   - ${warning}`));
      }
    }

    // Summary
    const passedCount = results.filter(r => r.success).length;
    console.log('\n📊 Verification Summary:');
    console.log(`Total: ${results.length}`);
    console.log(`Passed: ${passedCount}`);
    console.log(`Failed: ${results.length - passedCount}`);

    if (passedCount === results.length) {
      console.log('\n✨ All agreements passed verification!');
    } else {
      console.log('\n⚠️ Some agreements failed verification. See errors above.');
    }

    // Save verification results to a file
    const resultsPath = path.join(outputDir, 'verification-results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2), 'utf8');
    console.log(`\nDetailed verification results saved to: ${resultsPath}`);

  } catch (error) {
    console.error('❌ Error during verification:', error);
  }
}

// Run the verification
runVerification();
