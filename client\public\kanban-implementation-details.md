# Kanban System & Integration Specification

## 1. Introduction & Purpose

**Goal:** To design and implement an internal Kanban system within the web application, followed by integrating external project management and communication tools (initially <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Discord) to enable a unified task view and automate contribution tracking using **Artificial Intelligence (AI)** for robust status mapping.

**Core Application:** The internal application is built with **React** (using **Vite**), with **Supabase** for the backend/database. This specification details the creation of an interactive **Kanban board UI** using **`@atlaskit/drag-and-drop`** as the primary task management interface within the app.

**Contribution Tracking:** A key driver is automating data collection for the contribution/royalty algorithm (detailed in "Royalty Algorithm Implementation Guide.pdf"), which relies on Tasks Completed, Hours Worked, and Task Difficulty. This will apply to both internal tasks created within the app and tasks synced from external tools.

This document outlines the phased development approach, the required database schema, details of the internal Kanban UI, the integration architecture, data mapping strategies (prioritizing AI for status mapping), and tool-specific considerations.

## 2. Example Database Schema

```sql
-- tasks Table Definition (Example SQL)
CREATE TABLE tasks (
    task_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(project_id),
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL, -- Internal status ('ToDo', 'InProgress', 'Review', 'Done', 'Blocked')
    assignee_id UUID REFERENCES users(user_id),
    task_type TEXT,
    difficulty_level TEXT,
    difficulty_points INTEGER DEFAULT 0,
    estimated_hours NUMERIC(5, 2),
    logged_hours NUMERIC(5, 2) DEFAULT 0.00,
    creation_timestamp TIMESTAMPTZ DEFAULT now(),
    completion_timestamp TIMESTAMPTZ,
    source_tool TEXT NOT NULL, -- ('jira', 'trello', 'codecks', 'discord', 'internal')
    external_tool_id TEXT, -- NULL for internal tasks
    external_status_name TEXT,
    external_tool_link TEXT,
    last_synced_timestamp TIMESTAMPTZ,
    UNIQUE (project_id, source_tool, external_tool_id)
);
CREATE INDEX idx_tasks_external ON tasks (source_tool, external_tool_id);
CREATE INDEX idx_tasks_project_status ON tasks (project_id, status);

-- projects Table Definition (Example SQL)
CREATE TABLE projects (
    project_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    weight_tasks_completed NUMERIC(3, 2) DEFAULT 0.30,
    weight_hours_worked NUMERIC(3, 2) DEFAULT 0.30,
    weight_task_difficulty NUMERIC(3, 2) DEFAULT 0.40,
    task_difficulty_points JSONB, -- Stores points per type/level
    integration_mappings JSONB, -- Stores status, type, difficulty mappings for integrations
    -- other project settings...
);

-- user_external_mappings Table Definition (Example SQL)
CREATE TABLE user_external_mappings (
    internal_user_id UUID NOT NULL REFERENCES users(user_id),
    source_tool TEXT NOT NULL, -- 'jira', 'trello', 'codecks', 'discord'
    external_user_id TEXT NOT NULL,
    external_username TEXT,
    access_token TEXT, -- Encrypted
    refresh_token TEXT, -- Encrypted
    token_expires_at TIMESTAMPTZ,
    last_refreshed_timestamp TIMESTAMPTZ,
    PRIMARY KEY (internal_user_id, source_tool),
    UNIQUE (source_tool, external_user_id)
);

-- users Table: Assumed to exist with user_id, name, email etc.

```

## 3. Development Phases (Roadmap)

Implementation will follow a phased approach to manage complexity, starting with the core internal Kanban functionality.

* **Phase 1: Core Setup & Internal Kanban UI Implementation**
    * **Goal:** Build the foundational Kanban board UI and backend support for managing tasks created *within* the application.
    * **Steps:**
        1.  Implement Database Schema: Set up the `users`, `projects`, and `tasks` tables in Supabase based on the definitions above.
        2.  Build Core React UI Components: Create reusable React components for the Kanban Board, Columns (representing internal statuses), and Task Cards using `@atlaskit/drag-and-drop` for drag-and-drop functionality.
        3.  Backend API (Internal Tasks): Implement backend API endpoints (e.g., using Supabase Functions or PostgREST) for:
            * Creating new tasks (`INSERT` into `tasks`, `source_tool` = 'internal').
            * Reading tasks for a specific project (`SELECT` from `tasks`).
            * Updating task status (triggered by drag-and-drop via `onDragEnd`, `UPDATE tasks SET status = ...`).
            * Updating task details (title, description, assignee, etc., via an edit modal/panel, `UPDATE tasks SET ...`).
            * Deleting internal tasks (`DELETE` from `tasks`).
        4.  Frontend Logic: Implement frontend logic to:
            * Fetch and display tasks for a selected project onto the board.
            * Handle drag-and-drop events (`onDragEnd`) to call the backend API for status updates.
            * Provide UI elements (e.g., buttons, modals) for creating new tasks and editing task details.
            * (Recommended) Implement real-time updates (e.g., Supabase Realtime) to keep the board view synchronized with database changes.
        5.  Basic Project Config UI: Create a simple UI for creating projects (setting `name`) and potentially viewing/setting royalty algorithm weights (`weight_*` fields).
        6.  Initial Contribution Trigger: Implement the core contribution calculation logic (based on the Royalty Algorithm PDF). Trigger this logic *only* when an *internal* task's status is updated to 'Done' via the backend API.

* **Phase 2: User Mapping & Basic Integration Layer**
    * **Goal:** Enable users to link external accounts and integrate *one* external tool, syncing tasks and using *explicit* status mapping.
    * **Steps:**
        1.  Implement `user_external_mappings` table.
        2.  Build UI for users to link external accounts (OAuth/token input), storing credentials securely and populating the mapping table.
        3.  Build Core Integration Architecture: Set up basic structure for webhook handling, API clients, and processing logic (e.g., using serverless functions).
        4.  Integrate One Tool (e.g., Trello or Jira):
            * Implement API client for the chosen tool.
            * Implement logic to fetch tasks and sync basic data (title, description, external ID/link, assignee mapping) into the `tasks` table (`source_tool` = 'jira'/'trello'). Handle task creation and updates based on external events (webhook/polling).
            * Implement **Explicit Status Mapping**: Use the `integration_mappings.statusMappings` configuration (defined in the `projects` table) to map the external tool's status names/IDs to internal statuses.
            * Extend Contribution Trigger: Ensure the contribution logic is triggered when a *synced* task's internal status becomes 'Done'.
        5.  Project Config UI (Mappings): Extend the project config UI to allow setting up `statusMappings` for the integrated tool.

* **Phase 3: AI Status Mapping & Remaining Core Integrations**
    * **Goal:** Implement the AI fallback for status mapping and integrate the other core external tools.
    * **Steps:**
        1.  Implement AI Fallback: Integrate the chosen AI approach (LLM API recommended) into the status mapping logic as the fallback when an explicit map entry is missing (see Section 6).
        2.  Implement Feedback Loop UI: Create UI for users to review/correct AI classifications and update the explicit `statusMappings`.
        3.  Integrate Remaining Tools (Jira/Trello, Codecks): Implement core sync (task creation/update, explicit + AI status mapping) for the other tools.
        4.  Implement Type/Difficulty Mapping: Implement logic to map external fields (issue types, labels, effort) to internal `task_type` and `difficulty_level` based on `integrationTypeDifficultyMappings` in project config. Calculate and store `difficulty_points`.
        5.  Implement Time Logging: Fetch `logged_hours` where available (Jira). Add/refine manual time logging options in the UI/bot.

* **Phase 4: Discord Integration & Advanced Features**
    * **Goal:** Integrate Discord for notifications/interaction and consider future enhancements.
    * **Steps:**
        1.  Implement Discord Integration: Set up webhook notifications for key events (task done, bounty posted). Implement Discord Bot for commands (`/logtime`) and potentially fragile post parsing (with manual overrides).
        2.  Refine UI/UX: Improve the application based on user feedback from earlier phases.
        3.  Consider Advanced Features: Explore bi-directional status sync, more sophisticated AI use cases, enhanced reporting, etc.

## 4. Internal Kanban UI Details

*(This section now describes the UI built in Phase 1)*

The internal Kanban board is the primary interface for task management within the application.

* **Technology:** Built using React and the `@atlaskit/drag-and-drop` library for core drag-and-drop interactions.
* **Components:** Consists of a main Board view containing multiple Columns (representing internal statuses like 'ToDo', 'InProgress', 'Done') which in turn contain draggable Cards (representing individual tasks from the `tasks` table).
* **Data Display:** Cards display key task information (title, assignee, potentially type/difficulty points, source tool icon).
* **Interaction:**
    * Users drag cards between columns to update status.
    * A button allows creating new 'internal' tasks.
    * Clicking a card opens a modal/panel for viewing/editing details (title, description, assignee, logging hours, setting type/difficulty for internal tasks).
* **Backend Communication:** All actions (fetching tasks, moving cards, creating/editing tasks) communicate with the application's backend API, which interacts with the Supabase database. Real-time updates are recommended for a smooth user experience.

## 5. Integration Architecture Overview

*(Describes the architecture needed for Phases 2-4. Content unchanged from previous version - API Clients, Webhook Handler, Polling, Mapping Logic, Auth Manager, Config Service)*

## 6. Deep Dive: Status Mapping Strategy (AI-Prioritized)

*(Describes the hybrid mapping approach used in Phases 2 & 3. Content unchanged from previous version - Explicit Mapping first, LLM API Fallback second, Feedback Loop)*

**6.1. Internal Statuses (Example)**

* `ToDo`, `InProgress`, `Review`, `Done` (**Triggers Contribution Calc**), `Blocked`

**6.2. Hybrid Mapping Approach (AI Fallback Prioritized)**

* Step 1: Explicit Configuration Mapping (Primary)
* Step 2: AI-Powered Fallback (Secondary - LLM API Recommended Start)
* Alternative AI Fallback (Machine Learning Classifier)
* Confidence & Review
* Feedback Loop

**6.3. Processing Flow Summary**
*(Steps 1-7 remain the same)*

## 7. Other Mappings Recap

*(Content unchanged from previous version - Assignee, Type/Difficulty, Time Logging)*

## 8. Tool-Specific Integration Notes

*(Content unchanged from previous version - Details for Jira, Trello, Codecks, Discord)*

## 9. Implementation Considerations

*(Content unchanged from previous version - Error Handling, Rate Limits, Security, Config UI, Testing, Idempotency, AI Costs/Prompts/Privacy)*

## 10. Conclusion

This specification outlines a phased approach to building an internal Kanban system and subsequently integrating external tools, leveraging AI for robust status mapping to support automated contribution tracking. Starting with the core internal UI (Phase 1) provides immediate value and a solid foundation before tackling the complexities of external integrations (Phases 2-4). Success depends on careful implementation phase-by-phase, robust error handling, clear configuration options, managing external dependencies, and providing strong feedback mechanisms.

