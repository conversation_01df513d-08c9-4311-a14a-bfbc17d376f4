-- SQL Script: Insert Updated Roadmap Data
-- This script inserts the complete updated roadmap data into Supabase
-- Run this AFTER running the schema update script

-- First, backup existing roadmap data (optional)
-- CREATE TABLE roadmap_backup AS SELECT * FROM roadmap;

-- Clear existing roadmap data (be careful with this in production!)
-- DELETE FROM roadmap;

-- Insert the updated roadmap data
-- Note: This is a simplified version - you may want to use the Node.js script instead for complex JSON data

-- For now, let's update the existing roadmap entry with the new structure
UPDATE roadmap 
SET 
  platform_status = '{
    "overall_completion": "95%",
    "backend_completion": "100%",
    "frontend_completion": "85%", 
    "integration_completion": "60%",
    "current_phase": "Component Integration & Connection",
    "next_milestone": "Eliminate all Under Construction messages",
    "production_readiness": "Ready for deployment with component connections"
  }'::jsonb,
  
  redesign_summary = '{
    "completed_date": "2025-06-15",
    "major_changes": [
      "Complete UI/UX redesign with modern design system",
      "Migration to Tailwind CSS + shadcn/ui components", 
      "Enterprise-grade backend infrastructure with 40+ API endpoints",
      "Advanced database schema with 43 production tables",
      "Comprehensive security and authentication system",
      "Real-time data synchronization and monitoring",
      "Production-ready deployment infrastructure"
    ],
    "current_priority": "Connect existing backend services to frontend components and eliminate placeholder content"
  }'::jsonb,
  
  latest_feature = 'Platform redesign complete - Backend services ready, frontend connection in progress',
  updated_at = '2025-06-15T00:00:00.000Z'::timestamptz,
  last_updated = NOW()

WHERE id = (
  SELECT id FROM roadmap 
  ORDER BY created_at DESC 
  LIMIT 1
);

-- If no existing roadmap, insert a new one (this would need the full data JSON)
-- You should use the Node.js script for this instead as it's easier to handle large JSON

-- Verify the update
SELECT 
  id,
  latest_feature,
  updated_at,
  last_updated,
  platform_status->>'overall_completion' as completion,
  platform_status->>'current_phase' as current_phase,
  redesign_summary->>'completed_date' as redesign_date
FROM roadmap 
ORDER BY created_at DESC 
LIMIT 1;

-- Show the data structure
SELECT 
  jsonb_pretty(platform_status) as platform_status,
  jsonb_pretty(redesign_summary) as redesign_summary
FROM roadmap 
ORDER BY created_at DESC 
LIMIT 1;

SELECT 'Roadmap data update completed!' as status;
