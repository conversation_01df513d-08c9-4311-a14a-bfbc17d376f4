import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { BarChart3, TrendingUp, Users, CheckCircle, Clock, Target, Zap } from 'lucide-react';

/**
 * Project Metrics Section
 * 
 * Displays comprehensive project analytics including:
 * - Project completion rates
 * - Team performance metrics
 * - Task distribution analysis
 * - Timeline and milestone tracking
 */
const ProjectMetrics = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState('all');
  const [projects, setProjects] = useState([]);
  const [metricsData, setMetricsData] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalTasks: 0,
    completedTasks: 0,
    totalHours: 0,
    averageCompletionTime: 0,
    teamEfficiency: 0,
    projectBreakdown: [],
    taskStatusDistribution: [],
    recentActivity: []
  });
  const [error, setError] = useState(null);

  // Load project metrics data
  useEffect(() => {
    if (currentUser) {
      loadProjectsAndMetrics();
    }
  }, [currentUser, selectedProject]);

  const loadProjectsAndMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load user's projects
      const { data: userProjects, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id,
          name,
          status,
          created_at,
          target_completion_date,
          project_members!inner (
            user_id,
            role
          )
        `)
        .eq('project_members.user_id', currentUser.id);

      if (projectsError) throw projectsError;

      setProjects(userProjects || []);

      // Load project metrics
      const projectIds = userProjects?.map(p => p.id) || [];
      if (projectIds.length === 0) {
        setMetricsData({
          totalProjects: 0,
          activeProjects: 0,
          completedProjects: 0,
          totalTasks: 0,
          completedTasks: 0,
          totalHours: 0,
          averageCompletionTime: 0,
          teamEfficiency: 0,
          projectBreakdown: [],
          taskStatusDistribution: [],
          recentActivity: []
        });
        return;
      }

      // Filter projects if specific project selected
      const filteredProjectIds = selectedProject === 'all' 
        ? projectIds 
        : [selectedProject];

      // Load tasks for projects
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          status,
          project_id,
          estimated_hours,
          logged_hours,
          created_at,
          completion_timestamp,
          difficulty_level,
          projects (
            id,
            name
          )
        `)
        .in('project_id', filteredProjectIds);

      if (tasksError) throw tasksError;

      // Load contributions for projects
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          hours_logged,
          project_id,
          contribution_date,
          projects (
            id,
            name
          )
        `)
        .in('project_id', filteredProjectIds);

      if (contributionsError) throw contributionsError;

      // Process metrics data
      const processedData = processProjectMetrics(
        userProjects.filter(p => selectedProject === 'all' || p.id === selectedProject),
        tasks || [],
        contributions || []
      );
      setMetricsData(processedData);

    } catch (error) {
      console.error('Error loading project metrics:', error);
      setError(error.message);
      toast.error('Failed to load project metrics');
    } finally {
      setLoading(false);
    }
  };

  const processProjectMetrics = (projects, tasks, contributions) => {
    const totalProjects = projects.length;
    const activeProjects = projects.filter(p => p.status === 'active').length;
    const completedProjects = projects.filter(p => p.status === 'completed').length;
    
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.status === 'Done' || t.status === 'completed').length;
    
    const totalHours = contributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0);
    
    // Calculate average completion time
    const completedTasksWithTime = tasks.filter(t => 
      t.completion_timestamp && t.created_at
    );
    const averageCompletionTime = completedTasksWithTime.length > 0
      ? completedTasksWithTime.reduce((sum, t) => {
          const created = new Date(t.created_at);
          const completed = new Date(t.completion_timestamp);
          return sum + (completed - created) / (1000 * 60 * 60 * 24); // days
        }, 0) / completedTasksWithTime.length
      : 0;

    // Team efficiency (completed tasks / total tasks * 100)
    const teamEfficiency = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // Project breakdown
    const projectBreakdown = projects.map(project => {
      const projectTasks = tasks.filter(t => t.project_id === project.id);
      const projectContributions = contributions.filter(c => c.project_id === project.id);
      const completedProjectTasks = projectTasks.filter(t => t.status === 'Done' || t.status === 'completed');
      
      return {
        id: project.id,
        name: project.name,
        status: project.status,
        totalTasks: projectTasks.length,
        completedTasks: completedProjectTasks.length,
        completionRate: projectTasks.length > 0 
          ? Math.round((completedProjectTasks.length / projectTasks.length) * 100)
          : 0,
        totalHours: projectContributions.reduce((sum, c) => sum + (c.hours_logged || 0), 0),
        created_at: project.created_at
      };
    });

    // Task status distribution
    const statusCounts = {};
    tasks.forEach(task => {
      const status = task.status || 'Unknown';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    const taskStatusDistribution = Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: Math.round((count / totalTasks) * 100)
    }));

    // Recent activity (last 10 contributions)
    const recentActivity = contributions
      .sort((a, b) => new Date(b.contribution_date) - new Date(a.contribution_date))
      .slice(0, 10)
      .map(c => ({
        id: c.id,
        hours: c.hours_logged,
        date: c.contribution_date,
        project: c.projects?.name || 'Unknown Project'
      }));

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      totalTasks,
      completedTasks,
      totalHours: Math.round(totalHours * 100) / 100,
      averageCompletionTime: Math.round(averageCompletionTime * 10) / 10,
      teamEfficiency,
      projectBreakdown,
      taskStatusDistribution,
      recentActivity
    };
  };

  const formatHours = (hours) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    return `${Math.round(hours * 10) / 10}h`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'done':
        return 'success';
      case 'active':
      case 'in progress':
      case 'inprogress':
        return 'primary';
      case 'blocked':
        return 'danger';
      case 'review':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading project metrics...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">⚠️</span>
            <h2 className="text-2xl font-bold text-white mb-4">Error Loading Metrics</h2>
            <p className="text-white/70 mb-4">{error}</p>
            <Button onClick={loadProjectsAndMetrics} color="primary" variant="bordered">
              Try Again
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
            <BarChart3 size={20} className="text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Project Metrics</h2>
            <p className="text-white/60">Track project performance and team productivity</p>
          </div>
        </div>
        
        <Select
          value={selectedProject}
          onChange={(e) => setSelectedProject(e.target.value)}
          className="w-48"
          size="sm"
        >
          <SelectItem key="all" value="all">All Projects</SelectItem>
          {projects.map(project => (
            <SelectItem key={project.id} value={project.id}>
              {project.name}
            </SelectItem>
          ))}
        </Select>
      </motion.div>

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Target size={24} className="text-blue-400" />
              <div>
                <p className="text-white/60 text-sm">Total Projects</p>
                <p className="text-2xl font-bold text-white">{metricsData.totalProjects}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <CheckCircle size={24} className="text-green-400" />
              <div>
                <p className="text-white/60 text-sm">Completed Tasks</p>
                <p className="text-2xl font-bold text-white">
                  {metricsData.completedTasks}/{metricsData.totalTasks}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Clock size={24} className="text-purple-400" />
              <div>
                <p className="text-white/60 text-sm">Total Hours</p>
                <p className="text-2xl font-bold text-white">{formatHours(metricsData.totalHours)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <Zap size={24} className="text-orange-400" />
              <div>
                <p className="text-white/60 text-sm">Team Efficiency</p>
                <p className="text-2xl font-bold text-white">{metricsData.teamEfficiency}%</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Project Breakdown */}
      {metricsData.projectBreakdown.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Project Performance</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {metricsData.projectBreakdown.map((project, index) => (
                  <div key={project.id} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-white font-medium">{project.name}</h4>
                        <Chip 
                          size="sm" 
                          color={getStatusColor(project.status)}
                          variant="flat"
                        >
                          {project.status}
                        </Chip>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                          style={{ width: `${project.completionRate}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-6 text-right">
                      <p className="text-white font-medium">{project.completionRate}%</p>
                      <p className="text-white/60 text-sm">
                        {project.completedTasks}/{project.totalTasks} tasks
                      </p>
                      <p className="text-white/60 text-sm">{formatHours(project.totalHours)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Task Status Distribution */}
      {metricsData.taskStatusDistribution.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <h3 className="text-lg font-semibold text-white">Task Status Distribution</h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {metricsData.taskStatusDistribution.map((status, index) => (
                  <div key={index} className="text-center">
                    <Chip 
                      size="lg" 
                      color={getStatusColor(status.status)}
                      variant="flat"
                      className="mb-2"
                    >
                      {status.status}
                    </Chip>
                    <p className="text-white font-bold text-xl">{status.count}</p>
                    <p className="text-white/60 text-sm">{status.percentage}%</p>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default ProjectMetrics;
