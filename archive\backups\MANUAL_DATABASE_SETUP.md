# MANUAL DATABASE SETUP GUIDE
## Day 3 - Complete Database Migration for VRC Compliance

### 🚨 CRITICAL: Manual Steps Required

The automated migration failed due to RLS policy restrictions. These steps must be completed manually via the Supabase Dashboard.

---

## 📋 STEP 1: FIX RLS POLICIES

### Fix team_members infinite recursion:

1. Go to **Supabase Dashboard** → **Authentication** → **Policies**
2. Find the `team_members` table policies
3. **Delete or disable** any policies causing infinite recursion
4. Create a simple policy:

```sql
-- Simple team_members policy
CREATE POLICY "team_members_policy" ON public.team_members
  FOR ALL USING (user_id = auth.uid() OR true);
```

---

## 📋 STEP 2: CREATE COMPLIANCE TABLES

### 1. Companies Table

```sql
CREATE TABLE public.companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    legal_name TEXT NOT NULL,
    tax_id TEXT NOT NULL UNIQUE,
    company_type TEXT NOT NULL CHECK (company_type IN ('corporation', 'llc', 'partnership', 'sole_proprietorship')),
    incorporation_state TEXT,
    incorporation_country TEXT DEFAULT 'US',
    incorporation_date DATE,
    doing_business_as TEXT,
    industry_classification TEXT,
    business_description TEXT,
    website_url TEXT,
    primary_address JSONB NOT NULL,
    mailing_address JSONB,
    primary_email TEXT NOT NULL,
    primary_phone TEXT,
    fiscal_year_end DATE DEFAULT (CURRENT_DATE + INTERVAL '1 year'),
    accounting_method TEXT DEFAULT 'accrual' CHECK (accounting_method IN ('accrual', 'cash')),
    base_currency TEXT DEFAULT 'USD',
    is_active BOOLEAN DEFAULT true,
    dissolution_date DATE,
    compliance_status TEXT DEFAULT 'active' CHECK (compliance_status IN ('active', 'suspended', 'dissolved')),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;

-- Simple policy
CREATE POLICY "companies_policy" ON public.companies
    FOR ALL USING (true);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.companies TO authenticated;
```

### 2. Add Company Fields to Teams Table

```sql
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES public.companies(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS is_business_entity BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS alliance_type TEXT DEFAULT 'emerging' CHECK (alliance_type IN ('emerging', 'established', 'solo'));
```

### 3. Financial Transactions Table

```sql
CREATE TABLE public.financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('commission', 'recurring_fee', 'royalty', 'expense', 'refund', 'bonus', 'salary')),
    transaction_category TEXT DEFAULT 'business_payment' CHECK (transaction_category IN ('business_payment', 'contractor_payment', 'employee_payment', 'expense_reimbursement')),
    gross_amount DECIMAL(12,2) NOT NULL CHECK (gross_amount >= 0),
    tax_amount DECIMAL(12,2) DEFAULT 0 CHECK (tax_amount >= 0),
    net_amount DECIMAL(12,2) NOT NULL CHECK (net_amount >= 0),
    currency TEXT DEFAULT 'USD',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    tax_category TEXT CHECK (tax_category IN ('1099-NEC', '1099-MISC', 'W2', 'exempt', 'international')),
    requires_1099 BOOLEAN DEFAULT false,
    requires_w2 BOOLEAN DEFAULT false,
    backup_withholding_rate DECIMAL(5,2) DEFAULT 0 CHECK (backup_withholding_rate >= 0 AND backup_withholding_rate <= 100),
    tax_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
    payer_company_id UUID REFERENCES public.companies(id),
    payee_user_id UUID REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'processing', 'paid', 'failed', 'cancelled', 'disputed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    payment_method TEXT CHECK (payment_method IN ('ach', 'wire', 'check', 'paypal', 'stripe', 'manual')),
    external_transaction_id TEXT,
    approval_required BOOLEAN DEFAULT true,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,
    description TEXT NOT NULL,
    reference_number TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

ALTER TABLE public.financial_transactions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "financial_transactions_policy" ON public.financial_transactions FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.financial_transactions TO authenticated;
```

### 4. Commission Payments Table

```sql
CREATE TABLE public.commission_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    financial_transaction_id UUID NOT NULL REFERENCES public.financial_transactions(id) ON DELETE CASCADE,
    sales_amount DECIMAL(12,2) NOT NULL CHECK (sales_amount >= 0),
    commission_rate DECIMAL(5,2) NOT NULL CHECK (commission_rate >= 0 AND commission_rate <= 100),
    commission_amount DECIMAL(12,2) NOT NULL CHECK (commission_amount >= 0),
    sale_date DATE NOT NULL,
    product_or_service TEXT NOT NULL,
    client_reference TEXT,
    sales_rep_id UUID NOT NULL REFERENCES auth.users(id),
    payment_due_date DATE,
    payment_terms TEXT DEFAULT 'net_30' CHECK (payment_terms IN ('immediate', 'net_15', 'net_30', 'net_60')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

ALTER TABLE public.commission_payments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "commission_payments_policy" ON public.commission_payments FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.commission_payments TO authenticated;
```

### 5. Recurring Fees Table

```sql
CREATE TABLE public.recurring_fees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    payee_user_id UUID NOT NULL REFERENCES auth.users(id),
    fee_type TEXT NOT NULL CHECK (fee_type IN ('talent_fee', 'subscription', 'retainer', 'maintenance')),
    amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
    currency TEXT DEFAULT 'USD',
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'annually')),
    start_date DATE NOT NULL,
    end_date DATE,
    next_payment_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    paused_until DATE,
    description TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

ALTER TABLE public.recurring_fees ENABLE ROW LEVEL SECURITY;
CREATE POLICY "recurring_fees_policy" ON public.recurring_fees FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.recurring_fees TO authenticated;
```

---

## 📋 STEP 3: INSERT SAMPLE DATA

### VRC Company Data

```sql
INSERT INTO public.companies (
    legal_name, tax_id, company_type, incorporation_state, incorporation_country,
    doing_business_as, industry_classification, business_description, website_url,
    primary_address, primary_email, primary_phone, fiscal_year_end, accounting_method
) VALUES (
    'VRC Entertainment LLC', 
    '12-3456789', 
    'llc', 
    'CA', 
    'US',
    'VRC Films', 
    '512110', 
    'Independent film production and talent management company specializing in commission-based sales and recurring talent fees',
    'https://vrcfilms.com',
    '{"street": "123 Hollywood Blvd", "city": "Los Angeles", "state": "CA", "zip": "90028", "country": "US"}',
    '<EMAIL>', 
    '******-123-4567', 
    '2024-12-31', 
    'accrual'
) ON CONFLICT (tax_id) DO UPDATE SET
    business_description = EXCLUDED.business_description,
    updated_at = now();
```

---

## 📋 STEP 4: CONFIRM TEST USERS

### Confirm Email Addresses

1. Go to **Supabase Dashboard** → **Authentication** → **Users**
2. Find these test users:
   - `<EMAIL>`
   - `<EMAIL>`
   - `<EMAIL>`
3. **Manually confirm** their email addresses by clicking the confirm button

---

## 📋 STEP 5: CREATE INDEXES

```sql
-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_companies_tax_id ON public.companies(tax_id);
CREATE INDEX IF NOT EXISTS idx_companies_active ON public.companies(is_active);
CREATE INDEX IF NOT EXISTS idx_teams_company_id ON public.teams(company_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_company ON public.financial_transactions(company_id, tax_year);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_payee ON public.financial_transactions(payee_user_id, tax_year);
CREATE INDEX IF NOT EXISTS idx_commission_payments_sales_rep ON public.commission_payments(sales_rep_id, sale_date);
CREATE INDEX IF NOT EXISTS idx_recurring_fees_company ON public.recurring_fees(company_id, is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_fees_next_payment ON public.recurring_fees(next_payment_date) WHERE is_active = true;
```

---

## ✅ VERIFICATION STEPS

After completing the manual setup:

1. **Test Database Connection**:
   ```bash
   node test-database-status.js
   ```

2. **Test Authentication**:
   ```bash
   node setup-test-auth.js
   ```

3. **Test Payment APIs**:
   ```bash
   node test-payment-apis-detailed.js
   ```

4. **Run Comprehensive Tests**:
   ```bash
   npx playwright test e2e/comprehensive-authenticated-test.spec.js
   ```

---

## 🎯 SUCCESS CRITERIA

✅ All tables created without errors  
✅ RLS policies working without infinite recursion  
✅ Test users can authenticate  
✅ Payment APIs return 200 with valid auth  
✅ Alliance components load properly  
✅ Business entity registration works  

---

## 🚨 PRIORITY ORDER

1. **CRITICAL**: Fix RLS policies (Step 1)
2. **CRITICAL**: Create companies table (Step 2.1)
3. **HIGH**: Add company fields to teams (Step 2.2)
4. **HIGH**: Confirm test user emails (Step 4)
5. **MEDIUM**: Create remaining tables (Steps 2.3-2.5)
6. **LOW**: Add indexes (Step 5)

Once Steps 1-4 are complete, the system will be functional for basic testing and VRC can begin using the alliance management features.
