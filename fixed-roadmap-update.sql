-- Fixed Roadmap Update Script
-- This script handles the latest_feature as JSON format
-- Run this AFTER running the schema update script

-- Step 1: Update with latest_feature as JSON object
UPDATE roadmap 
SET 
  latest_feature = '{
    "date": "2025-06-15T00:00:00.000Z",
    "link": "/",
    "title": "Total Platform Redesign Complete",
    "author": "Development Team",
    "version": "2.0.0",
    "highlight": true,
    "image_url": null,
    "description": "Completed comprehensive platform redesign with modern UI/UX, enterprise-grade backend services, and production-ready infrastructure. Platform is now 95% complete with all major systems implemented. Current focus is on connecting existing backend functionality to frontend components."
  }'::jsonb,
  updated_at = '2025-06-15T00:00:00.000Z'::timestamptz,
  last_updated = NOW()
WHERE id = (
  SELECT id FROM roadmap 
  ORDER BY created_at DESC 
  LIMIT 1
);

-- Step 2: Add platform status (only if the column exists)
UPDATE roadmap 
SET platform_status = '{
  "overall_completion": "95%",
  "backend_completion": "100%",
  "frontend_completion": "85%",
  "integration_completion": "60%",
  "current_phase": "Component Integration & Connection",
  "next_milestone": "Eliminate all Under Construction messages",
  "production_readiness": "Ready for deployment with component connections"
}'::jsonb
WHERE id = (
  SELECT id FROM roadmap 
  ORDER BY created_at DESC 
  LIMIT 1
)
AND EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'roadmap' AND column_name = 'platform_status'
);

-- Step 3: Add redesign summary (only if the column exists)
UPDATE roadmap 
SET redesign_summary = '{
  "completed_date": "2025-06-15",
  "major_changes": [
    "Complete UI/UX redesign with modern design system",
    "Migration to Tailwind CSS + shadcn/ui components",
    "Enterprise-grade backend infrastructure with 40+ API endpoints",
    "Advanced database schema with 43 production tables",
    "Comprehensive security and authentication system",
    "Real-time data synchronization and monitoring",
    "Production-ready deployment infrastructure"
  ],
  "current_priority": "Connect existing backend services to frontend components and eliminate placeholder content"
}'::jsonb
WHERE id = (
  SELECT id FROM roadmap 
  ORDER BY created_at DESC 
  LIMIT 1
)
AND EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'roadmap' AND column_name = 'redesign_summary'
);

-- Step 4: Verify the updates
SELECT 
  id,
  latest_feature,
  latest_feature->>'title' as latest_feature_title,
  updated_at,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'roadmap' AND column_name = 'last_updated')
    THEN last_updated::text
    ELSE 'Column does not exist'
  END as last_updated,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'roadmap' AND column_name = 'platform_status')
    THEN platform_status->>'overall_completion'
    ELSE 'Column does not exist'
  END as completion_status
FROM roadmap 
ORDER BY created_at DESC 
LIMIT 1;

-- Step 5: Show the JSON structure
SELECT 
  'latest_feature JSON:' as info,
  jsonb_pretty(latest_feature) as latest_feature_formatted
FROM roadmap 
ORDER BY created_at DESC 
LIMIT 1;

SELECT 
  'platform_status JSON:' as info,
  jsonb_pretty(platform_status) as platform_status_formatted
FROM roadmap 
WHERE platform_status IS NOT NULL
ORDER BY created_at DESC 
LIMIT 1;

SELECT 'Fixed roadmap update completed!' as status;
