/**
 * Test Unity Project Agreement Generation
 *
 * This script tests the agreement generation for a Unity-based 2D mobile game project.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements/unity-project');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Define test data for a Unity-based 2D mobile game
const unityProjectData = {
  name: "Pixel Puzzle Adventure",
  title: "Pixel Puzzle Adventure",
  description: "a 2D pixel art puzzle game for mobile devices where players solve increasingly challenging puzzles with simple touch controls and casual gameplay mechanics",
  project_type: "game",
  is_public: true,
  launch_date: new Date("2023-06-30").toISOString(),
  company_name: "City of Gamers Inc.",
  // Not explicitly setting engine to test the auto-detection
  // engine: "Unity",
  platforms: "Mobile (iOS, Android)",
  estimated_duration: 3, // 3 months
  // Not explicitly setting languages to test the auto-detection
  // languages: ["C#"]
};

console.log('🚀 Starting Unity Project Agreement Test');
console.log('📁 Output directory:', outputDir);

// Simulate the agreement generation
const currentUser = {
  id: "contributor-id",
  email: "<EMAIL>",
  user_metadata: { full_name: "Jane Smith" },
  address: "456 Oak St, Techville, CA 54321"
};

const options = {
  contributors: [
    {
      id: "owner-id",
      display_name: "Gynell Journigan",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "President",
      address: "1205 43rd Street, Suite B, Orlando, Florida 32839",
      state: "Florida",
      county: "Orange County",
      role: "Project Owner",
      status: "active"
    },
    {
      id: "contributor-id",
      display_name: "Jane Smith",
      email: "<EMAIL>",
      permission_level: "Contributor",
      title: "Mobile Developer",
      address: "456 Oak St, Techville, CA 54321",
      role: "Developer",
      status: "active"
    }
  ],
  currentUser: currentUser,
  royaltyModel: {
    model_type: "custom",
    model_schema: "cog",
    configuration: {
      tasks_weight: 40,
      hours_weight: 30,
      difficulty_weight: 30
    },
    is_pre_expense: true,
    contributor_percentage: 45,
    min_payout: 50000,
    max_payout: 500000
  },
  milestones: [
    {
      id: "milestone-1",
      title: "Core Mechanics",
      description: "Basic puzzle mechanics implemented",
      target_date: new Date("2023-04-30").toISOString(),
      deliverables: [
        "Basic puzzle mechanics implemented",
        "Touch controls working",
        "First 5 levels playable"
      ]
    },
    {
      id: "milestone-2",
      title: "Content Complete",
      description: "All levels and features implemented",
      target_date: new Date("2023-05-31").toISOString(),
      deliverables: [
        "All 50 levels implemented",
        "Tutorial system complete",
        "Progression system working"
      ]
    },
    {
      id: "milestone-3",
      title: "Release Ready",
      description: "Game ready for app store submission",
      target_date: new Date("2023-06-30").toISOString(),
      deliverables: [
        "Performance optimized for mobile",
        "App store assets prepared",
        "Final testing complete"
      ]
    }
  ],
  fullName: "Jane Smith",
  // Use a fixed date for testing to ensure consistent output
  agreementDate: new Date("2025-05-17")
};

try {
  // Generate the agreement
  console.log('  ✓ Generating agreement...');
  const unityAgreement = generator.generateAgreement(templateText, unityProjectData, options);

  // Save the generated agreement
  const outputPath = path.join(outputDir, 'unity-project-agreement.md');
  fs.writeFileSync(outputPath, unityAgreement, 'utf8');
  console.log(`  ✓ Agreement saved to: ${outputPath}`);

  // Check if Unity was correctly detected
  if (unityAgreement.includes('Engine: Unity')) {
    console.log('  ✓ Unity engine correctly detected');
  } else {
    console.log('  ❌ Unity engine not correctly detected');
  }

  if (unityAgreement.includes('Programming: C#')) {
    console.log('  ✓ C# programming language correctly detected');
  } else {
    console.log('  ❌ C# programming language not correctly detected');
  }

  console.log('\n✅ Unity project agreement test completed successfully!');
} catch (error) {
  console.error('\n❌ Error generating agreement:', error);
}
