# Legacy Migrations

This directory contains legacy migration scripts that were previously located in the root directory. These scripts are kept for historical reference but should not be used for new migrations.

## Migration Best Practices

1. **Use Supabase CLI for Migrations**: All new migrations should be created using the Supabase CLI and stored in the `supabase/migrations` directory.

2. **Follow Naming Conventions**: Migration files should be named with a timestamp prefix followed by a descriptive name, e.g., `20230101000000_add_users_table.sql`.

3. **Document Migrations**: Each migration should include comments explaining what it does and why it's needed.

4. **Test Migrations**: Always test migrations in a development environment before applying them to production.

5. **Use Transactions**: Wrap migrations in transactions to ensure they are atomic.

## Legacy Migration Files

The following legacy migration files are included in this directory:

- `apply-columns-migration.js`: Script to apply column migrations
- `apply-migration-direct.js`: Script to apply migrations directly
- `apply-migration-sql.js`: Script to apply SQL migrations
- `apply-project-activities-migration.js`: Script to apply project activities migrations
- `apply-retro-profile-migration.js`: <PERSON>ript to apply retrospective profile migrations
- `check-migration.js`: Script to check migration status
- `check-project-activities-table.js`: <PERSON>ript to check project activities table
- `cleanup-migrations.ps1`: PowerShell script to clean up migrations
- `combined_skills_migration.sql`: SQL script for combined skills migration
- `corrected_skills_migration.sql`: SQL script for corrected skills migration
- `delete-applied-migrations.ps1`: PowerShell script to delete applied migrations
- `extract-retro-profile-migration.js`: Script to extract retrospective profile migration
- `final_seed_skills_data.sql`: SQL script for final seed skills data
- `final_skills_migration.sql`: SQL script for final skills migration
- `fixed_seed_skills_data.sql`: SQL script for fixed seed skills data
- `fixed_skills_migration.sql`: SQL script for fixed skills migration
- `manual-migration.sql`: SQL script for manual migration
- `MANUAL_MIGRATION_GUIDE.md`: Guide for manual migrations
- `MIGRATION_INSTRUCTIONS.md`: Instructions for migrations
- `retro-profile-migration.sql`: SQL script for retrospective profile migration
- `seed_skills_data.sql`: SQL script for seed skills data
- `supabase-migration-instructions.md`: Instructions for Supabase migrations

## Current Migration Process

The current migration process uses the Supabase CLI to create and apply migrations. All new migrations should be created using the Supabase CLI and stored in the `supabase/migrations` directory.

For more information on the current migration process, see the `supabase-migration-instructions.md` file in this directory.
