// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * Royaltea Platform - Production Testing Configuration
 * 
 * Configuration for testing against the production environment
 * without starting a local web server.
 */

module.exports = defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['list'] // Console output for debugging
  ],
  /* Shared settings for all the projects below. */
  use: {
    /* Base URL - Production URL */
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'https://royalty.technology',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    /* Record video on failure */
    video: 'retain-on-failure',
    /* Global timeout for each action - increased for production */
    actionTimeout: 20000,
    /* Global timeout for navigation - increased for production */
    navigationTimeout: 60000,
    /* Extra HTTP headers */
    extraHTTPHeaders: {
      'Accept': 'application/json',
    },
    /* Ignore HTTPS errors */
    ignoreHTTPSErrors: true,
  },

  /* Configure projects for major browsers */
  projects: [
    // Setup project for authentication
    {
      name: 'setup',
      testMatch: /.*\.setup\.js/,
      teardown: 'cleanup',
    },
    {
      name: 'cleanup',
      testMatch: /.*\.teardown\.js/,
    },

    // Main testing projects
    {
      name: 'chromium-auth',
      use: { 
        ...devices['Desktop Chrome'],
        // Use authenticated state
        storageState: 'test-results/auth-state.json',
      },
      dependencies: ['setup'],
    },

    {
      name: 'firefox-auth',
      use: { 
        ...devices['Desktop Firefox'],
        storageState: 'test-results/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Mobile testing with auth
    {
      name: 'mobile-chrome-auth',
      use: { 
        ...devices['Pixel 5'],
        storageState: 'test-results/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Admin testing project
    {
      name: 'admin-tests',
      use: { 
        ...devices['Desktop Chrome'],
        storageState: 'test-results/admin-auth-state.json',
      },
      dependencies: ['setup'],
      testMatch: /.*admin.*\.spec\.js/,
    },
  ],

  /* NO WEB SERVER - Testing against production */
  // webServer: undefined,

  /* Global setup and teardown for authentication */
  globalSetup: require.resolve('./tests/auth-global-setup.js'),
  globalTeardown: require.resolve('./tests/auth-global-teardown.js'),

  /* Test timeout - increased for production */
  timeout: 120 * 1000, // 2 minutes

  /* Expect timeout - increased for production */
  expect: {
    timeout: 15 * 1000, // 15 seconds
  },

  /* Output directory for test artifacts */
  outputDir: 'test-results/production-artifacts/',

  /* Test match patterns */
  testMatch: [
    '**/comprehensive-grid-navigation.spec.js',
    '**/final-grid-test.spec.js',
    '**/content-verification.spec.js',
    '**/realistic-content-verification.spec.js',
    '**/implementation-status-audit.spec.js',
    '**/auth-persistence-test.spec.js',
    '**/debug-grid-inspection.spec.js',
    '**/bento-grid-navigation.spec.js',
    '**/auth-*.spec.js',
    '**/authenticated-*.spec.js'
  ],

  /* Test ignore patterns */
  testIgnore: [
    '**/node_modules/**',
    '**/legacy/**',
    '**/archive/**'
  ],
});
