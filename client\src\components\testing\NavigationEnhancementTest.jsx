import React, { useState, useEffect } from 'react';
import { Card, CardBody, Button, Badge, Divider } from '@heroui/react';
import { motion } from 'framer-motion';
import inputManager, { INPUT_MODES } from '../../utils/input-manager';
import { useNavigation } from '../../contexts/NavigationContext';

/**
 * Navigation Enhancement Test Suite
 * 
 * Tests the unified input manager and enhanced navigation system
 */
const NavigationEnhancementTest = () => {
  const [testResults, setTestResults] = useState({});
  const [currentTest, setCurrentTest] = useState(null);
  const [inputEvents, setInputEvents] = useState([]);
  const { inputMode, allowScroll, actions } = useNavigation();

  // Test scenarios
  const tests = [
    {
      id: 'input-mode-switching',
      name: 'Input Mode Switching',
      description: 'Test switching between navigation, content, and editing modes',
      steps: [
        'Click "Set Navigation Mode" button',
        'Click "Set Content Mode" button', 
        'Click "Set Editing Mode" button',
        'Verify mode changes are reflected'
      ]
    },
    {
      id: 'keyboard-navigation',
      name: 'Keyboard Navigation',
      description: 'Test keyboard shortcuts and navigation',
      steps: [
        'Press Arrow keys (should trigger navigation events)',
        'Press Escape key (should exit content mode)',
        'Press Tab key (should toggle view mode)',
        'Type in input field (should switch to editing mode)'
      ]
    },
    {
      id: 'scroll-behavior',
      name: 'Scroll Behavior',
      description: 'Test scrolling in different modes',
      steps: [
        'Switch to content mode',
        'Scroll in the content area below',
        'Switch to navigation mode',
        'Try scrolling (should be prevented)',
        'Verify scroll state changes'
      ]
    },
    {
      id: 'touch-gestures',
      name: 'Touch Gestures',
      description: 'Test touch handling and gesture recognition',
      steps: [
        'On mobile: Try swiping gestures',
        'Touch and drag in navigation mode',
        'Touch and scroll in content mode',
        'Verify gestures are handled correctly'
      ]
    }
  ];

  // Listen for input manager events
  useEffect(() => {
    const handleInputEvent = (eventType, data) => {
      const timestamp = new Date().toLocaleTimeString();
      setInputEvents(prev => [...prev.slice(-9), { 
        timestamp, 
        eventType, 
        data: JSON.stringify(data, null, 2) 
      }]);
    };

    // Register event listeners
    const events = ['modeChange', 'navigate', 'escape', 'toggle', 'reset', 'touchStart', 'touchDrag', 'dragEnd', 'scroll'];
    events.forEach(event => {
      inputManager.on(event, (data) => handleInputEvent(event, data));
    });

    return () => {
      events.forEach(event => {
        inputManager.off(event, (data) => handleInputEvent(event, data));
      });
    };
  }, []);

  const runTest = (testId) => {
    setCurrentTest(testId);
    setTestResults(prev => ({
      ...prev,
      [testId]: { status: 'running', startTime: Date.now() }
    }));
  };

  const completeTest = (testId, success = true) => {
    setTestResults(prev => ({
      ...prev,
      [testId]: { 
        status: success ? 'passed' : 'failed', 
        endTime: Date.now(),
        duration: Date.now() - (prev[testId]?.startTime || Date.now())
      }
    }));
    setCurrentTest(null);
  };

  const clearEvents = () => {
    setInputEvents([]);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Navigation Enhancement Test Suite</h1>
        <p className="text-white/70">Test the unified input manager and enhanced navigation system</p>
      </div>

      {/* Current Status */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Current Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">Input Mode</h3>
              <Badge color={inputMode === INPUT_MODES.NAVIGATION ? 'primary' : inputMode === INPUT_MODES.CONTENT ? 'success' : 'warning'}>
                {inputMode}
              </Badge>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">Scroll Allowed</h3>
              <Badge color={allowScroll ? 'success' : 'danger'}>
                {allowScroll ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">Active Test</h3>
              <Badge color={currentTest ? 'warning' : 'default'}>
                {currentTest || 'None'}
              </Badge>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Test Controls */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Test Controls</h2>
          <div className="flex flex-wrap gap-3 mb-4">
            <Button 
              color="primary" 
              onClick={() => actions.setInputMode(INPUT_MODES.NAVIGATION)}
            >
              Set Navigation Mode
            </Button>
            <Button 
              color="success" 
              onClick={() => actions.setInputMode(INPUT_MODES.CONTENT)}
            >
              Set Content Mode
            </Button>
            <Button 
              color="warning" 
              onClick={() => actions.setInputMode(INPUT_MODES.EDITING)}
            >
              Set Editing Mode
            </Button>
            <Button 
              color="secondary" 
              onClick={() => actions.setScrollState({ allowScroll: !allowScroll })}
            >
              Toggle Scroll
            </Button>
          </div>
          
          <div className="mb-4">
            <input 
              type="text" 
              placeholder="Type here to test editing mode detection..."
              className="w-full p-3 rounded-lg bg-white/10 text-white placeholder-white/50 border border-white/20"
            />
          </div>
        </CardBody>
      </Card>

      {/* Test Scenarios */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Test Scenarios</h2>
            <div className="space-y-4">
              {tests.map(test => (
                <motion.div
                  key={test.id}
                  className="bg-white/5 rounded-lg p-4"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-white font-medium">{test.name}</h3>
                    <div className="flex items-center gap-2">
                      {testResults[test.id] && (
                        <Badge 
                          color={testResults[test.id].status === 'passed' ? 'success' : 
                                testResults[test.id].status === 'failed' ? 'danger' : 'warning'}
                        >
                          {testResults[test.id].status}
                        </Badge>
                      )}
                      <Button
                        size="sm"
                        color="primary"
                        onClick={() => runTest(test.id)}
                        disabled={currentTest === test.id}
                      >
                        {currentTest === test.id ? 'Running...' : 'Run Test'}
                      </Button>
                    </div>
                  </div>
                  <p className="text-white/70 text-sm mb-3">{test.description}</p>
                  <div className="text-xs text-white/60">
                    <strong>Steps:</strong>
                    <ol className="list-decimal list-inside mt-1 space-y-1">
                      {test.steps.map((step, index) => (
                        <li key={index}>{step}</li>
                      ))}
                    </ol>
                  </div>
                  {currentTest === test.id && (
                    <div className="mt-3 flex gap-2">
                      <Button size="sm" color="success" onClick={() => completeTest(test.id, true)}>
                        Pass
                      </Button>
                      <Button size="sm" color="danger" onClick={() => completeTest(test.id, false)}>
                        Fail
                      </Button>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Event Log */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">Input Events Log</h2>
              <Button size="sm" color="secondary" onClick={clearEvents}>
                Clear
              </Button>
            </div>
            <div className="bg-black/30 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
              {inputEvents.length === 0 ? (
                <p className="text-white/50">No events logged yet. Interact with the page to see events.</p>
              ) : (
                inputEvents.map((event, index) => (
                  <div key={index} className="mb-2 pb-2 border-b border-white/10 last:border-b-0">
                    <div className="text-green-400 font-semibold">
                      [{event.timestamp}] {event.eventType}
                    </div>
                    <pre className="text-white/70 text-xs mt-1 whitespace-pre-wrap">
                      {event.data}
                    </pre>
                  </div>
                ))
              )}
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Scrollable Content Test Area */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Scrollable Content Test Area</h2>
          <p className="text-white/70 mb-4">
            This area should be scrollable when in content mode and scroll is allowed.
          </p>
          <div className="bg-white/5 rounded-lg p-4 h-64 overflow-y-auto content-scrollable">
            {Array.from({ length: 50 }, (_, i) => (
              <div key={i} className="py-2 border-b border-white/10 text-white/80">
                Scrollable content item {i + 1} - This content should scroll when in content mode.
              </div>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default NavigationEnhancementTest;
