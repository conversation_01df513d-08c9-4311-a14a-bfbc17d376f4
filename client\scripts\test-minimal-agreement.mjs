/**
 * Test Minimal Agreement Generator Script
 *
 * This script tests the agreement generation functionality with minimal inputs
 * to verify that default values and fallbacks are properly applied.
 *
 * Usage: node test-minimal-agreement.mjs
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements/minimal');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Test cases for minimal input scenarios
const testCases = [
  // Test Case 1: Absolute Minimal Project (only name and type)
  {
    name: "absolute-minimal",
    title: "Absolute Minimal Project",
    project: {
      name: "Minimal Project",
      project_type: "game"
    },
    options: {
      currentUser: {
        email: "<EMAIL>"
      }
    }
  },

  // Test Case 2: Minimal Game Project
  {
    name: "minimal-game",
    title: "Minimal Game Project",
    project: {
      name: "Minimal Game",
      project_type: "game",
      description: "A simple game project"
    },
    options: {
      currentUser: {
        email: "<EMAIL>",
        user_metadata: { full_name: "Game Developer" }
      }
    }
  },

  // Test Case 3: Minimal Music Project
  {
    name: "minimal-music",
    title: "Minimal Music Project",
    project: {
      name: "Minimal Music",
      project_type: "music",
      description: "A simple music project"
    },
    options: {
      currentUser: {
        email: "<EMAIL>"
      }
    }
  },

  // Test Case 4: Minimal Software Project
  {
    name: "minimal-software",
    title: "Minimal Software Project",
    project: {
      name: "Minimal Software",
      project_type: "software",
      description: "A simple software project"
    },
    options: {
      currentUser: {
        email: "<EMAIL>"
      }
    }
  },

  // Test Case 5: Minimal Project with Contributors but No Owner
  {
    name: "minimal-with-contributors",
    title: "Minimal Project with Contributors",
    project: {
      name: "Team Project",
      project_type: "game",
      description: "A project with contributors but no owner",
      contributors: [
        {
          id: "contributor-1",
          display_name: "Contributor 1",
          email: "<EMAIL>",
          permission_level: "Contributor"
        },
        {
          id: "contributor-2",
          display_name: "Contributor 2",
          email: "<EMAIL>",
          permission_level: "Contributor"
        }
      ]
    },
    options: {
      contributors: [
        {
          id: "contributor-1",
          display_name: "Contributor 1",
          email: "<EMAIL>",
          permission_level: "Contributor"
        },
        {
          id: "contributor-2",
          display_name: "Contributor 2",
          email: "<EMAIL>",
          permission_level: "Contributor"
        }
      ],
      currentUser: {
        email: "<EMAIL>",
        user_metadata: { full_name: "Contributor 1" }
      }
    }
  },

  // Test Case 6: Minimal Project with Owner
  {
    name: "minimal-with-owner",
    title: "Minimal Project with Owner",
    project: {
      name: "Owned Project",
      project_type: "game",
      description: "A project with an owner",
      contributors: [
        {
          id: "owner-id",
          display_name: "Project Owner",
          email: "<EMAIL>",
          permission_level: "Owner"
        }
      ]
    },
    options: {
      contributors: [
        {
          id: "owner-id",
          display_name: "Project Owner",
          email: "<EMAIL>",
          permission_level: "Owner"
        }
      ],
      currentUser: {
        email: "<EMAIL>",
        user_metadata: { full_name: "New Contributor" }
      }
    }
  }
];

// Run the tests
console.log('🚀 Running minimal agreement generation tests...');
console.log(`📁 Output directory: ${outputDir}`);

try {
  // Generate agreements for all test cases
  for (const testCase of testCases) {
    console.log(`Generating agreement for: ${testCase.title}`);
    
    // Generate the agreement
    const agreement = generator.generateAgreement(templateText, testCase.project, testCase.options);
    
    // Save the agreement to a file
    const outputPath = path.join(outputDir, `${testCase.name}-agreement.md`);
    fs.writeFileSync(outputPath, agreement, 'utf8');
    
    console.log(`✅ Agreement saved to: ${outputPath}`);
  }
  
  console.log('✨ All minimal tests completed successfully!');
} catch (error) {
  console.error('❌ Error running tests:', error);
}
