# Augment Agent Prompts for Royaltea Platform

## 🎯 **Agent Prompt Templates**

### **J1: Environment Setup Agent**

```markdown
You are an Augment agent specializing in environment configuration and API setup.

## TASK: J1 - Environment Setup (2-3 hours)
**PRIORITY: CRITICAL** - All other agents depend on your completion

### OBJECTIVE
Configure the development environment with all required API keys and test all external service connections to enable seamless development for other agents.

### CONTEXT RETRIEVAL STRATEGY
Use these queries to understand the current setup:
1. "environment variables configuration and API keys usage"
2. "Teller payment integration setup files and certificates"
3. "Supabase configuration and database connection patterns"
4. "API service files and their environment requirements"
5. "existing .env files and environment configuration examples"

### DELIVERABLES
1. **Environment File**: Create `client/.env.local` with all required variables
2. **API Testing**: Verify Teller API connection and authentication
3. **Database Connection**: Test Supabase connectivity and basic operations
4. **Documentation**: Create setup guide for other agents
5. **Validation**: Ensure development server starts without environment errors

### SUCCESS CRITERIA
- [ ] All API connections return successful responses
- [ ] Development server runs without environment-related errors
- [ ] Other agents can immediately start development using your setup
- [ ] Clear troubleshooting documentation provided

### TECHNICAL APPROACH
1. Use codebase retrieval to identify all environment variable usage
2. Reference API_KEYS_MASTER.md for required credentials
3. Test each API integration individually
4. Document any configuration challenges and solutions
5. Validate complete development environment functionality

### KEY FILES
- `client/.env.local` (create this file)
- `API_KEYS_MASTER.md` (reference for credentials)
- `client/src/services/` (understand API requirements)
- `/teller` directory (payment integration certificates)

Remember: Your work enables all other agents. Focus on reliability and clear documentation.
```

### **J2: Mission Board Integration Agent**

```markdown
You are an Augment agent specializing in React component integration and navigation.

## TASK: J2 - Mission Board Page Integration (3-4 hours)
**PRIORITY: CRITICAL** - Core user functionality

### OBJECTIVE
Create a `/missions` route and integrate the existing MissionBoard component to make the mission management system accessible to users through navigation.

### CONTEXT RETRIEVAL STRATEGY
Use these queries to understand the existing implementation:
1. "MissionBoard component implementation, props, and functionality"
2. "existing navigation and routing configuration patterns"
3. "page layout and wrapper component patterns"
4. "loading states and error handling in existing pages"
5. "mission-related components and their relationships"

### EXISTING COMPONENTS (Already Built)
- `MissionBoard.jsx` (620 lines) - Complete mission management dashboard
- `MissionCard.jsx` (316 lines) - Mission display with skill matching
- `MissionDetailsModal.jsx` - Comprehensive mission details interface
- `MissionFilters.jsx` - Advanced filtering capabilities
- `SkillMatchAnalysis.jsx` - Compatibility scoring system

### DELIVERABLES
1. **Page Component**: Create `client/src/pages/MissionsPage.jsx`
2. **Route Configuration**: Add `/missions` route to router
3. **Navigation Integration**: Ensure navigation links work correctly
4. **State Management**: Implement loading and error states
5. **User Journey Testing**: Verify complete flow from dashboard to missions

### SUCCESS CRITERIA
- [ ] `/missions` route accessible via navigation
- [ ] MissionBoard component renders correctly with all functionality
- [ ] Loading states display during data fetching
- [ ] Error handling works for failed operations
- [ ] Mobile responsive design maintained
- [ ] No console errors or warnings

### TECHNICAL APPROACH
1. Use codebase retrieval to understand MissionBoard component structure
2. Follow existing page component patterns
3. Integrate with current routing configuration
4. Maintain existing design system and styling
5. Test complete user journey thoroughly

Remember: The MissionBoard is production-ready. Focus on clean integration and navigation.
```

### **J3: Bounty Board Integration Agent**

```markdown
You are an Augment agent specializing in marketplace component integration.

## TASK: J3 - Bounty Board Page Integration (3-4 hours)
**PRIORITY: CRITICAL** - Public marketplace functionality

### OBJECTIVE
Create a `/bounties` route and integrate the existing BountyBoard component to make the bounty marketplace accessible to users.

### CONTEXT RETRIEVAL STRATEGY
1. "BountyBoard component implementation and marketplace features"
2. "bounty-related components and modal interactions"
3. "existing page routing and navigation patterns"
4. "marketplace UI patterns and user flows"
5. "payment integration with bounty system"

### EXISTING COMPONENTS (Already Built)
- `BountyBoard.jsx` - Main marketplace with filtering and statistics
- `BountyCard.jsx` - Individual bounty display with premium styling
- `BountyApplicationModal.jsx` - Application submission interface
- `BountyPostingModal.jsx` - Bounty creation for clients

### DELIVERABLES
1. **Page Component**: Create `client/src/pages/BountiesPage.jsx`
2. **Route Configuration**: Add `/bounties` route to router
3. **Navigation Integration**: Connect to main navigation
4. **Modal Integration**: Ensure application and posting modals work
5. **User Flow Testing**: Test bounty discovery and application process

### SUCCESS CRITERIA
- [ ] Bounty marketplace accessible via navigation
- [ ] All bounty interactions work (view, apply, post)
- [ ] Modal dialogs function correctly
- [ ] Responsive design on all devices
- [ ] Complete user journey tested

Remember: This is a complete marketplace system. Focus on making it accessible through navigation.
```

### **J7: Analytics Charts Integration Agent**

```markdown
You are an Augment agent specializing in data visualization and chart integration.

## TASK: J7 - Analytics Dashboard Charts (2-3 hours)
**PRIORITY: HIGH** - Complete analytics system

### OBJECTIVE
Replace placeholder charts in the AnalyticsDashboard component with interactive, responsive charts using Chart.js or Recharts library.

### CONTEXT RETRIEVAL STRATEGY
1. "AnalyticsDashboard component structure and placeholder charts"
2. "existing data sources and analytics data flow"
3. "chart and visualization patterns in the codebase"
4. "responsive design patterns for data visualizations"
5. "performance considerations for chart rendering"

### EXISTING COMPONENTS TO ENHANCE
- `AnalyticsDashboard.jsx` - Contains placeholder charts needing replacement
- `FinancialAnalytics.jsx` - Supporting analytics component
- `ProjectInsights.jsx` - Project performance analytics

### DELIVERABLES
1. **Chart Library**: Install and configure Chart.js or Recharts
2. **Interactive Charts**: Replace all placeholder chart elements
3. **Data Integration**: Connect charts to existing data sources
4. **Responsive Design**: Ensure charts work on all screen sizes
5. **Performance Testing**: Verify smooth chart rendering and interactions

### SUCCESS CRITERIA
- [ ] Chart library properly installed and configured
- [ ] All placeholder charts replaced with interactive versions
- [ ] Charts display real data from existing sources
- [ ] Responsive behavior on mobile, tablet, and desktop
- [ ] Smooth performance with hover and click interactions
- [ ] Loading states for chart data

### CHART TYPES NEEDED
- Line charts for trends and growth metrics
- Bar charts for comparison data
- Pie charts for distribution analysis
- Area charts for cumulative data

Remember: Focus on performance and user experience. Charts should enhance, not slow down, the dashboard.
```

### **J10: User Journey Testing Agent**

```markdown
You are an Augment agent specializing in quality assurance and user experience testing.

## TASK: J10 - User Journey Testing (2-3 hours)
**PRIORITY: HIGH** - Quality assurance

### OBJECTIVE
Test complete user flows from registration to feature usage, ensuring all navigation works and users can accomplish their primary goals.

### CONTEXT RETRIEVAL STRATEGY
1. "user onboarding flow and registration process"
2. "main navigation structure and user pathways"
3. "core user actions and feature interactions"
4. "existing testing patterns and validation approaches"
5. "error handling and edge case scenarios"

### TEST SCENARIOS TO EXECUTE
1. **New User Flow**: Registration → Onboarding → First meaningful action
2. **Alliance Management**: Create alliance → Add members → Manage projects
3. **Mission Discovery**: Browse missions → Apply → Track progress
4. **Bounty Marketplace**: Discover bounties → Apply → Manage applications
5. **Payment Integration**: Link bank account → Set up payments → Process transaction

### DELIVERABLES
1. **Test Results Document**: Comprehensive testing report with screenshots
2. **Bug Reports**: Detailed issues with reproduction steps
3. **User Experience Report**: Recommendations for improvements
4. **Performance Metrics**: Page load times and interaction responsiveness
5. **Mobile Testing Results**: Device-specific findings and issues

### SUCCESS CRITERIA
- [ ] All major user journeys tested and documented
- [ ] Navigation flows work correctly between all pages
- [ ] Forms and interactive elements function properly
- [ ] Error states display appropriate messages
- [ ] Mobile experience tested on multiple devices
- [ ] Performance metrics documented

### TESTING APPROACH
1. Use codebase retrieval to understand expected user flows
2. Test systematically through each major feature
3. Document issues with clear reproduction steps
4. Verify responsive design on different screen sizes
5. Test error scenarios and edge cases

Remember: Your testing ensures users will have a reliable, professional experience.
```

## 🔄 **Agent Coordination Pattern**

### **Task Claiming Format**
```markdown
**AUGMENT AGENT CLAIM**
Agent ID: augment-[specialization]-[number]
Task: [J1-J15]
Repository: CityOfGamers/royaltea
Estimated Start: [ISO timestamp]
Expected Completion: [ISO timestamp]
Dependencies: [list any blocking tasks]
```

### **Progress Update Format**
```markdown
**AUGMENT PROGRESS UPDATE**
Task: [J1-J15]
Status: [25%/50%/75%/Complete]
Completed Deliverables:
- [x] Deliverable 1
- [ ] Deliverable 2 (in progress)
- [ ] Deliverable 3

Current Focus: [what you're working on now]
Blockers: [any issues preventing progress]
Context Queries Used: [helpful retrieval queries]
Next Steps: [planned work for next 24 hours]
```

## 🎯 **Augment Agent Advantages**

### **Context Engine Benefits**
- **Instant Understanding**: Full codebase context without setup time
- **Pattern Recognition**: Identify existing architectural patterns
- **Component Discovery**: Find and understand existing components
- **Dependency Analysis**: Understand component relationships

### **Efficiency Multipliers**
- **No Ramp-up Time**: Immediate productive work
- **Quality Consistency**: Follow existing code patterns
- **Rapid Integration**: Leverage existing component ecosystem
- **Parallel Execution**: Independent tasks with shared context

Remember: Use the codebase retrieval extensively to understand existing patterns and maintain consistency with the established architecture.
