<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Route Test</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #0f172a;
    }
    .test-button {
      display: inline-block;
      background-color: #3b82f6;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      text-decoration: none;
      margin: 5px;
    }
    .test-button:hover {
      background-color: #2563eb;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8fafc;
      border-radius: 4px;
      border: 1px solid #e2e8f0;
    }
  </style>
</head>
<body>
  <h1>Netlify Routing Test</h1>
  <p>This page tests if Netlify routing is working correctly for SPAs.</p>
  
  <div>
    <a href="/" class="test-button">Go to Home</a>
    <a href="/profile" class="test-button">Go to Profile</a>
    <a href="/non-existent-page" class="test-button">Go to Non-existent Page</a>
  </div>
  
  <div class="result">
    <h2>Current URL:</h2>
    <p id="current-url"></p>
    
    <h2>History State:</h2>
    <pre id="history-state"></pre>
  </div>
  
  <script>
    // Display the current URL
    document.getElementById('current-url').textContent = window.location.href;
    
    // Display the history state
    document.getElementById('history-state').textContent = JSON.stringify({
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash
    }, null, 2);
  </script>
</body>
</html>
