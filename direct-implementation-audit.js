#!/usr/bin/env node

/**
 * Direct Implementation Status Audit
 * 
 * Uses the working authentication approach to audit all pages
 * for placeholder vs. implemented content
 */

const { chromium } = require('playwright');

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// All routes to audit
const ROUTES_TO_AUDIT = [
  { route: '/', name: 'Dashboard' },
  { route: '/start', name: 'Start' },
  { route: '/track', name: 'Track' },
  { route: '/earn', name: 'Earn' },
  { route: '/projects', name: 'Projects/Ventures' },
  { route: '/project/wizard', name: 'Project Wizard' },
  { route: '/missions', name: 'Mission Board' },
  { route: '/validation/metrics', name: 'Validation' },
  { route: '/revenue', name: 'Revenue' },
  { route: '/analytics/contributions', name: 'Analytics' },
  { route: '/analytics/insights', name: 'AI Insights' },
  { route: '/profile', name: 'Profile' },
  { route: '/teams', name: 'Teams/Alliances' },
  { route: '/social', name: 'Social' },
  { route: '/settings', name: 'Settings' },
  { route: '/notifications', name: 'Notifications' },
  { route: '/bugs', name: 'Bug Reports' },
  { route: '/learn', name: 'Learning' },
  { route: '/help', name: 'Help Center' }
];

// Patterns that indicate placeholder/construction content
const PLACEHOLDER_PATTERNS = [
  /under construction/i,
  /section under construction/i,
  /being built/i,
  /development info/i,
  /canvas:/i,
  /section:/i,
  /component:/i,
  /placeholder/i,
  /coming soon/i,
  /not implemented/i,
  /todo/i,
  /work in progress/i,
  /🚧/,
  /⚠️.*construction/i,
  /this section is being/i,
  /feature coming soon/i
];

async function auditImplementationStatus() {
  console.log('🔍 DIRECT IMPLEMENTATION STATUS AUDIT');
  console.log('='.repeat(60));
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Initial authentication
    console.log('🔐 Authenticating...');
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const needsAuth = await page.locator('input[type="email"]').isVisible();
    if (needsAuth) {
      await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
      await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      console.log('✅ Authentication successful');
    }
    
    const auditResults = [];
    
    // Audit each route
    for (const { route, name } of ROUTES_TO_AUDIT) {
      console.log(`\n📍 Auditing: ${name} (${route})`);
      
      // Navigate to the page
      await page.goto(`${PRODUCTION_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Get page content
      const bodyText = await page.textContent('body');
      const title = await page.title();
      const contentLength = bodyText?.length || 0;
      
      // Check for authentication issues
      const isLoginPage = bodyText?.includes('Welcome Back') && bodyText?.includes('Sign in to continue');
      
      if (isLoginPage) {
        console.log(`   🔐 AUTHENTICATION ISSUE - Showing login page`);
        auditResults.push({
          route,
          name,
          status: 'AUTH_ISSUE',
          contentLength,
          contentPreview: bodyText?.substring(0, 200)
        });
        continue;
      }
      
      // Check for placeholder patterns
      const placeholderMatches = [];
      for (const pattern of PLACEHOLDER_PATTERNS) {
        if (bodyText && pattern.test(bodyText)) {
          const match = bodyText.match(pattern);
          if (match) {
            placeholderMatches.push(match[0]);
          }
        }
      }
      
      const hasPlaceholder = placeholderMatches.length > 0;
      
      // Determine status based on content analysis
      let status;
      let statusIcon;
      
      if (hasPlaceholder) {
        status = 'PLACEHOLDER_CONTENT';
        statusIcon = '🚧';
      } else if (contentLength > 1000) {
        status = 'IMPLEMENTED';
        statusIcon = '✅';
      } else if (contentLength > 500) {
        status = 'PARTIAL_IMPLEMENTATION';
        statusIcon = '⚠️';
      } else {
        status = 'MINIMAL_CONTENT';
        statusIcon = '❓';
      }
      
      console.log(`   ${statusIcon} Status: ${status}`);
      console.log(`   📏 Content Length: ${contentLength}`);
      
      if (hasPlaceholder) {
        console.log(`   🚧 Placeholder Content Found:`);
        placeholderMatches.forEach(match => console.log(`     - "${match}"`));
      }
      
      // Show content preview for analysis
      const preview = bodyText?.substring(0, 400).replace(/\s+/g, ' ').trim();
      console.log(`   📄 Content Preview: ${preview}...`);
      
      auditResults.push({
        route,
        name,
        status,
        contentLength,
        hasPlaceholder,
        placeholderMatches,
        contentPreview: preview
      });
    }
    
    // Generate comprehensive summary
    console.log('\n📊 IMPLEMENTATION STATUS SUMMARY');
    console.log('='.repeat(60));
    
    const totalPages = auditResults.length;
    const placeholderContent = auditResults.filter(r => r.status === 'PLACEHOLDER_CONTENT');
    const partialImplementation = auditResults.filter(r => r.status === 'PARTIAL_IMPLEMENTATION');
    const implemented = auditResults.filter(r => r.status === 'IMPLEMENTED');
    const minimalContent = auditResults.filter(r => r.status === 'MINIMAL_CONTENT');
    const authIssues = auditResults.filter(r => r.status === 'AUTH_ISSUE');
    
    console.log(`📊 Total Pages Audited: ${totalPages}`);
    console.log(`🚧 Placeholder Content: ${placeholderContent.length}`);
    console.log(`⚠️  Partial Implementation: ${partialImplementation.length}`);
    console.log(`✅ Fully Implemented: ${implemented.length}`);
    console.log(`❓ Minimal Content: ${minimalContent.length}`);
    console.log(`🔐 Auth Issues: ${authIssues.length}`);
    
    console.log('\n🚧 PAGES WITH PLACEHOLDER CONTENT (NEED IMPLEMENTATION):');
    placeholderContent.forEach(page => {
      console.log(`   - ${page.name} (${page.route})`);
      if (page.placeholderMatches && page.placeholderMatches.length > 0) {
        page.placeholderMatches.forEach(match => {
          console.log(`     * "${match}"`);
        });
      }
    });
    
    console.log('\n⚠️  PAGES WITH PARTIAL IMPLEMENTATION:');
    partialImplementation.forEach(page => {
      console.log(`   - ${page.name} (${page.route}) - ${page.contentLength} chars`);
    });
    
    console.log('\n❓ PAGES WITH MINIMAL CONTENT (NEED REVIEW):');
    minimalContent.forEach(page => {
      console.log(`   - ${page.name} (${page.route}) - ${page.contentLength} chars`);
    });
    
    console.log('\n✅ FULLY IMPLEMENTED PAGES:');
    implemented.forEach(page => {
      console.log(`   - ${page.name} (${page.route}) - ${page.contentLength} chars`);
    });
    
    if (authIssues.length > 0) {
      console.log('\n🔐 PAGES WITH AUTHENTICATION ISSUES:');
      authIssues.forEach(page => {
        console.log(`   - ${page.name} (${page.route})`);
      });
    }
    
    // Calculate metrics
    const implementationRate = ((implemented.length / totalPages) * 100).toFixed(1);
    const placeholderRate = ((placeholderContent.length / totalPages) * 100).toFixed(1);
    const needsWorkCount = placeholderContent.length + partialImplementation.length + minimalContent.length;
    
    console.log('\n📈 IMPLEMENTATION METRICS:');
    console.log(`   Fully Implemented: ${implementationRate}%`);
    console.log(`   Has Placeholder Content: ${placeholderRate}%`);
    console.log(`   Pages Needing Work: ${needsWorkCount}/${totalPages}`);
    
    // Priority recommendations
    console.log('\n🎯 PRIORITY RECOMMENDATIONS:');
    console.log('1. HIGH PRIORITY - Remove placeholder content from:');
    placeholderContent.forEach(page => {
      console.log(`   • ${page.name} (${page.route})`);
    });
    
    console.log('\n2. MEDIUM PRIORITY - Enhance partial implementations:');
    partialImplementation.forEach(page => {
      console.log(`   • ${page.name} (${page.route})`);
    });
    
    console.log('\n3. LOW PRIORITY - Review minimal content pages:');
    minimalContent.forEach(page => {
      console.log(`   • ${page.name} (${page.route})`);
    });
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalPages,
        placeholderContent: placeholderContent.length,
        partialImplementation: partialImplementation.length,
        implemented: implemented.length,
        minimalContent: minimalContent.length,
        authIssues: authIssues.length,
        implementationRate: parseFloat(implementationRate),
        placeholderRate: parseFloat(placeholderRate),
        needsWorkCount
      },
      pages: auditResults,
      priorities: {
        highPriority: placeholderContent.map(p => ({ name: p.name, route: p.route, placeholders: p.placeholderMatches })),
        mediumPriority: partialImplementation.map(p => ({ name: p.name, route: p.route, contentLength: p.contentLength })),
        lowPriority: minimalContent.map(p => ({ name: p.name, route: p.route, contentLength: p.contentLength }))
      }
    };
    
    const fs = require('fs');
    const path = require('path');
    
    const reportsDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const reportPath = path.join(reportsDir, `direct-implementation-audit-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 Detailed audit report saved: ${reportPath}`);
    console.log('\n🎉 Implementation status audit completed!');
    
  } catch (error) {
    console.error('❌ Error during audit:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the audit
auditImplementationStatus().catch(error => {
  console.error('❌ Failed to run audit:', error);
  process.exit(1);
});
