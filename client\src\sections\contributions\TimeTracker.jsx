import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Select, SelectItem, Input, Textarea, Chip } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Play, Pause, Square, Clock, Save, Calendar } from 'lucide-react';

/**
 * Time Tracker Section
 * 
 * Provides time tracking functionality including:
 * - Start/stop timer for active work
 * - Manual time entry
 * - Project and task selection
 * - Time logging and submission
 */
const TimeTracker = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [isTracking, setIsTracking] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState('');
  const [timeEntry, setTimeEntry] = useState({
    hours: '',
    description: '',
    task_category: 'development',
    difficulty_level: 'medium'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [recentEntries, setRecentEntries] = useState([]);

  // Load user's projects and recent entries
  useEffect(() => {
    if (currentUser) {
      loadProjectsAndEntries();
    }
  }, [currentUser]);

  // Timer effect
  useEffect(() => {
    let interval = null;
    if (isTracking && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime);
      }, 1000);
    } else if (!isTracking) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [isTracking, startTime]);

  const loadProjectsAndEntries = async () => {
    try {
      setLoading(true);

      // Load user's projects
      const { data: userProjects, error: projectsError } = await supabase
        .from('project_members')
        .select(`
          project_id,
          role,
          projects (
            id,
            name,
            status
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('projects.status', 'active');

      if (projectsError) throw projectsError;

      const projectList = userProjects?.map(member => ({
        id: member.projects.id,
        name: member.projects.name,
        role: member.role
      })) || [];

      setProjects(projectList);

      // Load recent time entries
      const { data: entries, error: entriesError } = await supabase
        .from('contributions')
        .select(`
          id,
          hours_logged,
          description,
          task_category,
          difficulty_level,
          contribution_date,
          projects (
            id,
            name
          )
        `)
        .eq('user_id', currentUser.id)
        .order('contribution_date', { ascending: false })
        .limit(5);

      if (entriesError) throw entriesError;

      setRecentEntries(entries || []);

    } catch (error) {
      console.error('Error loading projects and entries:', error);
      toast.error('Failed to load time tracking data');
    } finally {
      setLoading(false);
    }
  };

  const startTimer = () => {
    setStartTime(Date.now());
    setElapsedTime(0);
    setIsTracking(true);
    toast.success('Timer started');
  };

  const pauseTimer = () => {
    setIsTracking(false);
    toast.success('Timer paused');
  };

  const stopTimer = () => {
    if (elapsedTime > 0) {
      const hours = elapsedTime / (1000 * 60 * 60);
      setTimeEntry(prev => ({
        ...prev,
        hours: Math.round(hours * 100) / 100
      }));
    }
    setIsTracking(false);
    setStartTime(null);
    setElapsedTime(0);
    toast.success('Timer stopped - time added to entry');
  };

  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleInputChange = (field, value) => {
    setTimeEntry(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const saveTimeEntry = async () => {
    if (!selectedProject || !timeEntry.hours || !timeEntry.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSaving(true);

      const contributionData = {
        user_id: currentUser.id,
        project_id: selectedProject,
        hours_logged: parseFloat(timeEntry.hours),
        description: timeEntry.description,
        task_category: timeEntry.task_category,
        difficulty_level: timeEntry.difficulty_level,
        contribution_date: new Date().toISOString(),
        validation_status: 'pending'
      };

      const { error } = await supabase
        .from('contributions')
        .insert([contributionData]);

      if (error) throw error;

      toast.success('Time entry saved successfully');
      
      // Reset form
      setTimeEntry({
        hours: '',
        description: '',
        task_category: 'development',
        difficulty_level: 'medium'
      });
      setSelectedProject('');

      // Reload recent entries
      loadProjectsAndEntries();

    } catch (error) {
      console.error('Error saving time entry:', error);
      toast.error('Failed to save time entry');
    } finally {
      setSaving(false);
    }
  };

  const getDifficultyColor = (level) => {
    switch (level) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'danger';
      default: return 'default';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'development': return 'primary';
      case 'design': return 'secondary';
      case 'testing': return 'warning';
      case 'documentation': return 'success';
      case 'meeting': return 'default';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading time tracker...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center gap-3"
      >
        <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
          <Clock size={20} className="text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Time Tracker</h2>
          <p className="text-white/60">Track your work time and log contributions</p>
        </div>
      </motion.div>

      {/* Timer Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <h3 className="text-lg font-semibold text-white">Active Timer</h3>
          </CardHeader>
          <CardBody>
            <div className="text-center space-y-6">
              <div className="text-6xl font-mono font-bold text-white">
                {formatTime(elapsedTime)}
              </div>
              
              <div className="flex justify-center gap-4">
                {!isTracking ? (
                  <Button
                    color="success"
                    size="lg"
                    startContent={<Play size={20} />}
                    onClick={startTimer}
                  >
                    Start Timer
                  </Button>
                ) : (
                  <>
                    <Button
                      color="warning"
                      size="lg"
                      startContent={<Pause size={20} />}
                      onClick={pauseTimer}
                    >
                      Pause
                    </Button>
                    <Button
                      color="danger"
                      size="lg"
                      startContent={<Square size={20} />}
                      onClick={stopTimer}
                    >
                      Stop
                    </Button>
                  </>
                )}
              </div>

              {isTracking && (
                <div className="text-center">
                  <Chip color="success" variant="flat">
                    Timer Running
                  </Chip>
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Time Entry Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <h3 className="text-lg font-semibold text-white">Log Time Entry</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Project"
                placeholder="Select a project"
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                isRequired
              >
                {projects.map(project => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </Select>

              <Input
                label="Hours"
                placeholder="0.0"
                type="number"
                step="0.25"
                min="0"
                max="24"
                value={timeEntry.hours}
                onChange={(e) => handleInputChange('hours', e.target.value)}
                isRequired
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Task Category"
                value={timeEntry.task_category}
                onChange={(e) => handleInputChange('task_category', e.target.value)}
              >
                <SelectItem key="development" value="development">Development</SelectItem>
                <SelectItem key="design" value="design">Design</SelectItem>
                <SelectItem key="testing" value="testing">Testing</SelectItem>
                <SelectItem key="documentation" value="documentation">Documentation</SelectItem>
                <SelectItem key="meeting" value="meeting">Meeting</SelectItem>
                <SelectItem key="research" value="research">Research</SelectItem>
                <SelectItem key="planning" value="planning">Planning</SelectItem>
              </Select>

              <Select
                label="Difficulty Level"
                value={timeEntry.difficulty_level}
                onChange={(e) => handleInputChange('difficulty_level', e.target.value)}
              >
                <SelectItem key="easy" value="easy">Easy</SelectItem>
                <SelectItem key="medium" value="medium">Medium</SelectItem>
                <SelectItem key="hard" value="hard">Hard</SelectItem>
              </Select>
            </div>

            <Textarea
              label="Description"
              placeholder="Describe what you worked on..."
              value={timeEntry.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              minRows={3}
              isRequired
            />

            <Button
              color="primary"
              size="lg"
              startContent={<Save size={20} />}
              onClick={saveTimeEntry}
              isLoading={saving}
              disabled={saving || !selectedProject || !timeEntry.hours || !timeEntry.description}
              className="w-full"
            >
              {saving ? 'Saving...' : 'Save Time Entry'}
            </Button>
          </CardBody>
        </Card>
      </motion.div>

      {/* Recent Entries */}
      {recentEntries.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Calendar size={20} className="text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Recent Entries</h3>
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {recentEntries.map((entry, index) => (
                  <div key={entry.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-white font-medium">{entry.projects?.name || 'Unknown Project'}</p>
                        <Chip 
                          size="sm" 
                          color={getCategoryColor(entry.task_category)}
                          variant="flat"
                        >
                          {entry.task_category}
                        </Chip>
                        <Chip 
                          size="sm" 
                          color={getDifficultyColor(entry.difficulty_level)}
                          variant="flat"
                        >
                          {entry.difficulty_level}
                        </Chip>
                      </div>
                      <p className="text-white/70 text-sm">{entry.description}</p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-white font-bold">{entry.hours_logged}h</p>
                      <p className="text-white/60 text-xs">
                        {new Date(entry.contribution_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default TimeTracker;
