// Automated Roadmap Update using Supabase API
// This script uses the service key to directly update the database schema and data

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load Supabase configuration
const supabaseConfig = require('./scripts/database/supabase-assistant-config.js');

// Initialize Supabase client with service key (has admin privileges)
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = supabaseConfig.supabaseKey;

if (!supabaseKey) {
  console.error('❌ Error: Supabase service key not found in configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to execute SQL commands
async function executeSql(sql, description) {
  try {
    console.log(`🔄 ${description}...`);
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      // If RPC doesn't exist, try direct query
      const { data: directData, error: directError } = await supabase
        .from('_sql_exec')
        .select('*')
        .eq('query', sql);
      
      if (directError) {
        console.error(`❌ Error executing SQL: ${directError.message}`);
        return false;
      }
    }
    
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Error in ${description}:`, error.message);
    return false;
  }
}

// Function to update schema
async function updateSchema() {
  console.log('🚀 Starting schema update...\n');
  
  const schemaUpdates = [
    {
      sql: `ALTER TABLE roadmap 
            ADD COLUMN IF NOT EXISTS platform_status JSONB,
            ADD COLUMN IF NOT EXISTS redesign_summary JSONB,
            ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();`,
      description: 'Adding new columns to roadmap table'
    },
    {
      sql: `ALTER TABLE roadmap 
            ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING updated_at::TIMESTAMPTZ;`,
      description: 'Updating updated_at column type'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_roadmap_platform_status 
            ON roadmap USING GIN (platform_status);`,
      description: 'Creating platform_status index'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_roadmap_redesign_summary 
            ON roadmap USING GIN (redesign_summary);`,
      description: 'Creating redesign_summary index'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_roadmap_last_updated 
            ON roadmap (last_updated DESC);`,
      description: 'Creating last_updated index'
    }
  ];
  
  for (const update of schemaUpdates) {
    const success = await executeSql(update.sql, update.description);
    if (!success) {
      console.log('⚠️  Continuing with next update...');
    }
  }
  
  return true;
}

// Function to check current schema
async function checkSchema() {
  try {
    console.log('🔍 Checking current roadmap table schema...\n');
    
    const { data, error } = await supabase
      .from('roadmap')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error checking schema:', error.message);
      return false;
    }
    
    if (data && data.length > 0) {
      const columns = Object.keys(data[0]);
      console.log('📋 Current columns:', columns.join(', '));
      
      // Check for our new columns
      const hasNewColumns = {
        platform_status: columns.includes('platform_status'),
        redesign_summary: columns.includes('redesign_summary'),
        last_updated: columns.includes('last_updated')
      };
      
      console.log('🔍 New columns status:');
      Object.entries(hasNewColumns).forEach(([col, exists]) => {
        console.log(`   ${col}: ${exists ? '✅ Exists' : '❌ Missing'}`);
      });
      
      return hasNewColumns;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
    return false;
  }
}

// Function to update roadmap data
async function updateRoadmapData() {
  try {
    console.log('\n📊 Updating roadmap data...');
    
    // Read the updated roadmap data
    const roadmapPath = path.join(__dirname, 'static-roadmap.json');
    const roadmapContent = fs.readFileSync(roadmapPath, 'utf8');
    const roadmapData = JSON.parse(roadmapContent);
    
    // Get current roadmap
    const { data: currentRoadmap, error: fetchError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (fetchError) {
      console.error('❌ Error fetching current roadmap:', fetchError.message);
      return false;
    }
    
    if (currentRoadmap && currentRoadmap.length > 0) {
      // Prepare update data
      const updateData = {
        data: roadmapData.data,
        updated_at: roadmapData.updated_at,
        last_updated: new Date().toISOString()
      };
      
      // Add latest_feature as JSON object (not string)
      updateData.latest_feature = {
        date: "2025-06-15T00:00:00.000Z",
        link: "/",
        title: "Total Platform Redesign Complete",
        author: "Development Team",
        version: "2.0.0",
        highlight: true,
        image_url: null,
        description: "Completed comprehensive platform redesign with modern UI/UX, enterprise-grade backend services, and production-ready infrastructure. Platform is now 95% complete with all major systems implemented. Current focus is on connecting existing backend functionality to frontend components."
      };
      
      // Add new columns if they exist
      if (roadmapData.platform_status) {
        updateData.platform_status = roadmapData.platform_status;
      }
      
      if (roadmapData.redesign_summary) {
        updateData.redesign_summary = roadmapData.redesign_summary;
      }
      
      // Update the roadmap
      const { data: updateResult, error: updateError } = await supabase
        .from('roadmap')
        .update(updateData)
        .eq('id', currentRoadmap[0].id)
        .select();
      
      if (updateError) {
        console.error('❌ Error updating roadmap:', updateError.message);
        return false;
      }
      
      console.log('✅ Roadmap data updated successfully');
      return true;
    } else {
      console.log('❌ No existing roadmap found');
      return false;
    }
  } catch (error) {
    console.error('❌ Error updating roadmap data:', error.message);
    return false;
  }
}

// Function to verify the update
async function verifyUpdate() {
  try {
    console.log('\n🔍 Verifying the update...');
    
    const { data, error } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (error) {
      console.error('❌ Error verifying update:', error.message);
      return false;
    }
    
    if (data && data.length > 0) {
      const roadmap = data[0];
      console.log('✅ Verification successful!');
      console.log(`📊 Roadmap contains ${roadmap.data.length} phases`);
      console.log(`🕒 Last updated: ${roadmap.updated_at}`);
      
      if (roadmap.latest_feature) {
        if (typeof roadmap.latest_feature === 'object') {
          console.log(`🎯 Latest feature: ${roadmap.latest_feature.title}`);
        } else {
          console.log(`🎯 Latest feature: ${roadmap.latest_feature}`);
        }
      }
      
      if (roadmap.platform_status) {
        console.log(`📈 Platform completion: ${roadmap.platform_status.overall_completion}`);
        console.log(`🔧 Current phase: ${roadmap.platform_status.current_phase}`);
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Error verifying update:', error.message);
    return false;
  }
}

// Main execution function
async function main() {
  try {
    console.log('🚀 AUTOMATED ROADMAP UPDATE');
    console.log('=' .repeat(50));
    
    // Step 1: Check current schema
    const schemaStatus = await checkSchema();
    
    // Step 2: Update schema if needed
    await updateSchema();
    
    // Step 3: Check schema again
    await checkSchema();
    
    // Step 4: Update roadmap data
    const dataSuccess = await updateRoadmapData();
    
    // Step 5: Verify the update
    if (dataSuccess) {
      await verifyUpdate();
    }
    
    console.log('\n🎉 Automated roadmap update completed!');
    console.log('🌐 The updated roadmap should now be live on the platform');
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  updateSchema,
  updateRoadmapData,
  verifyUpdate,
  checkSchema
};
