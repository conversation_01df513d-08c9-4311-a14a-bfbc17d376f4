<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Netlify Function Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    button:hover {
      background-color: #45a049;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
  </style>
</head>
<body>
  <h1>Netlify Function Test</h1>
  
  <div>
    <h2>Test Debug Function</h2>
    <button id="testDebug">Test Debug Function</button>
    <pre id="debugResult">Results will appear here...</pre>
  </div>
  
  <div>
    <h2>Test Test Function</h2>
    <button id="testTest">Test Test Function</button>
    <pre id="testResult">Results will appear here...</pre>
  </div>
  
  <div>
    <h2>Test Roadmap Function (GET)</h2>
    <button id="testRoadmapGet">Test Roadmap GET</button>
    <pre id="roadmapGetResult">Results will appear here...</pre>
  </div>
  
  <div>
    <h2>Test Roadmap Function (POST)</h2>
    <button id="testRoadmapPost">Test Roadmap POST</button>
    <pre id="roadmapPostResult">Results will appear here...</pre>
  </div>

  <script>
    // Helper function to make API calls
    async function callFunction(url, method = 'GET', data = null) {
      try {
        const options = {
          method,
          headers: {
            'Content-Type': 'application/json'
          }
        };
        
        if (data && method !== 'GET') {
          options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
          return {
            status: response.status,
            data: await response.json(),
            isJson: true
          };
        } else {
          return {
            status: response.status,
            data: await response.text(),
            isJson: false
          };
        }
      } catch (error) {
        return {
          status: 'Error',
          data: error.message,
          isJson: false
        };
      }
    }
    
    // Helper function to display results
    function displayResult(elementId, result) {
      const element = document.getElementById(elementId);
      
      if (result.isJson) {
        element.textContent = JSON.stringify(result.data, null, 2);
      } else {
        if (result.data.length > 1000) {
          element.textContent = result.data.substring(0, 1000) + '... (truncated)';
        } else {
          element.textContent = result.data;
        }
      }
      
      if (result.status === 200) {
        element.classList.add('success');
        element.classList.remove('error');
      } else {
        element.classList.add('error');
        element.classList.remove('success');
      }
    }
    
    // Test debug function
    document.getElementById('testDebug').addEventListener('click', async () => {
      const result = await callFunction('/.netlify/functions/debug');
      displayResult('debugResult', result);
    });
    
    // Test test function
    document.getElementById('testTest').addEventListener('click', async () => {
      const result = await callFunction('/.netlify/functions/test');
      displayResult('testResult', result);
    });
    
    // Test roadmap GET function
    document.getElementById('testRoadmapGet').addEventListener('click', async () => {
      const result = await callFunction('/.netlify/functions/roadmap');
      displayResult('roadmapGetResult', result);
    });
    
    // Test roadmap POST function
    document.getElementById('testRoadmapPost').addEventListener('click', async () => {
      // Sample data to post
      const sampleData = [
        {
          id: 1,
          title: "Test Phase",
          timeframe: "1 week",
          expanded: true,
          sections: [
            {
              id: "1.1",
              title: "Test Section",
              tasks: [
                { id: "1.1.1", text: "Test Task", completed: true }
              ]
            }
          ]
        }
      ];
      
      const result = await callFunction('/.netlify/functions/roadmap', 'POST', sampleData);
      displayResult('roadmapPostResult', result);
    });
  </script>
</body>
</html>
