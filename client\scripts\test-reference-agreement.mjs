/**
 * Test Reference Agreement Generator Script
 *
 * This script generates an agreement that should exactly match the example template
 * by providing all the necessary data to match the "Village of The Ages" example.
 *
 * Usage: node test-reference-agreement.mjs
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements/reference');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Reference project data that should match the example template exactly
const referenceProject = {
  name: "Village of The Ages",
  title: "Village of The Ages",
  description: "a village simulation game where players guide communities through historical progressions and manage resource-based challenges",
  project_type: "game",
  company_name: "City of Gamers Inc.",
  engine: "Unity",
  platforms: "PC, Mac, Mobile",
  estimated_duration: 4, // 4 months as in the example
  contributors: [
    {
      id: "owner-id",
      display_name: "Gynell Journigan",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "President",
      address: "1205 43rd Street, Suite B, Orlando, Florida 32839",
      state: "Florida",
      county: "Orange County"
    }
  ],
  royalty_model: {
    model_type: "custom",
    model_schema: "cog",
    configuration: {
      tasks_weight: 30,
      hours_weight: 30,
      difficulty_weight: 40
    },
    is_pre_expense: true,
    contributor_percentage: 50,
    min_payout: 100000,
    max_payout: 1000000
  },
  milestones: [
    {
      id: "milestone-1",
      title: "First Playable",
      description: "Core mechanics implemented",
      target_date: new Date("2023-01-31").toISOString(),
      deliverables: [
        "Core mechanics implemented",
        "Basic building and resource systems functional",
        "First era playable"
      ]
    },
    {
      id: "milestone-2",
      title: "Feature Complete",
      description: "All core features implemented",
      target_date: new Date("2023-03-31").toISOString(),
      deliverables: [
        "All core features implemented",
        "Multiple eras playable",
        "Core systems integrated"
      ]
    },
    {
      id: "milestone-3",
      title: "Release Candidate",
      description: "All features polished",
      target_date: new Date("2023-04-15").toISOString(),
      deliverables: [
        "All features polished",
        "Performance optimized",
        "Major bugs resolved"
      ]
    },
    {
      id: "milestone-4",
      title: "Launch",
      description: "Game ready for early access release",
      target_date: new Date("2023-04-30").toISOString(),
      deliverables: [
        "Game ready for early access release",
        "Store page and marketing assets complete",
        "Launch plan executed"
      ]
    }
  ]
};

// Options for the agreement generator
const referenceOptions = {
  contributors: referenceProject.contributors,
  currentUser: {
    email: "<EMAIL>",
    user_metadata: { full_name: "John Doe" },
    address: "123 Main St, Anytown, CA 12345"
  },
  royaltyModel: referenceProject.royalty_model,
  milestones: referenceProject.milestones,
  fullName: "John Doe"
};

// Run the test
console.log('🚀 Running reference agreement generation test...');
console.log(`📁 Output directory: ${outputDir}`);

try {
  console.log(`Generating reference agreement for: Village of The Ages`);
  
  // Generate the agreement
  const agreement = generator.generateAgreement(templateText, referenceProject, referenceOptions);
  
  // Save the agreement to a file
  const outputPath = path.join(outputDir, `reference-agreement.md`);
  fs.writeFileSync(outputPath, agreement, 'utf8');
  
  console.log(`✅ Reference agreement saved to: ${outputPath}`);
  
  // Also save a copy of the original template for comparison
  const templateCopyPath = path.join(outputDir, `original-template.md`);
  fs.writeFileSync(templateCopyPath, templateText, 'utf8');
  
  console.log(`✅ Original template saved to: ${templateCopyPath}`);
  
  console.log('✨ Reference test completed successfully!');
} catch (error) {
  console.error('❌ Error running reference test:', error);
}
