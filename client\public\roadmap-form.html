<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Roadmap Data Form</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    button, input[type="submit"] {
      padding: 8px 16px;
      background-color: #0066cc;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    textarea {
      width: 100%;
      height: 300px;
      font-family: monospace;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #ccc;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <h1>Roadmap Data Form</h1>
  
  <div>
    <p>This form allows you to save and retrieve roadmap data using Netlify Forms.</p>
    
    <h2>Save Roadmap Data</h2>
    <form name="roadmap-data" method="POST" data-netlify="true" netlify-honeypot="bot-field">
      <p class="hidden">
        <label>Don't fill this out if you're human: <input name="bot-field" /></label>
      </p>
      <input type="hidden" name="form-name" value="roadmap-data" />
      <p>
        <label for="roadmap-json">Roadmap Data (JSON):</label><br>
        <textarea name="roadmap-json" id="roadmap-json" placeholder="Paste your roadmap JSON data here"></textarea>
      </p>
      <p>
        <button type="submit">Save Roadmap Data</button>
      </p>
    </form>
    
    <h2>Load Current Data</h2>
    <button onclick="loadFromLocalStorage()">Load from localStorage</button>
    <pre id="current-data">No data loaded yet.</pre>
    
    <h2>Export/Import</h2>
    <button onclick="exportData()">Export Data</button>
    <button onclick="importData()">Import Data</button>
  </div>

  <script>
    // Load data from localStorage
    function loadFromLocalStorage() {
      const data = localStorage.getItem('royalteaRoadmapData');
      if (data) {
        document.getElementById('current-data').textContent = data;
        document.getElementById('roadmap-json').value = data;
      } else {
        document.getElementById('current-data').textContent = 'No data found in localStorage.';
      }
    }
    
    // Export data
    function exportData() {
      const data = localStorage.getItem('royalteaRoadmapData');
      if (!data) {
        alert('No data to export.');
        return;
      }
      
      const dataStr = 'data:text/json;charset=utf-8,' + encodeURIComponent(data);
      const downloadAnchorNode = document.createElement('a');
      downloadAnchorNode.setAttribute('href', dataStr);
      downloadAnchorNode.setAttribute('download', 'roadmap-data.json');
      document.body.appendChild(downloadAnchorNode);
      downloadAnchorNode.click();
      downloadAnchorNode.remove();
    }
    
    // Import data
    function importData() {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'application/json';
      
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (event) => {
          try {
            const data = event.target.result;
            // Validate that it's valid JSON
            JSON.parse(data);
            
            localStorage.setItem('royalteaRoadmapData', data);
            document.getElementById('current-data').textContent = data;
            document.getElementById('roadmap-json').value = data;
            
            alert('Data imported successfully!');
          } catch (error) {
            alert('Error importing data. Please check the file format.');
            console.error('Import error:', error);
          }
        };
        reader.readAsText(file);
      };
      
      input.click();
    }
    
    // Load data on page load
    document.addEventListener('DOMContentLoaded', loadFromLocalStorage);
  </script>
</body>
</html>
