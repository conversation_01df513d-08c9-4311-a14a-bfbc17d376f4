/**
 * Test Agreement Generator Script
 *
 * This script tests the agreement generation functionality by generating
 * agreements with different project configurations and saving them to files.
 *
 * Usage: node test-agreement-generator.js
 */

const fs = require('fs');
const path = require('path');
const { NewAgreementGenerator } = require('./agreement-generator-adapter');

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Test cases for different project types
const testCases = [
  // Test Case 1: Game Project with Custom Royalty Model
  {
    name: "game-project",
    title: "Game Project - Custom Royalty Model",
    project: {
      name: "Stellar Conquest",
      description: "A space strategy game where players build and manage interstellar empires",
      project_type: "game",
      estimated_duration: 12,
      launch_date: "2024-12-01",
      is_public: true,
      company_name: "Cosmic Games LLC",
      engine: "Unity",
      platforms: "PC, Mac, Mobile",
      royalty_model: {
        model_type: "custom",
        contributor_percentage: 40,
        min_payout: 50000,
        max_payout: 500000,
        configuration: {
          tasks_weight: 35,
          hours_weight: 25,
          difficulty_weight: 40
        }
      },
      revenue_tranches: [
        {
          name: "Initial Release",
          threshold: 100000,
          percentage: 30
        },
        {
          name: "Major Milestone",
          threshold: 500000,
          percentage: 40
        }
      ],
      milestones: [
        {
          name: "Core Gameplay",
          description: "Implement basic game mechanics and systems",
          deadline: "2024-06-01"
        },
        {
          name: "Alpha Release",
          description: "Release alpha version for testing",
          deadline: "2024-08-15"
        },
        {
          name: "Beta Release",
          description: "Release beta version with most features",
          deadline: "2024-10-01"
        },
        {
          name: "Full Release",
          description: "Launch the complete game",
          deadline: "2024-12-01"
        }
      ],
      contributors: [
        {
          id: "owner-id",
          display_name: "Jane Smith",
          email: "<EMAIL>",
          permission_level: "Owner",
          title: "CEO",
          address: "123 Galaxy Lane, San Francisco, CA 94107",
          state: "California",
          county: "San Francisco County"
        },
        {
          id: "contributor-id",
          display_name: "John Doe",
          email: "<EMAIL>",
          permission_level: "Contributor"
        }
      ]
    },
    options: {
      fullName: "John Doe",
      currentUser: {
        email: "<EMAIL>",
        user_metadata: { full_name: "John Doe" }
      }
    }
  },

  // Test Case 2: Music Project with Equal Split Royalty Model
  {
    name: "music-project",
    title: "Music Project - Equal Split Royalty Model",
    project: {
      name: "Harmonic Echoes",
      description: "A collaborative album featuring ambient electronic music",
      project_type: "music",
      estimated_duration: 6,
      launch_date: "2024-08-15",
      is_public: true,
      company_name: "Echo Studios",
      royalty_model: {
        model_type: "equal",
        contributor_percentage: 50
      },
      revenue_tranches: [
        {
          name: "Initial Release",
          threshold: 10000,
          percentage: 50
        }
      ],
      milestones: [
        {
          name: "Composition Phase",
          description: "Complete all track compositions",
          deadline: "2024-05-01"
        },
        {
          name: "Recording Phase",
          description: "Complete all recordings",
          deadline: "2024-06-15"
        },
        {
          name: "Mixing & Mastering",
          description: "Complete final mix and master",
          deadline: "2024-07-15"
        },
        {
          name: "Release",
          description: "Release album on streaming platforms",
          deadline: "2024-08-15"
        }
      ],
      contributors: [
        {
          id: "owner-id",
          display_name: "Alex Rivera",
          email: "<EMAIL>",
          permission_level: "Owner",
          title: "Producer",
          address: "456 Sound Ave, Nashville, TN 37203",
          state: "Tennessee",
          county: "Davidson County"
        }
      ]
    },
    options: {
      fullName: "Sarah Johnson",
      currentUser: {
        email: "<EMAIL>",
        user_metadata: { full_name: "Sarah Johnson" }
      }
    }
  },

  // Test Case 3: Software Project with Task-Based Royalty Model
  {
    name: "software-project",
    title: "Software Project - Task-Based Royalty Model",
    project: {
      name: "TaskFlow Pro",
      description: "A productivity app for team collaboration and project management",
      project_type: "software",
      estimated_duration: 9,
      launch_date: "2024-10-01",
      is_public: true,
      company_name: "Productive Solutions Inc.",
      engine: "React Native",
      platforms: "Web, iOS, Android",
      royalty_model: {
        model_type: "task",
        contributor_percentage: 35
      },
      revenue_tranches: [
        {
          name: "Initial Release",
          threshold: 75000,
          percentage: 25
        },
        {
          name: "Enterprise Adoption",
          threshold: 250000,
          percentage: 35
        }
      ],
      milestones: [
        {
          name: "MVP Development",
          description: "Develop minimum viable product",
          deadline: "2024-05-01"
        },
        {
          name: "Beta Testing",
          description: "Release beta version for testing",
          deadline: "2024-07-15"
        },
        {
          name: "Full Release",
          description: "Launch the complete application",
          deadline: "2024-10-01"
        }
      ],
      contributors: [
        {
          id: "owner-id",
          display_name: "Productive Solutions Inc.",
          email: "<EMAIL>",
          permission_level: "Owner",
          title: "Company",
          address: "789 Tech Blvd, Austin, TX 78701",
          state: "Texas",
          county: "Travis County",
          isCompany: true
        }
      ]
    },
    options: {
      fullName: "Michael Chen",
      currentUser: {
        email: "<EMAIL>",
        user_metadata: { full_name: "Michael Chen" }
      }
    }
  }
];

/**
 * Load the agreement template
 * @returns {Promise<string>} The agreement template text
 */
async function loadAgreementTemplate() {
  try {
    const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
    return fs.readFileSync(templatePath, 'utf8');
  } catch (error) {
    console.error('Error loading agreement template:', error);
    throw error;
  }
}

/**
 * Generate and save a test agreement
 * @param {Object} testCase - The test case data
 * @param {string} templateText - The agreement template text
 * @returns {Promise<void>}
 */
async function generateAndSaveAgreement(testCase, templateText) {
  try {
    console.log(`Generating agreement for: ${testCase.title}`);

    // Create a new agreement generator
    const generator = new NewAgreementGenerator();

    // Prepare options for the agreement generator
    const options = {
      contributors: testCase.project.contributors || [],
      currentUser: testCase.options.currentUser,
      royaltyModel: testCase.project.royalty_model,
      milestones: testCase.project.milestones || [],
      fullName: testCase.options.fullName
    };

    // Generate the agreement
    const agreement = generator.generateAgreement(templateText, testCase.project, options);

    // Save the agreement to a file
    const outputPath = path.join(outputDir, `${testCase.name}-agreement.md`);
    fs.writeFileSync(outputPath, agreement, 'utf8');

    console.log(`✅ Agreement saved to: ${outputPath}`);
  } catch (error) {
    console.error(`❌ Error generating agreement for ${testCase.title}:`, error);
  }
}

/**
 * Run all test cases
 * @returns {Promise<void>}
 */
async function runTests() {
  console.log('🚀 Running agreement generation tests...');
  console.log(`📁 Output directory: ${outputDir}`);

  try {
    // Load the agreement template
    const templateText = await loadAgreementTemplate();
    console.log('📄 Agreement template loaded successfully');

    // Generate agreements for all test cases
    for (const testCase of testCases) {
      await generateAndSaveAgreement(testCase, templateText);
    }

    console.log('✨ All tests completed successfully!');
  } catch (error) {
    console.error('❌ Error running tests:', error);
  }
}

// Run the tests
runTests();
