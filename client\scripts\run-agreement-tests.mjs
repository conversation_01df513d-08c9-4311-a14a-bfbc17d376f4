/**
 * Run Agreement Tests Script
 *
 * This script runs the agreement generation tests for different project types
 * and saves the generated agreements to files.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../src/utils/agreement/newAgreementGenerator.js';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../test-output/agreements');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Load the agreement template
const templatePath = path.join(__dirname, '../public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a new agreement generator
const generator = new NewAgreementGenerator();

// Test Case 1: Game Project with Custom Royalty Model
const gameProject = {
  name: "Stellar Conquest",
  description: "A space strategy game where players build and manage interstellar empires",
  project_type: "game",
  estimated_duration: 12,
  launch_date: "2024-12-01",
  is_public: true,
  company_name: "Cosmic Games LLC",
  engine: "Unity",
  platforms: "PC, Mac, Mobile",
  royalty_model: {
    model_type: "custom",
    contributor_percentage: 40,
    min_payout: 50000,
    max_payout: 500000,
    configuration: {
      tasks_weight: 35,
      hours_weight: 25,
      difficulty_weight: 40
    }
  },
  milestones: [
    {
      name: "Core Gameplay",
      description: "Implement basic game mechanics and systems",
      deadline: "2024-06-01"
    },
    {
      name: "Alpha Release",
      description: "Release alpha version for testing",
      deadline: "2024-08-15"
    },
    {
      name: "Beta Release",
      description: "Release beta version with most features",
      deadline: "2024-10-01"
    },
    {
      name: "Full Release",
      description: "Launch the complete game",
      deadline: "2024-12-01"
    }
  ],
  contributors: [
    {
      id: "owner-id",
      display_name: "Jane Smith",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "CEO",
      address: "123 Galaxy Lane, San Francisco, CA 94107",
      state: "California",
      county: "San Francisco County"
    }
  ]
};

const gameOptions = {
  contributors: gameProject.contributors,
  currentUser: {
    email: "<EMAIL>",
    user_metadata: { full_name: "John Doe" },
    address: "456 Gamer Lane, San Francisco, CA 94107"
  },
  royaltyModel: gameProject.royalty_model,
  milestones: gameProject.milestones,
  fullName: "John Doe"
};

// Test Case 2: Music Project with Equal Split Royalty Model
const musicProject = {
  name: "Harmonic Echoes",
  description: "A collaborative album featuring ambient electronic music",
  project_type: "music",
  estimated_duration: 6,
  launch_date: "2024-08-15",
  is_public: true,
  company_name: "Echo Studios",
  platforms: "Streaming Services (Spotify, Apple Music, etc.)",
  royalty_model: {
    model_type: "equal",
    contributor_percentage: 50
  },
  milestones: [
    {
      name: "Composition Phase",
      description: "Complete all track compositions",
      deadline: "2024-05-01"
    },
    {
      name: "Recording Phase",
      description: "Complete all recordings",
      deadline: "2024-06-15"
    },
    {
      name: "Mixing & Mastering",
      description: "Complete final mix and master",
      deadline: "2024-07-15"
    },
    {
      name: "Release",
      description: "Release album on streaming platforms",
      deadline: "2024-08-15"
    }
  ],
  contributors: [
    {
      id: "owner-id",
      display_name: "Alex Rivera",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "Producer",
      address: "456 Sound Ave, Nashville, TN 37203",
      state: "Tennessee",
      county: "Davidson County"
    }
  ]
};

const musicOptions = {
  contributors: musicProject.contributors,
  currentUser: {
    email: "<EMAIL>",
    user_metadata: { full_name: "Sarah Johnson" },
    address: "789 Music Ave, Nashville, TN 37203"
  },
  royaltyModel: musicProject.royalty_model,
  milestones: musicProject.milestones,
  fullName: "Sarah Johnson"
};

// Test Case 3: Software Project with Task-Based Royalty Model
const softwareProject = {
  name: "TaskFlow Pro",
  description: "A productivity app for team collaboration and project management",
  project_type: "software",
  estimated_duration: 9,
  launch_date: "2024-10-01",
  is_public: true,
  company_name: "Productive Solutions Inc.",
  engine: "React Native",
  platforms: "Web, iOS, Android",
  royalty_model: {
    model_type: "task",
    contributor_percentage: 35
  },
  milestones: [
    {
      name: "MVP Development",
      description: "Develop minimum viable product",
      deadline: "2024-05-01"
    },
    {
      name: "Beta Testing",
      description: "Release beta version for testing",
      deadline: "2024-07-15"
    },
    {
      name: "Full Release",
      description: "Launch the complete application",
      deadline: "2024-10-01"
    }
  ],
  contributors: [
    {
      id: "owner-id",
      display_name: "Productive Solutions Inc.",
      email: "<EMAIL>",
      permission_level: "Owner",
      title: "Company",
      address: "789 Tech Blvd, Austin, TX 78701",
      state: "Texas",
      county: "Travis County",
      isCompany: true
    }
  ]
};

const softwareOptions = {
  contributors: softwareProject.contributors,
  currentUser: {
    email: "<EMAIL>",
    user_metadata: { full_name: "Michael Chen" },
    address: "123 Tech Blvd, Austin, TX 78701"
  },
  royaltyModel: softwareProject.royalty_model,
  milestones: softwareProject.milestones,
  fullName: "Michael Chen"
};

// Run the tests
console.log('🚀 Running agreement generation tests...');
console.log(`📁 Output directory: ${outputDir}`);

try {
  // Generate game project agreement
  console.log('Generating agreement for: Game Project - Custom Royalty Model');
  // Fix the project type to ensure it's properly processed
  gameProject.projectType = gameProject.project_type;
  const gameAgreement = generator.generateAgreement(templateText, gameProject, gameOptions);
  const gameOutputPath = path.join(outputDir, 'game-project-agreement.md');
  fs.writeFileSync(gameOutputPath, gameAgreement, 'utf8');
  console.log(`✅ Agreement saved to: ${gameOutputPath}`);

  // Generate music project agreement
  console.log('Generating agreement for: Music Project - Equal Split Royalty Model');
  // Fix the project type to ensure it's properly processed
  musicProject.projectType = musicProject.project_type;
  const musicAgreement = generator.generateAgreement(templateText, musicProject, musicOptions);
  const musicOutputPath = path.join(outputDir, 'music-project-agreement.md');
  fs.writeFileSync(musicOutputPath, musicAgreement, 'utf8');
  console.log(`✅ Agreement saved to: ${musicOutputPath}`);

  // Generate software project agreement
  console.log('Generating agreement for: Software Project - Task-Based Royalty Model');
  // Fix the project type to ensure it's properly processed
  softwareProject.projectType = softwareProject.project_type;
  const softwareAgreement = generator.generateAgreement(templateText, softwareProject, softwareOptions);
  const softwareOutputPath = path.join(outputDir, 'software-project-agreement.md');
  fs.writeFileSync(softwareOutputPath, softwareAgreement, 'utf8');
  console.log(`✅ Agreement saved to: ${softwareOutputPath}`);

  console.log('✨ All tests completed successfully!');
} catch (error) {
  console.error('❌ Error running tests:', error);
}
